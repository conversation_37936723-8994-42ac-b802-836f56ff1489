#!/usr/bin/env python3
"""
图记忆功能健康检查工具
Graph Memory Health Check Tool

检查Mem0项目中图记忆功能的完整性和可用性
"""

import sys
import os
import traceback
from typing import Dict, List, Tuple

# 添加项目路径
sys.path.insert(0, '/opt/mem0ai')

class GraphMemoryHealthChecker:
    def __init__(self):
        self.results = []
        self.errors = []
        
    def log_result(self, check_name: str, success: bool, message: str, details: str = ""):
        """记录检查结果"""
        status = "✓" if success else "✗"
        self.results.append({
            'check': check_name,
            'success': success,
            'message': message,
            'details': details
        })
        print(f"{status} {check_name}: {message}")
        if details and not success:
            print(f"   详情: {details}")
    
    def check_basic_imports(self):
        """检查基础导入"""
        try:
            from mem0 import Memory
            self.log_result("基础导入", True, "Mem0 Memory类导入成功")
        except Exception as e:
            self.log_result("基础导入", False, "Mem0 Memory导入失败", str(e))
    
    def check_graph_dependencies(self):
        """检查图记忆依赖"""
        dependencies = [
            ('langchain_neo4j', 'Neo4jGraph'),
            ('rank_bm25', 'BM25Okapi'),
        ]
        
        for module_name, class_name in dependencies:
            try:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                self.log_result(f"依赖检查 {module_name}", True, f"{module_name}.{class_name} 可用")
            except ImportError as e:
                self.log_result(f"依赖检查 {module_name}", False, f"{module_name} 导入失败", str(e))
            except AttributeError as e:
                self.log_result(f"依赖检查 {module_name}", False, f"{class_name} 类不存在", str(e))
    
    def check_graph_memory_classes(self):
        """检查图记忆核心类"""
        try:
            from mem0.memory.graph_memory import MemoryGraph
            self.log_result("图记忆核心类", True, "MemoryGraph类导入成功")
        except Exception as e:
            self.log_result("图记忆核心类", False, "MemoryGraph导入失败", str(e))
        
        try:
            from mem0.graphs.configs import GraphStoreConfig, Neo4jConfig, MemgraphConfig, NeptuneConfig
            self.log_result("图配置类", True, "所有图配置类导入成功")
        except Exception as e:
            self.log_result("图配置类", False, "图配置类导入失败", str(e))
    
    def check_graph_tools(self):
        """检查图记忆工具"""
        try:
            from mem0.graphs.tools import (
                EXTRACT_ENTITIES_TOOL,
                RELATIONS_TOOL,
                DELETE_MEMORY_TOOL_GRAPH,
                EXTRACT_ENTITIES_STRUCT_TOOL,
                RELATIONS_STRUCT_TOOL,
                DELETE_MEMORY_STRUCT_TOOL_GRAPH
            )
            self.log_result("图记忆工具", True, "所有LLM工具定义正常")
        except Exception as e:
            self.log_result("图记忆工具", False, "图记忆工具导入失败", str(e))
    
    def check_graph_configuration_validation(self):
        """检查图配置验证"""
        try:
            from mem0.graphs.configs import Neo4jConfig, GraphStoreConfig
            
            # 测试Neo4j配置验证
            neo4j_config = Neo4jConfig(
                url='bolt://localhost:7687',
                username='neo4j',
                password='test123'
            )
            
            graph_config = GraphStoreConfig(
                provider='neo4j',
                config=neo4j_config
            )
            
            self.log_result("配置验证", True, "图配置验证通过")
        except Exception as e:
            self.log_result("配置验证", False, "图配置验证失败", str(e))
    
    def check_neo4j_connectivity(self):
        """检查Neo4j连接性"""
        try:
            from neo4j import GraphDatabase
            
            # 尝试连接到本地Neo4j实例
            driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', 'mem0graph'))
            
            with driver.session() as session:
                result = session.run('RETURN 1 as test')
                record = result.single()
                if record and record['test'] == 1:
                    self.log_result("Neo4j连接", True, "Neo4j数据库连接成功")
                else:
                    self.log_result("Neo4j连接", False, "Neo4j连接异常响应")
            driver.close()
            
        except Exception as e:
            self.log_result("Neo4j连接", False, "Neo4j连接失败（可能未运行）", str(e))
    
    def check_typescript_support(self):
        """检查TypeScript支持"""
        ts_graph_file = '/opt/mem0ai/mem0-ts/src/oss/src/memory/graph_memory.ts'
        
        if os.path.exists(ts_graph_file):
            try:
                with open(ts_graph_file, 'r') as f:
                    content = f.read()
                    if 'export' in content and 'GraphMemory' in content:
                        self.log_result("TypeScript支持", True, "TypeScript图记忆模块存在且导出正常")
                    else:
                        self.log_result("TypeScript支持", False, "TypeScript模块结构异常")
            except Exception as e:
                self.log_result("TypeScript支持", False, "TypeScript文件读取失败", str(e))
        else:
            self.log_result("TypeScript支持", False, "TypeScript图记忆文件不存在")
    
    def check_ui_components(self):
        """检查UI组件"""
        ui_components = [
            '/opt/mem0ai/mem0_ui/components/graph/GraphVisualization.tsx',
            '/opt/mem0ai/mem0_ui/components/graph/GraphNode.tsx',
            '/opt/mem0ai/mem0_ui/components/graph/GraphEdge.tsx',
            '/opt/mem0ai/mem0_ui/store/graphMemorySlice.ts'
        ]
        
        existing_components = []
        missing_components = []
        
        for component in ui_components:
            if os.path.exists(component):
                existing_components.append(os.path.basename(component))
            else:
                missing_components.append(os.path.basename(component))
        
        if missing_components:
            self.log_result("UI组件", False, f"部分UI组件缺失: {', '.join(missing_components)}")
        else:
            self.log_result("UI组件", True, f"所有UI组件存在: {', '.join(existing_components)}")
    
    def check_documentation_consistency(self):
        """检查文档一致性"""
        docs = [
            '/opt/mem0ai/docs/platform/features/graph-memory.mdx',
            '/opt/mem0ai/docs/open-source/graph_memory/overview.mdx',
            '/opt/mem0ai/docs/open-source/graph_memory/features.mdx'
        ]
        
        existing_docs = []
        missing_docs = []
        
        for doc in docs:
            if os.path.exists(doc):
                existing_docs.append(os.path.basename(doc))
            else:
                missing_docs.append(os.path.basename(doc))
        
        if missing_docs:
            self.log_result("文档完整性", False, f"部分文档缺失: {', '.join(missing_docs)}")
        else:
            self.log_result("文档完整性", True, f"所有文档存在: {', '.join(existing_docs)}")
    
    def check_neptune_support(self):
        """检查Neptune支持"""
        try:
            from langchain_aws import NeptuneAnalyticsGraph
            from mem0.graphs.neptune.main import MemoryGraph as NeptuneMemoryGraph
            self.log_result("Neptune支持", True, "Neptune Analytics支持完整")
        except ImportError as e:
            if "langchain_aws" in str(e):
                self.log_result("Neptune支持", False, "Neptune依赖缺失", "langchain_aws未安装")
            else:
                self.log_result("Neptune支持", False, "Neptune导入失败", str(e))
        except Exception as e:
            self.log_result("Neptune支持", False, "Neptune检查异常", str(e))
    
    def run_comprehensive_check(self):
        """运行综合检查"""
        print("=" * 60)
        print("Mem0 图记忆功能健康检查")
        print("=" * 60)
        
        # 运行所有检查
        self.check_basic_imports()
        self.check_graph_dependencies()
        self.check_graph_memory_classes()
        self.check_graph_tools()
        self.check_graph_configuration_validation()
        self.check_neo4j_connectivity()
        self.check_typescript_support()
        self.check_ui_components()
        self.check_documentation_consistency()
        self.check_neptune_support()
        
        # 统计结果
        successful_checks = sum(1 for r in self.results if r['success'])
        total_checks = len(self.results)
        
        print("\n" + "=" * 60)
        print(f"检查完成: {successful_checks}/{total_checks} 项通过")
        print("=" * 60)
        
        # 显示失败的检查
        failed_checks = [r for r in self.results if not r['success']]
        if failed_checks:
            print("\n⚠️  需要注意的问题:")
            for check in failed_checks:
                print(f"   • {check['check']}: {check['message']}")
                if check['details']:
                    print(f"     详情: {check['details']}")
        
        # 总体评估
        if successful_checks == total_checks:
            print("\n🎉 图记忆功能完全正常！")
        elif successful_checks >= total_checks * 0.8:
            print("\n✅ 图记忆功能基本正常，有少量非关键问题")
        elif successful_checks >= total_checks * 0.6:
            print("\n⚠️  图记忆功能部分正常，存在一些问题需要关注")
        else:
            print("\n❌ 图记忆功能存在严重问题，需要修复")
        
        return successful_checks, total_checks, self.results

if __name__ == "__main__":
    checker = GraphMemoryHealthChecker()
    successful, total, results = checker.run_comprehensive_check()
    
    # 返回适当的退出码
    if successful == total:
        sys.exit(0)  # 完全正常
    elif successful >= total * 0.8:
        sys.exit(1)  # 基本正常但有小问题
    else:
        sys.exit(2)  # 有严重问题