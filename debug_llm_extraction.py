#!/usr/bin/env python3
"""
直接测试LLM的事实提取响应
"""

import requests
import json

def test_direct_llm():
    print("🔍 直接测试LLM事实提取")
    print("=" * 50)
    
    # 准备测试数据 - 这应该产生明确的事实
    test_content = "Hi, my name is <PERSON> and I work as a data scientist at Microsoft. I love playing tennis and I'm planning a vacation to Hawaii next summer."
    
    # 构建请求以直接测试记忆创建的内部过程
    # 通过更详细的调试来理解为什么返回空数组
    
    user_id = "debug_test_user"
    
    print(f"测试内容: {test_content}")
    print(f"用户ID: {user_id}")
    
    # 发送请求
    response = requests.post(
        "http://localhost:8000/v1/memories/",
        json={
            "messages": [{"role": "user", "content": test_content}],
            "user_id": user_id
        },
        timeout=30
    )
    
    print(f"\n响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"创建的记忆数量: {len(result)}")
        
        if result:
            print("创建的记忆详情:")
            for item in result:
                print(f"  - {json.dumps(item, indent=2, ensure_ascii=False)}")
        else:
            print("返回空数组 - 可能原因:")
            print("  1. LLM认为没有需要存储的新事实")
            print("  2. 事实提取过程返回了空列表")
            print("  3. 记忆更新过程判定为NONE操作")
        
        # 检查用户是否有任何记忆被创建
        import time
        time.sleep(1)
        
        get_response = requests.get(
            f"http://localhost:8000/v1/memories/?user_id={user_id}&limit=10"
        )
        
        if get_response.status_code == 200:
            memories = get_response.json()
            print(f"\n用户 {user_id} 的总记忆数: {len(memories)}")
            
            if memories:
                print("用户记忆详情:")
                for mem in memories:
                    print(f"  记忆: {mem.get('memory', '')}")
                    print(f"  Categories: {mem.get('metadata', {}).get('categories', [])}")
                    print()
        else:
            print(f"获取用户记忆失败: {get_response.status_code}")
    else:
        print(f"请求失败: {response.text}")

if __name__ == "__main__":
    test_direct_llm()