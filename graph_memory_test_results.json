{"summary": {"total_tests": 41, "passed": 18, "failed": 23, "warnings": 0, "total_time": 97.85169339179993, "success_rate": 43.90243902439025}, "detailed_results": [{"test_name": "API Connectivity", "status": "PASS", "details": "API is accessible and responding", "execution_time": 0.0057239532470703125, "timestamp": "2025-07-31T11:56:46.781725"}, {"test_name": "KeyError Fix (No user_id)", "status": "PASS", "details": "Graph stats API works without user_id parameter", "execution_time": 0.0030641555786132812, "timestamp": "2025-07-31T11:56:46.785253"}, {"test_name": "KeyError Fix (With user_id)", "status": "PASS", "details": "Graph stats API works with user_id parameter", "execution_time": 0.01180577278137207, "timestamp": "2025-07-31T11:56:46.797274"}, {"test_name": "Delete All Memories (test_user_alice)", "status": "PASS", "details": "Successfully deleted all memories", "execution_time": 0.1567373275756836, "timestamp": "2025-07-31T11:56:46.954312"}, {"test_name": "Delete All Memories (test_user_bob)", "status": "PASS", "details": "Successfully deleted all memories", "execution_time": 0.01093602180480957, "timestamp": "2025-07-31T11:56:46.965524"}, {"test_name": "Add Memory (test_user_alice)", "status": "FAIL", "details": "Failed to add memory: [{'id': 'c8cfb8e3-0cc9-4339-96e9-0989f57919d4', 'memory': 'Name is <PERSON>', 'event': 'ADD'}, {'id': '7567a943-c776-417e-abb3-920cb9abcc1b', 'memory': 'Is a software engineer', 'event': 'ADD'}]", "execution_time": 8.244609117507935, "timestamp": "2025-07-31T11:56:55.210378"}, {"test_name": "Add Memory (test_user_alice)", "status": "FAIL", "details": "Failed to add memory: [{'id': 'fef12cca-de7a-444d-8ba3-4aac5c63bc0c', 'memory': 'Lives in Seattle', 'event': 'ADD'}, {'id': '5a1ee091-49e8-4722-8664-42dcdf0775c1', 'memory': 'Loves hiking', 'event': 'ADD'}]", "execution_time": 7.227466106414795, "timestamp": "2025-07-31T11:57:02.438118"}, {"test_name": "Add Memory (test_user_alice)", "status": "FAIL", "details": "Failed to add memory: [{'id': 'fb1ca7d0-0194-4e60-990e-d068bb8ccb54', 'memory': 'Favorite food is pizza', 'event': 'ADD'}]", "execution_time": 23.40537714958191, "timestamp": "2025-07-31T11:57:25.843763"}, {"test_name": "Add Memory (test_user_bob)", "status": "FAIL", "details": "Failed to add memory: [{'id': '727351fc-d6e4-4bd2-83b7-9da8313fd250', 'memory': 'Name is <PERSON>', 'event': 'ADD'}, {'id': '21167350-efd4-4f84-adff-92f62145456c', 'memory': 'Works as a designer', 'event': 'ADD'}]", "execution_time": 7.554927110671997, "timestamp": "2025-07-31T11:57:33.399000"}, {"test_name": "Add Memory (test_user_bob)", "status": "FAIL", "details": "Failed to add memory: [{'id': '9a1ace26-e654-4fc3-9b98-f055c5b6d9fe', 'memory': 'Lives in Portland', 'event': 'ADD'}, {'id': '96a4606e-e2f1-40ce-9554-b0b383cac7c2', 'memory': 'Enjoys cycling', 'event': 'ADD'}]", "execution_time": 5.55714225769043, "timestamp": "2025-07-31T11:57:38.956410"}, {"test_name": "Search Memory (test_user_alice)", "status": "FAIL", "details": "Search failed: {'error': '500 Server Error: Internal Server Error for url: http://localhost:8000/v1/memories/search/?query=What+is+my+name%3F&user_id=test_user_alice'}", "execution_time": 0.014464139938354492, "timestamp": "2025-07-31T11:57:40.972462"}, {"test_name": "Search Memory (test_user_alice)", "status": "FAIL", "details": "Search failed: {'error': '500 Server Error: Internal Server Error for url: http://localhost:8000/v1/memories/search/?query=Where+do+I+live%3F&user_id=test_user_alice'}", "execution_time": 0.007892847061157227, "timestamp": "2025-07-31T11:57:40.980566"}, {"test_name": "Search Memory (test_user_bob)", "status": "FAIL", "details": "Search failed: {'error': '500 Server Error: Internal Server Error for url: http://localhost:8000/v1/memories/search/?query=What+is+my+name%3F&user_id=test_user_bob'}", "execution_time": 0.008191108703613281, "timestamp": "2025-07-31T11:57:40.988950"}, {"test_name": "Get All Memories (test_user_alice)", "status": "FAIL", "details": "Get all failed: [{'id': '5a1ee091-49e8-4722-8664-42dcdf0775c1', 'memory': 'Loves hiking', 'hash': 'a2b0ac7e56c0f757a1fc96c136a6c256', 'metadata': {'api_version': 'v1', 'categories': ['travel', 'hobbies']}, 'created_at': '2025-07-31T03:56:59.137687+00:00', 'updated_at': None, 'user_id': 'test_user_alice'}, {'id': '7567a943-c776-417e-abb3-920cb9abcc1b', 'memory': 'Is a software engineer', 'hash': '8db92c7668b54156c2f9cd2f7e318032', 'metadata': {'api_version': 'v1', 'categories': ['personal_details', 'professional_details']}, 'created_at': '2025-07-31T03:56:50.942305+00:00', 'updated_at': None, 'user_id': 'test_user_alice'}, {'id': 'c8cfb8e3-0cc9-4339-96e9-0989f57919d4', 'memory': 'Name is Alice', 'hash': '6be668ba991c1e5e20a76c7bb1ac49aa', 'metadata': {'api_version': 'v1', 'categories': ['personal_details', 'professional_details']}, 'created_at': '2025-07-31T03:56:50.885785+00:00', 'updated_at': None, 'user_id': 'test_user_alice'}, {'id': 'fb1ca7d0-0194-4e60-990e-d068bb8ccb54', 'memory': 'Favorite food is pizza', 'hash': 'cd9ea4a49400b3bc28effcdaf3c01a8b', 'metadata': {'api_version': 'v1', 'categories': ['food']}, 'created_at': '2025-07-31T03:57:25.826709+00:00', 'updated_at': None, 'user_id': 'test_user_alice'}, {'id': 'fef12cca-de7a-444d-8ba3-4aac5c63bc0c', 'memory': 'Lives in Seattle', 'hash': '061b2755a04a3723613d44c398b551c5', 'metadata': {'api_version': 'v1', 'categories': ['travel', 'hobbies']}, 'created_at': '2025-07-31T03:56:59.119949+00:00', 'updated_at': None, 'user_id': 'test_user_alice'}]", "execution_time": 0.04956817626953125, "timestamp": "2025-07-31T11:57:41.038756"}, {"test_name": "Get All Memories (test_user_bob)", "status": "FAIL", "details": "Get all failed: [{'id': '21167350-efd4-4f84-adff-92f62145456c', 'memory': 'Works as a designer', 'hash': 'e58006e860ce259575e9494289533321', 'metadata': {'api_version': 'v1', 'categories': ['personal_details', 'professional_details']}, 'created_at': '2025-07-31T03:57:29.325952+00:00', 'updated_at': None, 'user_id': 'test_user_bob'}, {'id': '727351fc-d6e4-4bd2-83b7-9da8313fd250', 'memory': 'Name is Bob', 'hash': '2c6f48df7e8d4ea366914773ca57b8b4', 'metadata': {'api_version': 'v1', 'categories': ['personal_details', 'professional_details']}, 'created_at': '2025-07-31T03:57:29.312311+00:00', 'updated_at': None, 'user_id': 'test_user_bob'}, {'id': '96a4606e-e2f1-40ce-9554-b0b383cac7c2', 'memory': 'Enjoys cycling', 'hash': 'c95e402f67be9c1cc0b6bfc397e9206b', 'metadata': {'api_version': 'v1', 'categories': ['user_preferences', 'hobbies']}, 'created_at': '2025-07-31T03:57:37.319440+00:00', 'updated_at': None, 'user_id': 'test_user_bob'}, {'id': '9a1ace26-e654-4fc3-9b98-f055c5b6d9fe', 'memory': 'Lives in Portland', 'hash': 'bb6fb221bedf260056e590469e4c271d', 'metadata': {'api_version': 'v1', 'categories': ['user_preferences', 'hobbies']}, 'created_at': '2025-07-31T03:57:37.306501+00:00', 'updated_at': None, 'user_id': 'test_user_bob'}]", "execution_time": 0.049870967864990234, "timestamp": "2025-07-31T11:57:41.088887"}, {"test_name": "Get All Memories (test_user_alice)", "status": "FAIL", "details": "Get all failed: [{'id': '5a1ee091-49e8-4722-8664-42dcdf0775c1', 'memory': 'Loves hiking', 'hash': 'a2b0ac7e56c0f757a1fc96c136a6c256', 'metadata': {'api_version': 'v1', 'categories': ['travel', 'hobbies']}, 'created_at': '2025-07-31T03:56:59.137687+00:00', 'updated_at': None, 'user_id': 'test_user_alice'}, {'id': '7567a943-c776-417e-abb3-920cb9abcc1b', 'memory': 'Is a software engineer', 'hash': '8db92c7668b54156c2f9cd2f7e318032', 'metadata': {'api_version': 'v1', 'categories': ['personal_details', 'professional_details']}, 'created_at': '2025-07-31T03:56:50.942305+00:00', 'updated_at': None, 'user_id': 'test_user_alice'}, {'id': 'c8cfb8e3-0cc9-4339-96e9-0989f57919d4', 'memory': 'Name is Alice', 'hash': '6be668ba991c1e5e20a76c7bb1ac49aa', 'metadata': {'api_version': 'v1', 'categories': ['personal_details', 'professional_details']}, 'created_at': '2025-07-31T03:56:50.885785+00:00', 'updated_at': None, 'user_id': 'test_user_alice'}, {'id': 'fb1ca7d0-0194-4e60-990e-d068bb8ccb54', 'memory': 'Favorite food is pizza', 'hash': 'cd9ea4a49400b3bc28effcdaf3c01a8b', 'metadata': {'api_version': 'v1', 'categories': ['food']}, 'created_at': '2025-07-31T03:57:25.826709+00:00', 'updated_at': None, 'user_id': 'test_user_alice'}, {'id': 'fef12cca-de7a-444d-8ba3-4aac5c63bc0c', 'memory': 'Lives in Seattle', 'hash': '061b2755a04a3723613d44c398b551c5', 'metadata': {'api_version': 'v1', 'categories': ['travel', 'hobbies']}, 'created_at': '2025-07-31T03:56:59.119949+00:00', 'updated_at': None, 'user_id': 'test_user_alice'}]", "execution_time": 0.049515485763549805, "timestamp": "2025-07-31T11:57:41.138765"}, {"test_name": "Get All Memories (test_user_bob)", "status": "FAIL", "details": "Get all failed: [{'id': '21167350-efd4-4f84-adff-92f62145456c', 'memory': 'Works as a designer', 'hash': 'e58006e860ce259575e9494289533321', 'metadata': {'api_version': 'v1', 'categories': ['personal_details', 'professional_details']}, 'created_at': '2025-07-31T03:57:29.325952+00:00', 'updated_at': None, 'user_id': 'test_user_bob'}, {'id': '727351fc-d6e4-4bd2-83b7-9da8313fd250', 'memory': 'Name is Bob', 'hash': '2c6f48df7e8d4ea366914773ca57b8b4', 'metadata': {'api_version': 'v1', 'categories': ['personal_details', 'professional_details']}, 'created_at': '2025-07-31T03:57:29.312311+00:00', 'updated_at': None, 'user_id': 'test_user_bob'}, {'id': '96a4606e-e2f1-40ce-9554-b0b383cac7c2', 'memory': 'Enjoys cycling', 'hash': 'c95e402f67be9c1cc0b6bfc397e9206b', 'metadata': {'api_version': 'v1', 'categories': ['user_preferences', 'hobbies']}, 'created_at': '2025-07-31T03:57:37.319440+00:00', 'updated_at': None, 'user_id': 'test_user_bob'}, {'id': '9a1ace26-e654-4fc3-9b98-f055c5b6d9fe', 'memory': 'Lives in Portland', 'hash': 'bb6fb221bedf260056e590469e4c271d', 'metadata': {'api_version': 'v1', 'categories': ['user_preferences', 'hobbies']}, 'created_at': '2025-07-31T03:57:37.306501+00:00', 'updated_at': None, 'user_id': 'test_user_bob'}]", "execution_time": 0.05033373832702637, "timestamp": "2025-07-31T11:57:41.189379"}, {"test_name": "Add Memory (test_user_charlie, food_agent)", "status": "FAIL", "details": "Failed to add memory: [{'id': 'ef662fbb-8b99-4f26-9a30-bad30ba84163', 'memory': 'Likes Italian food', 'event': 'ADD'}]", "execution_time": 4.736912965774536, "timestamp": "2025-07-31T11:57:45.926613"}, {"test_name": "Add Memory (test_user_charlie, health_agent)", "status": "FAIL", "details": "Failed to add memory: [{'id': '3ccf45e3-acab-456d-ac79-97abf3d2fb88', 'memory': 'Allergic to peanuts', 'event': 'ADD'}]", "execution_time": 4.785061359405518, "timestamp": "2025-07-31T11:57:50.711991"}, {"test_name": "Add Memory (test_user_charlie)", "status": "FAIL", "details": "Failed to add memory: [{'id': '4f937238-c75c-4c77-b867-ab1bc5d9ca31', 'memory': 'Likes Italian food', 'event': 'ADD'}, {'id': 'c94a2e78-d41a-4e4e-8671-8db857b7650e', 'memory': 'Allergic to peanuts', 'event': 'ADD'}, {'id': '3c9ae4b4-8613-4e3f-857b-bd3f28fe8a0e', 'memory': 'Lives in New York', 'event': 'ADD'}]", "execution_time": 5.0821497440338135, "timestamp": "2025-07-31T11:57:55.794391"}, {"test_name": "Get All Memories (test_user_charlie, food_agent)", "status": "FAIL", "details": "Get all failed: [{'id': 'ef662fbb-8b99-4f26-9a30-bad30ba84163', 'memory': 'Likes Italian food', 'hash': '21faf1728d98625c307cc15e1db55363', 'metadata': {'api_version': 'v1', 'categories': ['food']}, 'created_at': '2025-07-31T03:57:43.963583+00:00', 'updated_at': None, 'user_id': 'test_user_charlie', 'agent_id': 'food_agent'}]", "execution_time": 0.05408310890197754, "timestamp": "2025-07-31T11:57:57.850770"}, {"test_name": "Get All Memories (test_user_charlie, health_agent)", "status": "FAIL", "details": "Get all failed: [{'id': '3ccf45e3-acab-456d-ac79-97abf3d2fb88', 'memory': 'Allergic to peanuts', 'hash': 'bfbe51c2a12e06aa942e95e2ec36526a', 'metadata': {'api_version': 'v1', 'categories': ['health']}, 'created_at': '2025-07-31T03:57:48.024638+00:00', 'updated_at': None, 'user_id': 'test_user_charlie', 'agent_id': 'health_agent'}]", "execution_time": 0.05362582206726074, "timestamp": "2025-07-31T11:57:57.904744"}, {"test_name": "Get All Memories (test_user_charlie)", "status": "FAIL", "details": "Get all failed: [{'id': '3c9ae4b4-8613-4e3f-857b-bd3f28fe8a0e', 'memory': 'Lives in New York', 'hash': 'dc90a9ed407fc2599420c74ae0f55803', 'metadata': {'api_version': 'v1', 'categories': ['misc']}, 'created_at': '2025-07-31T03:57:55.780663+00:00', 'updated_at': None, 'user_id': 'test_user_charlie'}, {'id': '3ccf45e3-acab-456d-ac79-97abf3d2fb88', 'memory': 'Allergic to peanuts', 'hash': 'bfbe51c2a12e06aa942e95e2ec36526a', 'metadata': {'api_version': 'v1', 'categories': ['health']}, 'created_at': '2025-07-31T03:57:48.024638+00:00', 'updated_at': None, 'user_id': 'test_user_charlie', 'agent_id': 'health_agent'}, {'id': '4f937238-c75c-4c77-b867-ab1bc5d9ca31', 'memory': 'Likes Italian food', 'hash': '21faf1728d98625c307cc15e1db55363', 'metadata': {'api_version': 'v1', 'categories': ['misc']}, 'created_at': '2025-07-31T03:57:55.388954+00:00', 'updated_at': None, 'user_id': 'test_user_charlie'}, {'id': 'c94a2e78-d41a-4e4e-8671-8db857b7650e', 'memory': 'Allergic to peanuts', 'hash': 'bfbe51c2a12e06aa942e95e2ec36526a', 'metadata': {'api_version': 'v1', 'categories': ['misc']}, 'created_at': '2025-07-31T03:57:55.769125+00:00', 'updated_at': None, 'user_id': 'test_user_charlie'}, {'id': 'ef662fbb-8b99-4f26-9a30-bad30ba84163', 'memory': 'Likes Italian food', 'hash': '21faf1728d98625c307cc15e1db55363', 'metadata': {'api_version': 'v1', 'categories': ['food']}, 'created_at': '2025-07-31T03:57:43.963583+00:00', 'updated_at': None, 'user_id': 'test_user_charlie', 'agent_id': 'food_agent'}]", "execution_time": 0.01126718521118164, "timestamp": "2025-07-31T11:57:57.916295"}, {"test_name": "Graph Entities (No user)", "status": "PASS", "details": "Retrieved 0 entities", "execution_time": 0.003116130828857422, "timestamp": "2025-07-31T11:57:57.919711"}, {"test_name": "Graph Entities (test_user_alice)", "status": "PASS", "details": "Retrieved 6 entities", "execution_time": 0.007800102233886719, "timestamp": "2025-07-31T11:57:57.927774"}, {"test_name": "Graph Relationships (No user)", "status": "PASS", "details": "Retrieved 0 relationships", "execution_time": 0.003039836883544922, "timestamp": "2025-07-31T11:57:57.931116"}, {"test_name": "Graph Relationships (test_user_alice)", "status": "PASS", "details": "Retrieved 5 relationships", "execution_time": 0.007394552230834961, "timestamp": "2025-07-31T11:57:57.938773"}, {"test_name": "Add Memory (perf_user_0)", "status": "FAIL", "details": "Failed to add memory: []", "execution_time": 5.35986852645874, "timestamp": "2025-07-31T11:58:03.299038"}, {"test_name": "Add Memory (perf_user_1)", "status": "FAIL", "details": "Failed to add memory: []", "execution_time": 4.127648830413818, "timestamp": "2025-07-31T11:58:07.426954"}, {"test_name": "Add Memory (perf_user_2)", "status": "FAIL", "details": "Failed to add memory: []", "execution_time": 6.098066806793213, "timestamp": "2025-07-31T11:58:13.525352"}, {"test_name": "Add Memory (perf_user_3)", "status": "FAIL", "details": "Failed to add memory: []", "execution_time": 6.689151763916016, "timestamp": "2025-07-31T11:58:20.214759"}, {"test_name": "Add Memory (perf_user_4)", "status": "FAIL", "details": "Failed to add memory: []", "execution_time": 4.005181074142456, "timestamp": "2025-07-31T11:58:24.220207"}, {"test_name": "Batch Operations Performance", "status": "PASS", "details": "Added 5 memories in 26.28s", "execution_time": 26.28133463859558, "timestamp": "2025-07-31T11:58:24.220503"}, {"test_name": "Delete All Memories (perf_user_0)", "status": "PASS", "details": "Successfully deleted all memories", "execution_time": 0.0312044620513916, "timestamp": "2025-07-31T11:58:24.251856"}, {"test_name": "Delete All Memories (perf_user_1)", "status": "PASS", "details": "Successfully deleted all memories", "execution_time": 0.015095949172973633, "timestamp": "2025-07-31T11:58:24.267265"}, {"test_name": "Delete All Memories (perf_user_2)", "status": "PASS", "details": "Successfully deleted all memories", "execution_time": 0.01703953742980957, "timestamp": "2025-07-31T11:58:24.284548"}, {"test_name": "Delete All Memories (perf_user_3)", "status": "PASS", "details": "Successfully deleted all memories", "execution_time": 0.016531944274902344, "timestamp": "2025-07-31T11:58:24.301371"}, {"test_name": "Delete All Memories (perf_user_4)", "status": "PASS", "details": "Successfully deleted all memories", "execution_time": 0.015700817108154297, "timestamp": "2025-07-31T11:58:24.317335"}, {"test_name": "Delete All Memories (test_user_alice)", "status": "PASS", "details": "Successfully deleted all memories", "execution_time": 0.10547494888305664, "timestamp": "2025-07-31T11:58:24.423204"}, {"test_name": "Delete All Memories (test_user_bob)", "status": "PASS", "details": "Successfully deleted all memories", "execution_time": 0.0958402156829834, "timestamp": "2025-07-31T11:58:24.519329"}, {"test_name": "Delete All Memories (test_user_charlie)", "status": "PASS", "details": "Successfully deleted all memories", "execution_time": 0.10769796371459961, "timestamp": "2025-07-31T11:58:24.627332"}]}