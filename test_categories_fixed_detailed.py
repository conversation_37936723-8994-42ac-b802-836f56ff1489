#!/usr/bin/env python3
"""
详细测试Categories修复后的功能
"""

import requests
import json
import time

API_BASE = "http://localhost:8000"

def test_categories_functionality():
    print("🔧 详细测试Categories修复后的功能")
    print("=" * 60)
    
    # 1. 首先测试自动Categories生成
    print("\n=== 1. 测试自动Categories生成 ===")
    
    try:
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": "My name is <PERSON> and I work as a software engineer at Apple. I love playing basketball."}],
                "user_id": "auto_categories_test"
            },
            timeout=30
        )
        
        print(f"创建记忆响应状态: {response.status_code}")
        if response.status_code == 200:
            memories_created = response.json()
            print(f"创建的记忆数量: {len(memories_created)}")
            print(f"创建详情: {json.dumps(memories_created, indent=2, ensure_ascii=False)}")
            
            # 等待处理完成
            time.sleep(2)
            
            # 获取用户的所有记忆
            get_response = requests.get(
                f"{API_BASE}/v1/memories/",
                params={"user_id": "auto_categories_test", "limit": 50},
                timeout=10
            )
            
            if get_response.status_code == 200:
                all_memories = get_response.json()
                print(f"用户记忆总数: {len(all_memories)}")
                
                for i, memory in enumerate(all_memories):
                    categories = memory.get('metadata', {}).get('categories', [])
                    print(f"记忆 {i+1}: {memory.get('memory', '')} -> Categories: {categories}")
            else:
                print(f"获取记忆失败: {get_response.status_code}")
        else:
            print(f"创建记忆失败: {response.text}")
            
    except Exception as e:
        print(f"自动Categories测试异常: {e}")
    
    # 2. 测试自定义Categories（正确格式）
    print(f"\n=== 2. 测试自定义Categories（修复后） ===")
    
    try:
        custom_categories = [
            {"education": "Educational background and achievements"},
            {"programming": "Programming skills and technologies"},
            {"achievement": "Personal and professional achievements"}
        ]
        
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": "I completed my Master's degree in Computer Science and received certification in Python programming."}],
                "user_id": "custom_categories_test", 
                "custom_categories": custom_categories
            },
            timeout=30
        )
        
        print(f"自定义Categories请求: {json.dumps(custom_categories, indent=2, ensure_ascii=False)}")
        print(f"创建记忆响应状态: {response.status_code}")
        
        if response.status_code == 200:
            memories_created = response.json()
            print(f"创建的记忆数量: {len(memories_created)}")
            print(f"创建详情: {json.dumps(memories_created, indent=2, ensure_ascii=False)}")
            
            # 等待处理完成
            time.sleep(2)
            
            # 获取用户的所有记忆
            get_response = requests.get(
                f"{API_BASE}/v1/memories/",
                params={"user_id": "custom_categories_test", "limit": 50},
                timeout=10
            )
            
            if get_response.status_code == 200:
                all_memories = get_response.json()
                print(f"用户记忆总数: {len(all_memories)}")
                
                custom_found = False
                for i, memory in enumerate(all_memories):
                    categories = memory.get('metadata', {}).get('categories', [])
                    print(f"记忆 {i+1}: {memory.get('memory', '')} -> Categories: {categories}")
                    
                    # 检查是否使用了自定义分类
                    expected_custom = ["education", "programming", "achievement"]
                    found_custom = [cat for cat in expected_custom if cat in categories]
                    if found_custom:
                        custom_found = True
                        print(f"  ✓ 发现自定义分类: {found_custom}")
                
                if custom_found:
                    print("✅ 自定义Categories功能修复成功!")
                else:
                    print("❌ 自定义Categories功能仍有问题")
            else:
                print(f"获取记忆失败: {get_response.status_code}")
        else:
            print(f"创建记忆失败: {response.text}")
            
    except Exception as e:
        print(f"自定义Categories测试异常: {e}")
    
    # 3. 测试优先级：custom_categories应该覆盖自动生成的
    print(f"\n=== 3. 测试custom_categories优先级 ===")
    
    try:
        # 这个内容通常会被自动分类为professional_details, technology等
        # 但我们提供自定义分类应该覆盖它
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": "I work as a data scientist and use machine learning daily."}],
                "user_id": "priority_test",
                "custom_categories": [
                    {"my_work": "Work related activities"},
                    {"my_skills": "Technical skills"}
                ]
            },
            timeout=30
        )
        
        print(f"优先级测试响应状态: {response.status_code}")
        
        if response.status_code == 200:
            time.sleep(2)
            
            get_response = requests.get(
                f"{API_BASE}/v1/memories/",
                params={"user_id": "priority_test", "limit": 50},
                timeout=10
            )
            
            if get_response.status_code == 200:
                all_memories = get_response.json()
                
                for memory in all_memories:
                    categories = memory.get('metadata', {}).get('categories', [])
                    print(f"优先级测试结果: {categories}")
                    
                    # 检查是否使用了自定义分类而不是自动分类
                    if "my_work" in categories or "my_skills" in categories:
                        print("✅ 自定义分类优先级正确!")
                        # 确认没有默认分类
                        default_cats = ["professional_details", "technology"]
                        found_default = [cat for cat in default_cats if cat in categories]
                        if not found_default:
                            print("✅ 成功覆盖了默认分类!")
                        else:
                            print(f"⚠️ 仍包含默认分类: {found_default}")
                    else:
                        print(f"❌ 未使用自定义分类，使用了: {categories}")
        else:
            print(f"优先级测试失败: {response.text}")
            
    except Exception as e:
        print(f"优先级测试异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Categories修复功能测试完成!")

if __name__ == "__main__":
    test_categories_functionality()