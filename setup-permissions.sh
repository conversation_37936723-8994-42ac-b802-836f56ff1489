#!/bin/bash
set -e

# Mem0 Docker 权限管理脚本
# 用于确保数据目录有正确的用户映射和权限

echo "🔧 Mem0 Docker 权限配置脚本"
echo "================================"

# 项目根目录
PROJECT_ROOT="/opt/mem0ai"
DATA_DIR="$PROJECT_ROOT/data"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 创建数据目录结构
create_data_structure() {
    log_info "创建数据目录结构..."
    
    mkdir -p "$DATA_DIR"/{mem0,qdrant,neo4j/{data,logs,import,plugins}}
    
    log_info "数据目录结构创建完成："
    tree "$DATA_DIR" 2>/dev/null || ls -la "$DATA_DIR"
}

# 设置用户和组权限
set_permissions() {
    log_info "配置服务权限..."
    
    # Mem0 服务权限 (UID 1000 - 对应容器内mem0用户)
    log_info "设置 Mem0 数据目录权限 (UID: 1000)"
    chown -R 1000:1000 "$DATA_DIR/mem0"
    
    # Qdrant 服务权限 (UID 1000 - qdrant默认用户)
    log_info "设置 Qdrant 数据目录权限 (UID: 1000)"
    chown -R 1000:1000 "$DATA_DIR/qdrant"
    
    # Neo4j 服务权限 (UID 7474 - neo4j官方容器用户)
    log_info "设置 Neo4j 数据目录权限 (UID: 7474)"
    chown -R 7474:7474 "$DATA_DIR/neo4j"
    
    # 设置基本权限
    log_info "设置目录权限..."
    chmod -R 755 "$DATA_DIR"
    
    # 确保数据目录对容器用户可写
    find "$DATA_DIR" -type d -exec chmod 755 {} \;
    find "$DATA_DIR" -type f -exec chmod 644 {} \;
}

# 验证权限设置
verify_permissions() {
    log_info "验证权限设置..."
    
    echo "数据目录权限概览："
    ls -la "$DATA_DIR"
    
    echo ""
    echo "详细权限信息："
    
    # 检查mem0目录
    mem0_owner=$(stat -c "%u:%g" "$DATA_DIR/mem0")
    if [[ "$mem0_owner" == "1000:1000" ]]; then
        log_info "✓ Mem0 目录权限正确 ($mem0_owner)"
    else
        log_warn "✗ Mem0 目录权限异常 ($mem0_owner)，应为 1000:1000"
    fi
    
    # 检查qdrant目录
    qdrant_owner=$(stat -c "%u:%g" "$DATA_DIR/qdrant")
    if [[ "$qdrant_owner" == "1000:1000" ]]; then
        log_info "✓ Qdrant 目录权限正确 ($qdrant_owner)"
    else
        log_warn "✗ Qdrant 目录权限异常 ($qdrant_owner)，应为 1000:1000"
    fi
    
    # 检查neo4j目录
    neo4j_owner=$(stat -c "%u:%g" "$DATA_DIR/neo4j")
    if [[ "$neo4j_owner" == "7474:7474" ]]; then
        log_info "✓ Neo4j 目录权限正确 ($neo4j_owner)"
    else
        log_warn "✗ Neo4j 目录权限异常 ($neo4j_owner)，应为 7474:7474"
    fi
}

# 创建备份
create_backup() {
    if [[ -d "$DATA_DIR" ]]; then
        log_info "创建数据目录备份..."
        BACKUP_DIR="/opt/mem0ai/backup_$(date +%Y%m%d_%H%M%S)"
        cp -r "$DATA_DIR" "$BACKUP_DIR"
        log_info "备份已创建: $BACKUP_DIR"
    fi
}

# 检查Docker容器用户映射
check_container_users() {
    log_info "检查Docker容器用户映射..."
    
    if command -v docker &> /dev/null; then
        echo "Docker 版本："
        docker --version
        
        echo ""
        echo "检查可能的用户ID冲突："
        
        # 检查系统中是否存在相关用户
        if id 1000 &>/dev/null; then
            log_warn "系统中存在 UID 1000 用户: $(id -un 1000)"
        fi
        
        if id 7474 &>/dev/null; then
            log_warn "系统中存在 UID 7474 用户: $(id -un 7474)"  
        fi
    else
        log_error "Docker 未安装或不可用"
    fi
}

# 主函数
main() {
    echo "开始配置 Mem0 Docker 环境权限..."
    echo ""
    
    check_root
    create_backup
    create_data_structure
    set_permissions
    verify_permissions
    check_container_users
    
    echo ""
    log_info "权限配置完成！"
    echo ""
    echo "🚀 现在可以安全地运行："
    echo "   docker-compose up -d"
    echo ""
    echo "📝 权限配置摘要："
    echo "   • Mem0 数据目录: $DATA_DIR/mem0 (1000:1000)"
    echo "   • Qdrant 数据目录: $DATA_DIR/qdrant (1000:1000)"  
    echo "   • Neo4j 数据目录: $DATA_DIR/neo4j (7474:7474)"
    echo ""
    echo "⚠️  如需重新配置权限，可重新运行此脚本"
}

# 脚本参数处理
case "${1:-}" in
    --verify-only)
        check_root
        verify_permissions
        ;;
    --backup-only)
        check_root
        create_backup
        ;;
    *)
        main
        ;;
esac