#!/usr/bin/env python3
"""
快速验证Categories功能是否正常工作
"""

import requests
import json
import time
import uuid

API_BASE = "http://localhost:8000"

def quick_categories_test():
    print("⚡ 快速验证Categories功能")
    print("=" * 50)
    
    # 使用随机用户ID避免冲突
    test_user = f"quick_test_{str(uuid.uuid4())[:8]}"
    
    # 1. 测试自动Categories生成
    print(f"\n=== 测试用户: {test_user} ===")
    print("1. 测试自动Categories生成")
    
    try:
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": "My name is <PERSON> and I am a machine learning researcher at Stanford University. I love hiking in the mountains."}],
                "user_id": test_user
            },
            timeout=30
        )
        
        print(f"响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"创建结果数量: {len(result)}")
            
            if result:
                print("创建详情:")
                for item in result:
                    print(f"  - ID: {item.get('id')}")
                    print(f"  - 记忆: {item.get('memory')}")
                    print(f"  - 事件: {item.get('event')}")
            else:
                print("  没有创建新记忆（可能因为内容重复或LLM认为没有新事实）")
                
            # 等待2秒确保存储完成
            time.sleep(2)
            
            # 查看该用户的所有记忆
            get_response = requests.get(
                f"{API_BASE}/v1/memories/",
                params={"user_id": test_user, "limit": 10}
            )
            
            if get_response.status_code == 200:
                memories = get_response.json()
                print(f"\n该用户总记忆数: {len(memories)}")
                
                for i, memory in enumerate(memories):
                    categories = memory.get('metadata', {}).get('categories', [])
                    print(f"记忆 {i+1}: \"{memory.get('memory', '')}\" -> Categories: {categories}")
                    
                if memories and any(mem.get('metadata', {}).get('categories') for mem in memories):
                    print("✅ 自动Categories生成正常!")
                else:
                    print("⚠️ 未发现Categories字段")
            else:
                print(f"获取记忆失败: {get_response.status_code}")
        else:
            print(f"请求失败: {response.text[:200]}")
            
    except Exception as e:
        print(f"测试异常: {e}")
    
    # 2. 测试自定义Categories
    print(f"\n2. 测试自定义Categories")
    
    custom_user = f"custom_test_{str(uuid.uuid4())[:8]}"
    
    try:
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": "I just finished a marathon race in Boston and achieved my personal best time of 3 hours and 15 minutes."}],
                "user_id": custom_user,
                "custom_categories": [
                    {"sports_achievement": "Athletic accomplishments and records"},
                    {"running": "Running activities and progress"},
                    {"personal_milestone": "Important personal achievements"}
                ]
            },
            timeout=30
        )
        
        print(f"响应状态: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"创建结果数量: {len(result)}")
            
            time.sleep(2)
            
            # 查看该用户的所有记忆
            get_response = requests.get(
                f"{API_BASE}/v1/memories/",
                params={"user_id": custom_user, "limit": 10}
            )
            
            if get_response.status_code == 200:
                memories = get_response.json()
                print(f"\n该用户总记忆数: {len(memories)}")
                
                custom_categories_found = False
                for i, memory in enumerate(memories):
                    categories = memory.get('metadata', {}).get('categories', [])
                    print(f"记忆 {i+1}: \"{memory.get('memory', '')}\" -> Categories: {categories}")
                    
                    # 检查是否使用了自定义分类
                    expected_custom = ["sports_achievement", "running", "personal_milestone"]
                    found_custom = [cat for cat in expected_custom if cat in categories]
                    if found_custom:
                        custom_categories_found = True
                        print(f"  ✅ 发现自定义分类: {found_custom}")
                
                if custom_categories_found:
                    print("✅ 自定义Categories功能正常!")
                else:
                    print("❌ 自定义Categories功能异常")
            else:
                print(f"获取记忆失败: {get_response.status_code}")
        else:
            print(f"请求失败: {response.text[:200]}")
            
    except Exception as e:
        print(f"自定义Categories测试异常: {e}")
    
    print("\n" + "=" * 50)
    print("⚡ 快速验证完成!")

if __name__ == "__main__":
    quick_categories_test()