#!/usr/bin/env python3
"""
测试Mem0高级检索功能
包括关键词搜索、重排序、内存过滤等高级功能
"""

import requests
import json
import time
import uuid

API_BASE = "http://localhost:8000"

def setup_test_memories(user_id):
    """设置测试用的记忆数据"""
    print(f"为用户 {user_id} 设置测试记忆...")
    
    test_memories = [
        "I work as a software engineer at Google, specializing in machine learning algorithms.",
        "I love hiking in the mountains during weekends, especially in Yosemite National Park.",
        "My favorite programming languages are Python and JavaScript for different projects.",
        "I recently completed a marathon race in San Francisco with a time of 3:45:23.",
        "I'm currently reading a book about artificial intelligence and neural networks.",
        "I enjoy cooking Italian cuisine and my specialty is homemade pasta.",
        "I have a pet dog named <PERSON> who loves playing fetch in the park.",
        "I'm planning a vacation to Japan next summer to visit Tokyo and Kyoto.",
        "I graduated from Stanford University with a Master's degree in Computer Science.",
        "I volunteer at a local animal shelter every Saturday morning."
    ]
    
    created_count = 0
    for memory_text in test_memories:
        try:
            response = requests.post(
                f"{API_BASE}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": memory_text}],
                    "user_id": user_id
                },
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                created_count += len(result)
            
            time.sleep(0.5)  # 短暂延迟避免过快请求
            
        except Exception as e:
            print(f"创建记忆失败: {e}")
    
    print(f"成功创建 {created_count} 条记忆")
    return created_count

def test_advanced_retrieval():
    """测试高级检索功能"""
    print("🔍 测试Mem0高级检索功能")
    print("=" * 60)
    
    test_user = f"advanced_test_{str(uuid.uuid4())[:8]}"
    
    # 1. 设置测试数据
    print("\n=== 1. 设置测试数据 ===")
    memory_count = setup_test_memories(test_user)
    
    if memory_count == 0:
        print("❌ 无法创建测试记忆，跳过高级检索测试")
        return
    
    # 等待记忆处理完成
    print("等待记忆处理完成...")
    time.sleep(5)
    
    # 2. 测试基本搜索
    print("\n=== 2. 测试基本搜索功能 ===")
    
    try:
        # 基本搜索测试
        search_queries = [
            "programming",
            "hiking",
            "food",
            "education",
            "animals"
        ]
        
        for query in search_queries:
            print(f"\n搜索查询: '{query}'")
            
            response = requests.post(
                f"{API_BASE}/v1/memories/search/",
                json={
                    "query": query,
                    "user_id": test_user,
                    "limit": 5
                },
                timeout=15
            )
            
            if response.status_code == 200:
                results = response.json()
                print(f"找到 {len(results)} 条相关记忆:")
                
                for i, memory in enumerate(results[:3]):  # 只显示前3条
                    score = memory.get('score', 0)
                    memory_text = memory.get('memory', '')
                    print(f"  {i+1}. [分数: {score:.3f}] {memory_text[:80]}...")
                
                if results:
                    print("✅ 基本搜索功能正常")
                else:
                    print("⚠️ 搜索无结果")
            else:
                print(f"搜索失败: {response.status_code}")
                
    except Exception as e:
        print(f"基本搜索测试异常: {e}")
    
    # 3. 测试阈值过滤
    print("\n=== 3. 测试阈值过滤功能 ===")
    
    try:
        thresholds = [0.5, 0.7, 0.9]
        query = "programming software"
        
        for threshold in thresholds:
            print(f"\n测试阈值: {threshold}")
            
            response = requests.post(
                f"{API_BASE}/v1/memories/search/",
                json={
                    "query": query,
                    "user_id": test_user,
                    "threshold": threshold,
                    "limit": 10
                },
                timeout=15
            )
            
            if response.status_code == 200:
                results = response.json()
                
                # 检查所有结果是否满足阈值要求
                valid_results = [r for r in results if r.get('score', 0) >= threshold]
                print(f"  阈值 {threshold}: {len(results)} 条结果, {len(valid_results)} 条满足阈值")
                
                if len(valid_results) == len(results):
                    print(f"  ✅ 阈值 {threshold} 过滤正确")
                else:
                    print(f"  ⚠️ 阈值 {threshold} 过滤可能不完全准确")
            else:
                print(f"  阈值测试失败: {response.status_code}")
                
    except Exception as e:
        print(f"阈值过滤测试异常: {e}")
    
    # 4. 测试高级检索参数（如果支持）
    print("\n=== 4. 测试高级检索参数 ===")
    
    advanced_params = [
        {"keyword_search": True},
        {"rerank": True}, 
        {"filter_memories": True},
        {"keyword_search": True, "rerank": True}
    ]
    
    base_query = "machine learning artificial intelligence"
    
    for i, params in enumerate(advanced_params):
        try:
            print(f"\n测试参数组合 {i+1}: {params}")
            
            search_params = {
                "query": base_query,
                "user_id": test_user,
                "limit": 5,
                **params
            }
            
            response = requests.post(
                f"{API_BASE}/v1/memories/search/",
                json={
                    "query": base_query,
                    "user_id": test_user,
                    "limit": 5,
                    **params
                },
                timeout=20
            )
            
            if response.status_code == 200:
                results = response.json()
                print(f"  找到 {len(results)} 条结果")
                
                if results:
                    # 显示最相关的结果
                    top_result = results[0]
                    print(f"  最相关: [分数: {top_result.get('score', 0):.3f}] {top_result.get('memory', '')[:60]}...")
                    print(f"  ✅ 参数组合 {i+1} 工作正常")
                else:
                    print(f"  ❓ 参数组合 {i+1} 无结果")
            elif response.status_code == 422:
                print(f"  ❌ 参数组合 {i+1} 不支持: {response.text[:100]}")
            else:
                print(f"  ❌ 参数组合 {i+1} 失败: {response.status_code}")
                
        except Exception as e:
            print(f"高级参数测试异常: {e}")
    
    # 5. 测试检索条件评分（如果支持）
    print("\n=== 5. 测试检索条件评分 ===")
    
    try:
        # 尝试测试retrieval_criteria参数
        retrieval_criteria = [
            {"name": "technical_relevance", "weight": 0.7},
            {"name": "recency", "weight": 0.3}
        ]
        
        response = requests.post(
            f"{API_BASE}/v1/memories/search/",
            json={
                "query": "programming project work",
                "user_id": test_user,
                "limit": 5,
                "retrieval_criteria": retrieval_criteria
            },
            timeout=20
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"条件评分搜索找到 {len(results)} 条结果")
            
            if results:
                print("✅ 检索条件评分功能可用")
            else:
                print("❓ 检索条件评分无结果")
        else:
            print(f"检索条件评分不支持或失败: {response.status_code}")
            
    except Exception as e:
        print(f"检索条件评分测试异常: {e}")
    
    # 6. 性能测试
    print("\n=== 6. 简单性能测试 ===")
    
    try:
        start_time = time.time()
        
        response = requests.post(
            f"{API_BASE}/v1/memories/search/",
            json={
                "query": "test performance query with multiple keywords",
                "user_id": test_user,
                "limit": 10
            },
            timeout=15
        )
        
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        print(f"搜索响应时间: {response_time:.1f}ms")
        
        if response.status_code == 200:
            results = response.json()
            print(f"性能测试找到 {len(results)} 条结果")
            
            if response_time < 2000:  # 2秒以内
                print("✅ 搜索性能良好")
            elif response_time < 5000:  # 5秒以内
                print("✅ 搜索性能可接受")
            else:
                print("⚠️ 搜索响应较慢")
        else:
            print(f"性能测试失败: {response.status_code}")
            
    except Exception as e:
        print(f"性能测试异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 高级检索功能测试完成!")

if __name__ == "__main__":
    test_advanced_retrieval()