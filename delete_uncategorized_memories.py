#!/usr/bin/env python3
"""
删除没有自动分类的记忆脚本
"""

import requests
import json
import time

API_BASE = "http://localhost:8000"
QDRANT_BASE = "http://localhost:6333"

def get_uncategorized_memories():
    """获取所有没有categories字段的记忆"""
    response = requests.post(
        f"{QDRANT_BASE}/collections/mem0/points/query",
        json={
            "limit": 1000,
            "with_payload": True,
            "with_vector": False
        }
    )
    
    if response.status_code != 200:
        print(f"获取记忆失败: {response.status_code}")
        return []
    
    data = response.json()
    points = data.get('result', {}).get('points', [])
    
    uncategorized = []
    categorized = []
    
    for point in points:
        payload = point.get('payload', {})
        memory_id = point.get('id')
        memory_data = payload.get('data', '')
        categories = payload.get('categories', [])
        
        if categories:
            categorized.append({
                'id': memory_id,
                'data': memory_data,
                'categories': categories
            })
        else:
            uncategorized.append({
                'id': memory_id,
                'data': memory_data,
                'payload': payload
            })
    
    print(f"发现 {len(points)} 条总记忆")
    print(f"有分类的记忆: {len(categorized)} 条")
    print(f"无分类的记忆: {len(uncategorized)} 条")
    
    return uncategorized

def delete_memory_by_id(memory_id):
    """通过ID删除单个记忆"""
    response = requests.delete(f"{API_BASE}/v1/memories/{memory_id}/")
    
    if response.status_code == 200:
        return True, "成功"
    else:
        return False, f"HTTP {response.status_code}: {response.text}"

def main():
    print("=" * 60)
    print("删除没有自动分类的记忆")
    print("=" * 60)
    
    # 获取无分类记忆
    uncategorized_memories = get_uncategorized_memories()
    
    if not uncategorized_memories:
        print("没有发现无分类的记忆，无需删除。")
        return
    
    print(f"\\n准备删除 {len(uncategorized_memories)} 条无分类记忆...")
    
    # 显示前10条将被删除的记忆
    print("\\n前10条将被删除的记忆:")
    for i, memory in enumerate(uncategorized_memories[:10]):
        print(f"{i+1:2d}. ID: {memory['id'][:8]}... | 内容: {memory['data'][:50]}...")
    
    if len(uncategorized_memories) > 10:
        print(f"    ... 还有 {len(uncategorized_memories) - 10} 条")
    
    # 自动确认删除
    print(f"\\n自动确认删除 {len(uncategorized_memories)} 条无分类记忆...")
    
    # 批量删除
    print("\\n开始删除...")
    success_count = 0
    failed_count = 0
    
    for i, memory in enumerate(uncategorized_memories):
        memory_id = memory['id']
        memory_data = memory['data']
        
        success, message = delete_memory_by_id(memory_id)
        
        if success:
            success_count += 1
            print(f"✓ [{i+1:2d}/{len(uncategorized_memories)}] 删除成功: {memory_data[:40]}...")
        else:
            failed_count += 1
            print(f"✗ [{i+1:2d}/{len(uncategorized_memories)}] 删除失败: {memory_data[:40]}... | {message}")
        
        # 避免API过载
        time.sleep(0.1)
    
    print("\\n" + "=" * 60)
    print("删除完成统计:")
    print(f"成功删除: {success_count} 条")
    print(f"删除失败: {failed_count} 条")
    print(f"总计处理: {len(uncategorized_memories)} 条")
    print("=" * 60)
    
    # 验证删除结果
    print("\\n验证删除结果...")
    remaining_uncategorized = get_uncategorized_memories()
    print(f"剩余无分类记忆: {len(remaining_uncategorized)} 条")

if __name__ == "__main__":
    main()