#!/usr/bin/env python3
"""
测试修复后的custom_categories功能
"""

import requests
import json
import time

API_BASE = "http://localhost:8000"

def test_fixed_custom_categories():
    print("🔧 测试修复后的custom_categories功能")
    print("=" * 60)
    
    # 等待服务重启
    print("等待服务重启...")
    time.sleep(5)
    
    # 1. 健康检查
    try:
        response = requests.get(f"{API_BASE}/health", timeout=5)
        if response.status_code == 200:
            print("✓ API健康检查: 正常")
        else:
            print("✗ API健康检查: 失败")
            return
    except Exception as e:
        print(f"✗ API连接失败: {e}")
        return
    
    # 2. 测试修复后的自定义Categories
    print(f"\n=== 测试修复后的自定义Categories ===")
    
    test_cases = [
        {
            "name": "教育成就测试",
            "content": "I completed my machine learning certification from Stanford.",
            "custom_categories": [{"education": "Educational achievements"}, {"certification": "Professional certifications"}],
            "expected": ["education", "certification"]
        },
        {
            "name": "工作经历测试",
            "content": "I got promoted to Senior Software Engineer at Microsoft.",
            "custom_categories": [{"career": "Career progression"}, {"achievement": "Personal achievements"}],
            "expected": ["career", "achievement"]
        },
        {
            "name": "多个分类测试",
            "content": "I learned Python programming and started working on AI projects.",
            "custom_categories": [
                {"programming": "Programming skills"}, 
                {"AI": "Artificial Intelligence"}, 
                {"learning": "Learning activities"}
            ],
            "expected": ["programming", "AI", "learning"]
        }
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n--- {case['name']} ---")
        
        try:
            response = requests.post(
                f"{API_BASE}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": case["content"]}],
                    "user_id": f"fixed_test_{i}",
                    "custom_categories": case["custom_categories"]
                },
                timeout=20
            )
            
            if response.status_code == 200:
                memories = response.json()
                if memories:
                    print(f"✓ 成功创建 {len(memories)} 条记忆")
                    
                    # 获取记忆详情检查categories
                    user_response = requests.get(
                        f"{API_BASE}/v1/memories/",
                        params={"user_id": f"fixed_test_{i}", "limit": 10}
                    )
                    
                    if user_response.status_code == 200:
                        user_memories = user_response.json()
                        if user_memories:
                            categories = user_memories[0].get('metadata', {}).get('categories', [])
                            print(f"✓ 发现categories: {categories}")
                            
                            # 检查是否使用了自定义分类
                            expected_found = [cat for cat in case['expected'] if cat in categories]
                            if expected_found:
                                print(f"✓ 成功使用自定义分类: {expected_found}")
                                if len(expected_found) == len(case['expected']):
                                    print(f"✓ 完全匹配预期分类!")
                                else:
                                    missing = [cat for cat in case['expected'] if cat not in categories]
                                    print(f"⚠️ 部分匹配，缺少: {missing}")
                            else:
                                print(f"✗ 未使用自定义分类，实际: {categories}")
                        else:
                            print("✗ 无法获取记忆")
                    else:
                        print("✗ 无法获取记忆列表")
                else:
                    print("✗ 未创建记忆")
            else:
                print(f"✗ HTTP {response.status_code}: {response.text[:200]}")
                
        except Exception as e:
            print(f"✗ 异常: {e}")
            
        time.sleep(2)
    
    # 3. 对比自动分类 vs 自定义分类
    print(f"\n=== 对比测试：自动 vs 自定义Categories ===")
    test_content = "I learned Python programming and built an AI chatbot."
    
    # 自动分类
    try:
        auto_response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": test_content}],
                "user_id": "compare_auto_fixed"
            },
            timeout=15
        )
        
        if auto_response.status_code == 200:
            time.sleep(1)
            auto_memories = requests.get(
                f"{API_BASE}/v1/memories/?user_id=compare_auto_fixed&limit=10"
            ).json()
            if auto_memories:
                auto_categories = auto_memories[0].get('metadata', {}).get('categories', [])
                print(f"自动分类结果: {auto_categories}")
            else:
                print("自动分类: 无结果")
        else:
            print(f"自动分类失败: HTTP {auto_response.status_code}")
            
    except Exception as e:
        print(f"自动分类异常: {e}")
    
    # 自定义分类
    try:
        custom_response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": test_content}],
                "user_id": "compare_custom_fixed",
                "custom_categories": [
                    {"coding": "Programming activities"}, 
                    {"project": "Personal projects"}
                ]
            },
            timeout=15
        )
        
        if custom_response.status_code == 200:
            time.sleep(1)
            custom_memories = requests.get(
                f"{API_BASE}/v1/memories/?user_id=compare_custom_fixed&limit=10"
            ).json()
            if custom_memories:
                custom_categories = custom_memories[0].get('metadata', {}).get('categories', [])
                print(f"自定义分类结果: {custom_categories}")
                
                if "coding" in custom_categories and "project" in custom_categories:
                    print("✓ 修复成功！自定义Categories正常工作!")
                else:
                    print("⚠️ 自定义Categories可能仍有问题")
            else:
                print("自定义分类: 无结果")
        else:
            print(f"自定义分类失败: HTTP {custom_response.status_code}")
            
    except Exception as e:
        print(f"自定义分类异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 custom_categories修复测试完成!")

if __name__ == "__main__":
    test_fixed_custom_categories()