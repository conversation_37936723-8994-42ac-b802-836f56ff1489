#!/usr/bin/env python3
"""
简化版测试记忆生成器
"""

import requests
import json

API_BASE = "http://localhost:8000"
HEADERS = {"Content-Type": "application/json"}

def add_memory(messages, user_id, agent_id=None, enable_graph=False, metadata=None):
    """添加记忆"""
    data = {
        "messages": messages,
        "user_id": user_id,
        "enable_graph": enable_graph
    }
    
    if agent_id:
        data["agent_id"] = agent_id
    if metadata:
        data["metadata"] = metadata
    
    try:
        response = requests.post(f"{API_BASE}/v1/memories/", json=data, headers=HEADERS)
        if response.status_code == 200:
            print(f"✅ 成功: {messages[0]['content'][:30]}...")
            return True
        else:
            print(f"❌ 失败: {response.text[:50]}")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

# 快速测试记忆数据
test_memories = [
    # 学习类
    {
        "messages": [{"role": "user", "content": "学习了React Hooks：useState管理状态，useEffect处理副作用"}],
        "user_id": "user_002",
        "agent_id": "learning_assistant",
        "metadata": {"category": "learning", "type": "programming", "topic": "React"}
    },
    {
        "messages": [{"role": "user", "content": "Docker核心概念：镜像是模板，容器是实例，仓库存储镜像"}],
        "user_id": "user_002", 
        "metadata": {"category": "learning", "type": "devops", "topic": "Docker"}
    },
    
    # 生活类
    {
        "messages": [{"role": "user", "content": "今天去三里屯购物，买了优衣库毛衣299元，星巴克咖啡42元"}],
        "user_id": "user_003",
        "metadata": {"category": "life", "type": "shopping", "expense": 341}
    },
    {
        "messages": [{"role": "user", "content": "和朋友小美、小红看了电影《飞驰人生2》，很搞笑，评分8.5"}],
        "user_id": "user_003",
        "enable_graph": True,
        "metadata": {"category": "life", "type": "entertainment", "rating": 8.5}
    },
    
    # 关系类
    {
        "messages": [{"role": "user", "content": "我的妻子李小红是协和医院的医生，2020年结婚，喜欢瑜伽"}],
        "user_id": "user_004",
        "enable_graph": True,
        "metadata": {"category": "relationship", "type": "family", "relation": "spouse"}
    },
    {
        "messages": [{"role": "user", "content": "同事王大明是前端工程师，擅长Vue.js和React"}],
        "user_id": "user_004",
        "enable_graph": True,
        "metadata": {"category": "relationship", "type": "colleague", "skills": ["Vue.js", "React"]}
    },
    
    # 工作类
    {
        "messages": [{"role": "user", "content": "开发AI客服系统，技术栈FastAPI+Vue.js，3月上线"}],
        "user_id": "user_001",
        "agent_id": "work_assistant",
        "metadata": {"category": "work", "type": "project", "deadline": "2024-03-31"}
    },
    {
        "messages": [{"role": "user", "content": "Redis缓存优化完成，API响应从800ms降到120ms，提升85%"}],
        "user_id": "user_001",
        "agent_id": "work_assistant", 
        "metadata": {"category": "work", "type": "achievement", "performance": "85%"}
    },
    
    # 复杂对话
    {
        "messages": [
            {"role": "user", "content": "想做电商网站，用什么技术栈？"},
            {"role": "assistant", "content": "建议React+FastAPI+PostgreSQL+Redis"},
            {"role": "user", "content": "10万用户规模需要考虑什么？"},
            {"role": "assistant", "content": "负载均衡、分库分表、CDN、监控等"}
        ],
        "user_id": "user_005",
        "agent_id": "tech_consultant",
        "metadata": {"category": "consultation", "type": "complex_dialog", "users": 100000}
    }
]

print("🚀 开始添加测试记忆...")
success = 0
for i, memory in enumerate(test_memories, 1):
    print(f"[{i}/{len(test_memories)}] ", end="")
    if add_memory(**memory):
        success += 1

print(f"\n🎉 完成！成功添加 {success}/{len(test_memories)} 条记忆")