#!/usr/bin/env python3
"""
图记忆功能基础结构测试
Basic Graph Memory Structure Test

测试图记忆功能的核心结构和依赖，不依赖外部API
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '/opt/mem0ai')

def test_graph_memory_structure():
    """测试图记忆结构"""
    print("=" * 60)
    print("Mem0 图记忆功能结构测试")
    print(f"测试时间: {datetime.now()}")
    print("=" * 60)
    
    success_count = 0
    total_tests = 8
    
    # 1. 测试基础导入
    try:
        from mem0.memory.graph_memory import MemoryGraph
        from mem0.graphs.configs import GraphStoreConfig, Neo4jConfig
        print("✓ 1. 核心类导入成功")
        success_count += 1
    except Exception as e:
        print(f"✗ 1. 核心类导入失败: {e}")
    
    # 2. 测试Neo4j直接连接
    try:
        from langchain_neo4j import Neo4jGraph
        graph = Neo4jGraph(
            "bolt://localhost:7687",
            "neo4j", 
            "mem0graph",
            refresh_schema=False,
            driver_config={"notifications_min_severity": "OFF"}
        )
        
        # 测试基本查询
        result = graph.query("RETURN 'test' as message")
        if result and result[0]['message'] == 'test':
            print("✓ 2. Neo4j图数据库连接成功")
            success_count += 1
        else:
            print("✗ 2. Neo4j连接测试失败")
    except Exception as e:
        print(f"✗ 2. Neo4j连接失败: {e}")
    
    # 3. 测试配置类创建
    try:
        neo4j_config = Neo4jConfig(
            url="bolt://localhost:7687",
            username="neo4j",
            password="mem0graph"
        )
        
        graph_store_config = GraphStoreConfig(
            provider="neo4j",
            config=neo4j_config
        )
        print("✓ 3. 配置类创建成功")
        success_count += 1
    except Exception as e:
        print(f"✗ 3. 配置类创建失败: {e}")
    
    # 4. 测试图记忆工具导入
    try:
        from mem0.graphs.tools import (
            EXTRACT_ENTITIES_TOOL,
            RELATIONS_TOOL,
            DELETE_MEMORY_TOOL_GRAPH
        )
        print("✓ 4. 图记忆工具导入成功")
        success_count += 1
    except Exception as e:
        print(f"✗ 4. 图记忆工具导入失败: {e}")
    
    # 5. 测试图查询提示模板
    try:
        from mem0.graphs.utils import EXTRACT_RELATIONS_PROMPT, get_delete_messages
        
        # 检查关键内容是否存在（更正确的检查逻辑）
        if "extract structured information" in EXTRACT_RELATIONS_PROMPT.lower() and "USER_ID" in EXTRACT_RELATIONS_PROMPT:
            print("✓ 5. 图查询提示模板正常")
            success_count += 1
        else:
            print("✗ 5. 图查询提示模板异常")
    except Exception as e:
        print(f"✗ 5. 图查询提示模板导入失败: {e}")
    
    # 6. 测试直接图操作（不使用Memory类）
    try:
        from langchain_neo4j import Neo4jGraph
        
        graph = Neo4jGraph(
            "bolt://localhost:7687",
            "neo4j", 
            "mem0graph",
            refresh_schema=False
        )
        
        # 创建测试数据
        test_user = "structure_test_user"
        
        # 清理
        cleanup_query = f"""
        MATCH (n {{user_id: '{test_user}'}})
        DETACH DELETE n
        """
        graph.query(cleanup_query)
        
        # 创建测试图结构
        create_query = f"""
        CREATE (alice {{name: 'alice', user_id: '{test_user}', type: 'person'}})
        CREATE (bob {{name: 'bob', user_id: '{test_user}', type: 'person'}})
        CREATE (alice)-[:KNOWS]->(bob)
        RETURN count(*) as created
        """
        result = graph.query(create_query)
        
        # 查询测试
        query_result = graph.query(f"""
        MATCH (n {{user_id: '{test_user}'}})-[r]->(m {{user_id: '{test_user}'}})
        RETURN n.name as source, type(r) as rel, m.name as target
        """)
        
        if query_result and len(query_result) > 0:
            print("✓ 6. 直接图操作测试成功")
            success_count += 1
        else:
            print("✗ 6. 直接图操作测试失败")
        
        # 清理
        graph.query(cleanup_query)
        
    except Exception as e:
        print(f"✗ 6. 直接图操作测试异常: {e}")
    
    # 7. 测试BM25搜索依赖
    try:
        from rank_bm25 import BM25Okapi
        
        # 创建简单的BM25测试
        corpus = [
            ["alice", "knows", "bob"],
            ["alice", "likes", "pizza"],
            ["bob", "works", "company"]
        ]
        
        bm25 = BM25Okapi(corpus)
        query = ["alice", "bob"]
        scores = bm25.get_scores(query)
        
        if len(scores) == len(corpus):
            print("✓ 7. BM25搜索功能正常")
            success_count += 1
        else:
            print("✗ 7. BM25搜索功能异常")
    except Exception as e:
        print(f"✗ 7. BM25搜索测试失败: {e}")
    
    # 8. 测试图记忆核心方法存在性
    try:
        from mem0.memory.graph_memory import MemoryGraph
        
        # 检查关键方法
        required_methods = [
            'add', 'search', 'get_all', 'delete_all',
            '_retrieve_nodes_from_data', '_establish_nodes_relations_from_data',
            '_search_graph_db', '_add_entities', '_delete_entities'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(MemoryGraph, method):
                missing_methods.append(method)
        
        if not missing_methods:
            print("✓ 8. 图记忆核心方法完整")
            success_count += 1
        else:
            print(f"✗ 8. 图记忆缺少方法: {missing_methods}")
            
    except Exception as e:
        print(f"✗ 8. 图记忆方法检查失败: {e}")
    
    # 总结
    print("\n" + "=" * 60)
    print(f"测试结果: {success_count}/{total_tests} 项通过")
    
    if success_count == total_tests:
        print("🎉 图记忆功能结构完全正常！")
        status = "完全正常"
    elif success_count >= total_tests * 0.8:
        print("✅ 图记忆功能结构基本正常")
        status = "基本正常"
    elif success_count >= total_tests * 0.6:
        print("⚠️  图记忆功能结构部分正常")
        status = "部分正常"
    else:
        print("❌ 图记忆功能结构存在严重问题")
        status = "存在问题"
    
    print("=" * 60)
    return success_count, total_tests, status

def test_graph_memory_providers():
    """测试支持的图数据库提供商"""
    print("\n图数据库提供商支持测试:")
    print("-" * 40)
    
    # Neo4j
    try:
        from mem0.graphs.configs import Neo4jConfig
        config = Neo4jConfig()
        print("✓ Neo4j: 支持")
    except Exception as e:
        print(f"✗ Neo4j: 不支持 - {e}")
    
    # Memgraph
    try:
        from mem0.graphs.configs import MemgraphConfig
        config = MemgraphConfig(
            url="bolt://localhost:7687",
            username="memgraph",
            password="test"
        )
        print("✓ Memgraph: 支持")
    except Exception as e:
        print(f"✗ Memgraph: 不支持 - {e}")
    
    # Neptune
    try:
        from mem0.graphs.configs import NeptuneConfig
        from langchain_aws import NeptuneAnalyticsGraph
        config = NeptuneConfig(endpoint="neptune-graph://g-test")
        print("✓ Neptune: 完全支持")
    except ImportError as e:
        if "langchain_aws" in str(e):
            print("⚠️ Neptune: 配置支持（依赖缺失 - langchain_aws）")
        else:
            print(f"✗ Neptune: 不支持 - {e}")
    except Exception as e:
        print(f"✗ Neptune: 不支持 - {e}")

if __name__ == "__main__":
    success_count, total_tests, status = test_graph_memory_structure()
    test_graph_memory_providers()
    
    print(f"\n📊 最终评估: 图记忆功能{status}")
    
    if success_count >= total_tests * 0.8:
        print("✅ 结论: 图记忆功能在当前环境中可以正常使用")
        sys.exit(0)
    else:
        print("❌ 结论: 图记忆功能存在问题，可能影响正常使用")
        sys.exit(1)