2025/07/31-11:19:02.841933 16 RocksDB version: 8.10.0
2025/07/31-11:19:02.842922 16 Compile date 2023-12-15 13:01:14
2025/07/31-11:19:02.842933 16 DB SUMMARY
2025/07/31-11:19:02.842947 16 Host name (Env):  681d1fb24e9c
2025/07/31-11:19:02.842950 16 DB Session ID:  A10HZFNMYI1ESUQFAAAA
2025/07/31-11:19:02.843123 16 CURRENT file:  CURRENT
2025/07/31-11:19:02.843127 16 IDENTITY file:  IDENTITY
2025/07/31-11:19:02.843135 16 MANIFEST file:  MANIFEST-000603 size: 16344 Bytes
2025/07/31-11:19:02.843138 16 SST files in ./storage/collections/mem0/0/segments/d1c095fa-997b-40c3-a8e6-c0b5efc96824 dir, Total Num: 154, files: 000034.sst 000040.sst 000046.sst 000052.sst 000058.sst 000065.sst 000072.sst 000091.sst 000097.sst 
2025/07/31-11:19:02.843141 16 Write Ahead Log file in ./storage/collections/mem0/0/segments/d1c095fa-997b-40c3-a8e6-c0b5efc96824: 000622.log size: 0 ; 
2025/07/31-11:19:02.843143 16                         Options.error_if_exists: 0
2025/07/31-11:19:02.843145 16                       Options.create_if_missing: 1
2025/07/31-11:19:02.843147 16                         Options.paranoid_checks: 1
2025/07/31-11:19:02.843148 16             Options.flush_verify_memtable_count: 1
2025/07/31-11:19:02.843150 16          Options.compaction_verify_record_count: 1
2025/07/31-11:19:02.843151 16                               Options.track_and_verify_wals_in_manifest: 0
2025/07/31-11:19:02.843153 16        Options.verify_sst_unique_id_in_manifest: 1
2025/07/31-11:19:02.843154 16                                     Options.env: 0x71d8880015b0
2025/07/31-11:19:02.843156 16                                      Options.fs: PosixFileSystem
2025/07/31-11:19:02.843158 16                                Options.info_log: 0x71d88800c410
2025/07/31-11:19:02.843159 16                Options.max_file_opening_threads: 16
2025/07/31-11:19:02.843161 16                              Options.statistics: (nil)
2025/07/31-11:19:02.843162 16                               Options.use_fsync: 0
2025/07/31-11:19:02.843164 16                       Options.max_log_file_size: 1048576
2025/07/31-11:19:02.843166 16                  Options.max_manifest_file_size: 1073741824
2025/07/31-11:19:02.843168 16                   Options.log_file_time_to_roll: 0
2025/07/31-11:19:02.843181 16                       Options.keep_log_file_num: 1
2025/07/31-11:19:02.843183 16                    Options.recycle_log_file_num: 0
2025/07/31-11:19:02.843184 16                         Options.allow_fallocate: 1
2025/07/31-11:19:02.843186 16                        Options.allow_mmap_reads: 0
2025/07/31-11:19:02.843188 16                       Options.allow_mmap_writes: 0
2025/07/31-11:19:02.843190 16                        Options.use_direct_reads: 0
2025/07/31-11:19:02.843191 16                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/31-11:19:02.843193 16          Options.create_missing_column_families: 1
2025/07/31-11:19:02.843194 16                              Options.db_log_dir: 
2025/07/31-11:19:02.843196 16                                 Options.wal_dir: 
2025/07/31-11:19:02.843197 16                Options.table_cache_numshardbits: 6
2025/07/31-11:19:02.843199 16                         Options.WAL_ttl_seconds: 0
2025/07/31-11:19:02.843201 16                       Options.WAL_size_limit_MB: 0
2025/07/31-11:19:02.843202 16                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/31-11:19:02.843204 16             Options.manifest_preallocation_size: 4194304
2025/07/31-11:19:02.843205 16                     Options.is_fd_close_on_exec: 1
2025/07/31-11:19:02.843207 16                   Options.advise_random_on_open: 1
2025/07/31-11:19:02.843208 16                    Options.db_write_buffer_size: 0
2025/07/31-11:19:02.843210 16                    Options.write_buffer_manager: 0x71d88800be90
2025/07/31-11:19:02.843211 16         Options.access_hint_on_compaction_start: 1
2025/07/31-11:19:02.843213 16           Options.random_access_max_buffer_size: 1048576
2025/07/31-11:19:02.843214 16                      Options.use_adaptive_mutex: 0
2025/07/31-11:19:02.843216 16                            Options.rate_limiter: (nil)
2025/07/31-11:19:02.843220 16     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/31-11:19:02.843222 16                       Options.wal_recovery_mode: 0
2025/07/31-11:19:02.843223 16                  Options.enable_thread_tracking: 0
2025/07/31-11:19:02.843225 16                  Options.enable_pipelined_write: 0
2025/07/31-11:19:02.843226 16                  Options.unordered_write: 0
2025/07/31-11:19:02.843227 16         Options.allow_concurrent_memtable_write: 1
2025/07/31-11:19:02.843229 16      Options.enable_write_thread_adaptive_yield: 1
2025/07/31-11:19:02.843230 16             Options.write_thread_max_yield_usec: 100
2025/07/31-11:19:02.843231 16            Options.write_thread_slow_yield_usec: 3
2025/07/31-11:19:02.843233 16                               Options.row_cache: None
2025/07/31-11:19:02.843234 16                              Options.wal_filter: None
2025/07/31-11:19:02.843236 16             Options.avoid_flush_during_recovery: 0
2025/07/31-11:19:02.843237 16             Options.allow_ingest_behind: 0
2025/07/31-11:19:02.843238 16             Options.two_write_queues: 0
2025/07/31-11:19:02.843240 16             Options.manual_wal_flush: 0
2025/07/31-11:19:02.843241 16             Options.wal_compression: 0
2025/07/31-11:19:02.843242 16             Options.atomic_flush: 0
2025/07/31-11:19:02.843244 16             Options.avoid_unnecessary_blocking_io: 0
2025/07/31-11:19:02.843245 16                 Options.persist_stats_to_disk: 0
2025/07/31-11:19:02.843246 16                 Options.write_dbid_to_manifest: 0
2025/07/31-11:19:02.843248 16                 Options.log_readahead_size: 0
2025/07/31-11:19:02.843250 16                 Options.file_checksum_gen_factory: Unknown
2025/07/31-11:19:02.843251 16                 Options.best_efforts_recovery: 0
2025/07/31-11:19:02.843252 16                Options.max_bgerror_resume_count: 2147483647
2025/07/31-11:19:02.843254 16            Options.bgerror_resume_retry_interval: 1000000
2025/07/31-11:19:02.843255 16             Options.allow_data_in_errors: 0
2025/07/31-11:19:02.843257 16             Options.db_host_id: __hostname__
2025/07/31-11:19:02.843258 16             Options.enforce_single_del_contracts: true
2025/07/31-11:19:02.843259 16             Options.max_background_jobs: 2
2025/07/31-11:19:02.843261 16             Options.max_background_compactions: -1
2025/07/31-11:19:02.843263 16             Options.max_subcompactions: 1
2025/07/31-11:19:02.843264 16             Options.avoid_flush_during_shutdown: 0
2025/07/31-11:19:02.843265 16           Options.writable_file_max_buffer_size: 1048576
2025/07/31-11:19:02.843267 16             Options.delayed_write_rate : 16777216
2025/07/31-11:19:02.843268 16             Options.max_total_wal_size: 0
2025/07/31-11:19:02.843269 16             Options.delete_obsolete_files_period_micros: 180000000
2025/07/31-11:19:02.843271 16                   Options.stats_dump_period_sec: 600
2025/07/31-11:19:02.843272 16                 Options.stats_persist_period_sec: 600
2025/07/31-11:19:02.843274 16                 Options.stats_history_buffer_size: 1048576
2025/07/31-11:19:02.843275 16                          Options.max_open_files: 256
2025/07/31-11:19:02.843277 16                          Options.bytes_per_sync: 0
2025/07/31-11:19:02.843278 16                      Options.wal_bytes_per_sync: 0
2025/07/31-11:19:02.843280 16                   Options.strict_bytes_per_sync: 0
2025/07/31-11:19:02.843281 16       Options.compaction_readahead_size: 2097152
2025/07/31-11:19:02.843282 16                  Options.max_background_flushes: -1
2025/07/31-11:19:02.843284 16 Options.daily_offpeak_time_utc: 
2025/07/31-11:19:02.843285 16 Compression algorithms supported:
2025/07/31-11:19:02.843287 16 	kZSTD supported: 0
2025/07/31-11:19:02.843289 16 	kXpressCompression supported: 0
2025/07/31-11:19:02.843291 16 	kBZip2Compression supported: 0
2025/07/31-11:19:02.843293 16 	kZSTDNotFinalCompression supported: 0
2025/07/31-11:19:02.843294 16 	kLZ4Compression supported: 1
2025/07/31-11:19:02.843296 16 	kZlibCompression supported: 0
2025/07/31-11:19:02.843299 16 	kLZ4HCCompression supported: 1
2025/07/31-11:19:02.843302 16 	kSnappyCompression supported: 1
2025/07/31-11:19:02.843304 16 Fast CRC32 supported: Not supported on x86
2025/07/31-11:19:02.843306 16 DMutex implementation: pthread_mutex_t
2025/07/31-11:19:02.843546 16               Options.comparator: leveldb.BytewiseComparator
2025/07/31-11:19:02.843553 16           Options.merge_operator: None
2025/07/31-11:19:02.843554 16        Options.compaction_filter: None
2025/07/31-11:19:02.843568 16        Options.compaction_filter_factory: None
2025/07/31-11:19:02.843570 16  Options.sst_partitioner_factory: None
2025/07/31-11:19:02.843572 16         Options.memtable_factory: SkipListFactory
2025/07/31-11:19:02.843573 16            Options.table_factory: BlockBasedTable
2025/07/31-11:19:02.843608 16            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x71d8880025e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x71d888002950
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/31-11:19:02.843612 16        Options.write_buffer_size: 10485760
2025/07/31-11:19:02.843614 16  Options.max_write_buffer_number: 2
2025/07/31-11:19:02.843616 16          Options.compression: LZ4
2025/07/31-11:19:02.843618 16                  Options.bottommost_compression: Disabled
2025/07/31-11:19:02.843619 16       Options.prefix_extractor: nullptr
2025/07/31-11:19:02.843621 16   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/31-11:19:02.843623 16             Options.num_levels: 7
2025/07/31-11:19:02.843624 16        Options.min_write_buffer_number_to_merge: 1
2025/07/31-11:19:02.843626 16     Options.max_write_buffer_number_to_maintain: 0
2025/07/31-11:19:02.843627 16     Options.max_write_buffer_size_to_maintain: 0
2025/07/31-11:19:02.843629 16            Options.bottommost_compression_opts.window_bits: -14
2025/07/31-11:19:02.843630 16                  Options.bottommost_compression_opts.level: 32767
2025/07/31-11:19:02.843632 16               Options.bottommost_compression_opts.strategy: 0
2025/07/31-11:19:02.843633 16         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.843634 16         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.843636 16         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/31-11:19:02.843637 16                  Options.bottommost_compression_opts.enabled: false
2025/07/31-11:19:02.843638 16         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.843640 16         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.843642 16            Options.compression_opts.window_bits: -14
2025/07/31-11:19:02.843643 16                  Options.compression_opts.level: 32767
2025/07/31-11:19:02.843644 16               Options.compression_opts.strategy: 0
2025/07/31-11:19:02.843646 16         Options.compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.843647 16         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.843651 16         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.843653 16         Options.compression_opts.parallel_threads: 1
2025/07/31-11:19:02.843654 16                  Options.compression_opts.enabled: false
2025/07/31-11:19:02.843656 16         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.843657 16      Options.level0_file_num_compaction_trigger: 4
2025/07/31-11:19:02.843659 16          Options.level0_slowdown_writes_trigger: 20
2025/07/31-11:19:02.843661 16              Options.level0_stop_writes_trigger: 36
2025/07/31-11:19:02.843662 16                   Options.target_file_size_base: 67108864
2025/07/31-11:19:02.843664 16             Options.target_file_size_multiplier: 1
2025/07/31-11:19:02.843666 16                Options.max_bytes_for_level_base: 268435456
2025/07/31-11:19:02.843667 16 Options.level_compaction_dynamic_level_bytes: 1
2025/07/31-11:19:02.843670 16          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/31-11:19:02.843672 16 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/31-11:19:02.843674 16 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/31-11:19:02.843676 16 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/31-11:19:02.843678 16 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/31-11:19:02.843679 16 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/31-11:19:02.843680 16 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/31-11:19:02.843682 16 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/31-11:19:02.843683 16       Options.max_sequential_skip_in_iterations: 8
2025/07/31-11:19:02.843684 16                    Options.max_compaction_bytes: 1677721600
2025/07/31-11:19:02.843686 16   Options.ignore_max_compaction_bytes_for_input: true
2025/07/31-11:19:02.843687 16                        Options.arena_block_size: 1048576
2025/07/31-11:19:02.843689 16   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/31-11:19:02.843690 16   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/31-11:19:02.843691 16                Options.disable_auto_compactions: 0
2025/07/31-11:19:02.843693 16                        Options.compaction_style: kCompactionStyleLevel
2025/07/31-11:19:02.843695 16                          Options.compaction_pri: kMinOverlappingRatio
2025/07/31-11:19:02.843696 16 Options.compaction_options_universal.size_ratio: 1
2025/07/31-11:19:02.843697 16 Options.compaction_options_universal.min_merge_width: 2
2025/07/31-11:19:02.843699 16 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/31-11:19:02.843701 16 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/31-11:19:02.843703 16 Options.compaction_options_universal.compression_size_percent: -1
2025/07/31-11:19:02.843706 16 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/31-11:19:02.843708 16 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/31-11:19:02.843709 16 Options.compaction_options_fifo.allow_compaction: 0
2025/07/31-11:19:02.843716 16                   Options.table_properties_collectors: 
2025/07/31-11:19:02.843718 16                   Options.inplace_update_support: 0
2025/07/31-11:19:02.843719 16                 Options.inplace_update_num_locks: 10000
2025/07/31-11:19:02.843721 16               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/31-11:19:02.843722 16               Options.memtable_whole_key_filtering: 0
2025/07/31-11:19:02.843724 16   Options.memtable_huge_page_size: 0
2025/07/31-11:19:02.843725 16                           Options.bloom_locality: 0
2025/07/31-11:19:02.843726 16                    Options.max_successive_merges: 0
2025/07/31-11:19:02.843728 16                Options.optimize_filters_for_hits: 0
2025/07/31-11:19:02.843729 16                Options.paranoid_file_checks: 0
2025/07/31-11:19:02.843730 16                Options.force_consistency_checks: 1
2025/07/31-11:19:02.843732 16                Options.report_bg_io_stats: 0
2025/07/31-11:19:02.843735 16                               Options.ttl: 2592000
2025/07/31-11:19:02.843737 16          Options.periodic_compaction_seconds: 0
2025/07/31-11:19:02.843738 16                        Options.default_temperature: kUnknown
2025/07/31-11:19:02.843740 16  Options.preclude_last_level_data_seconds: 0
2025/07/31-11:19:02.843741 16    Options.preserve_internal_time_seconds: 0
2025/07/31-11:19:02.843742 16                       Options.enable_blob_files: false
2025/07/31-11:19:02.843745 16                           Options.min_blob_size: 0
2025/07/31-11:19:02.843746 16                          Options.blob_file_size: 268435456
2025/07/31-11:19:02.843748 16                   Options.blob_compression_type: NoCompression
2025/07/31-11:19:02.843749 16          Options.enable_blob_garbage_collection: false
2025/07/31-11:19:02.843751 16      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/31-11:19:02.843754 16 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/31-11:19:02.843756 16          Options.blob_compaction_readahead_size: 0
2025/07/31-11:19:02.843758 16                Options.blob_file_starting_level: 0
2025/07/31-11:19:02.843759 16         Options.experimental_mempurge_threshold: 0.000000
2025/07/31-11:19:02.843764 16            Options.memtable_max_range_deletions: 0
2025/07/31-11:19:02.843884 16               Options.comparator: leveldb.BytewiseComparator
2025/07/31-11:19:02.843889 16           Options.merge_operator: None
2025/07/31-11:19:02.843930 16        Options.compaction_filter: None
2025/07/31-11:19:02.843932 16        Options.compaction_filter_factory: None
2025/07/31-11:19:02.843941 16  Options.sst_partitioner_factory: None
2025/07/31-11:19:02.843943 16         Options.memtable_factory: SkipListFactory
2025/07/31-11:19:02.843944 16            Options.table_factory: BlockBasedTable
2025/07/31-11:19:02.843984 16            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x71d8880025e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x71d888002950
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/31-11:19:02.843988 16        Options.write_buffer_size: 10485760
2025/07/31-11:19:02.843990 16  Options.max_write_buffer_number: 2
2025/07/31-11:19:02.843991 16          Options.compression: LZ4
2025/07/31-11:19:02.843994 16                  Options.bottommost_compression: Disabled
2025/07/31-11:19:02.843995 16       Options.prefix_extractor: nullptr
2025/07/31-11:19:02.843997 16   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/31-11:19:02.844000 16             Options.num_levels: 7
2025/07/31-11:19:02.844001 16        Options.min_write_buffer_number_to_merge: 1
2025/07/31-11:19:02.844003 16     Options.max_write_buffer_number_to_maintain: 0
2025/07/31-11:19:02.844004 16     Options.max_write_buffer_size_to_maintain: 0
2025/07/31-11:19:02.844008 16            Options.bottommost_compression_opts.window_bits: -14
2025/07/31-11:19:02.844010 16                  Options.bottommost_compression_opts.level: 32767
2025/07/31-11:19:02.844011 16               Options.bottommost_compression_opts.strategy: 0
2025/07/31-11:19:02.844014 16         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844016 16         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844018 16         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844019 16                  Options.bottommost_compression_opts.enabled: false
2025/07/31-11:19:02.844021 16         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.844022 16         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.844023 16            Options.compression_opts.window_bits: -14
2025/07/31-11:19:02.844025 16                  Options.compression_opts.level: 32767
2025/07/31-11:19:02.844026 16               Options.compression_opts.strategy: 0
2025/07/31-11:19:02.844028 16         Options.compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844031 16         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844032 16         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.844033 16         Options.compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844035 16                  Options.compression_opts.enabled: false
2025/07/31-11:19:02.844036 16         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.844037 16      Options.level0_file_num_compaction_trigger: 4
2025/07/31-11:19:02.844039 16          Options.level0_slowdown_writes_trigger: 20
2025/07/31-11:19:02.844040 16              Options.level0_stop_writes_trigger: 36
2025/07/31-11:19:02.844044 16                   Options.target_file_size_base: 67108864
2025/07/31-11:19:02.844046 16             Options.target_file_size_multiplier: 1
2025/07/31-11:19:02.844047 16                Options.max_bytes_for_level_base: 268435456
2025/07/31-11:19:02.844049 16 Options.level_compaction_dynamic_level_bytes: 1
2025/07/31-11:19:02.844051 16          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/31-11:19:02.844053 16 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/31-11:19:02.844055 16 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/31-11:19:02.844057 16 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/31-11:19:02.844060 16 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/31-11:19:02.844061 16 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/31-11:19:02.844062 16 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/31-11:19:02.844063 16 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/31-11:19:02.844065 16       Options.max_sequential_skip_in_iterations: 8
2025/07/31-11:19:02.844066 16                    Options.max_compaction_bytes: 1677721600
2025/07/31-11:19:02.844069 16   Options.ignore_max_compaction_bytes_for_input: true
2025/07/31-11:19:02.844071 16                        Options.arena_block_size: 1048576
2025/07/31-11:19:02.844072 16   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/31-11:19:02.844074 16   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/31-11:19:02.844076 16                Options.disable_auto_compactions: 0
2025/07/31-11:19:02.844078 16                        Options.compaction_style: kCompactionStyleLevel
2025/07/31-11:19:02.844080 16                          Options.compaction_pri: kMinOverlappingRatio
2025/07/31-11:19:02.844082 16 Options.compaction_options_universal.size_ratio: 1
2025/07/31-11:19:02.844084 16 Options.compaction_options_universal.min_merge_width: 2
2025/07/31-11:19:02.844085 16 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/31-11:19:02.844087 16 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/31-11:19:02.844089 16 Options.compaction_options_universal.compression_size_percent: -1
2025/07/31-11:19:02.844090 16 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/31-11:19:02.844092 16 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/31-11:19:02.844097 16 Options.compaction_options_fifo.allow_compaction: 0
2025/07/31-11:19:02.844111 16                   Options.table_properties_collectors: 
2025/07/31-11:19:02.844114 16                   Options.inplace_update_support: 0
2025/07/31-11:19:02.844124 16                 Options.inplace_update_num_locks: 10000
2025/07/31-11:19:02.844127 16               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/31-11:19:02.844130 16               Options.memtable_whole_key_filtering: 0
2025/07/31-11:19:02.844131 16   Options.memtable_huge_page_size: 0
2025/07/31-11:19:02.844133 16                           Options.bloom_locality: 0
2025/07/31-11:19:02.844134 16                    Options.max_successive_merges: 0
2025/07/31-11:19:02.844136 16                Options.optimize_filters_for_hits: 0
2025/07/31-11:19:02.844138 16                Options.paranoid_file_checks: 0
2025/07/31-11:19:02.844139 16                Options.force_consistency_checks: 1
2025/07/31-11:19:02.844141 16                Options.report_bg_io_stats: 0
2025/07/31-11:19:02.844142 16                               Options.ttl: 2592000
2025/07/31-11:19:02.844144 16          Options.periodic_compaction_seconds: 0
2025/07/31-11:19:02.844146 16                        Options.default_temperature: kUnknown
2025/07/31-11:19:02.844148 16  Options.preclude_last_level_data_seconds: 0
2025/07/31-11:19:02.844149 16    Options.preserve_internal_time_seconds: 0
2025/07/31-11:19:02.844151 16                       Options.enable_blob_files: false
2025/07/31-11:19:02.844152 16                           Options.min_blob_size: 0
2025/07/31-11:19:02.844154 16                          Options.blob_file_size: 268435456
2025/07/31-11:19:02.844156 16                   Options.blob_compression_type: NoCompression
2025/07/31-11:19:02.844157 16          Options.enable_blob_garbage_collection: false
2025/07/31-11:19:02.844159 16      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/31-11:19:02.844162 16 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/31-11:19:02.844164 16          Options.blob_compaction_readahead_size: 0
2025/07/31-11:19:02.844165 16                Options.blob_file_starting_level: 0
2025/07/31-11:19:02.844167 16         Options.experimental_mempurge_threshold: 0.000000
2025/07/31-11:19:02.844169 16            Options.memtable_max_range_deletions: 0
2025/07/31-11:19:02.844319 16               Options.comparator: leveldb.BytewiseComparator
2025/07/31-11:19:02.844326 16           Options.merge_operator: None
2025/07/31-11:19:02.844328 16        Options.compaction_filter: None
2025/07/31-11:19:02.844340 16        Options.compaction_filter_factory: None
2025/07/31-11:19:02.844343 16  Options.sst_partitioner_factory: None
2025/07/31-11:19:02.844344 16         Options.memtable_factory: SkipListFactory
2025/07/31-11:19:02.844346 16            Options.table_factory: BlockBasedTable
2025/07/31-11:19:02.844376 16            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x71d8880025e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x71d888002950
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/31-11:19:02.844379 16        Options.write_buffer_size: 10485760
2025/07/31-11:19:02.844381 16  Options.max_write_buffer_number: 2
2025/07/31-11:19:02.844383 16          Options.compression: LZ4
2025/07/31-11:19:02.844384 16                  Options.bottommost_compression: Disabled
2025/07/31-11:19:02.844386 16       Options.prefix_extractor: nullptr
2025/07/31-11:19:02.844387 16   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/31-11:19:02.844389 16             Options.num_levels: 7
2025/07/31-11:19:02.844391 16        Options.min_write_buffer_number_to_merge: 1
2025/07/31-11:19:02.844393 16     Options.max_write_buffer_number_to_maintain: 0
2025/07/31-11:19:02.844394 16     Options.max_write_buffer_size_to_maintain: 0
2025/07/31-11:19:02.844396 16            Options.bottommost_compression_opts.window_bits: -14
2025/07/31-11:19:02.844397 16                  Options.bottommost_compression_opts.level: 32767
2025/07/31-11:19:02.844399 16               Options.bottommost_compression_opts.strategy: 0
2025/07/31-11:19:02.844401 16         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844402 16         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844403 16         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844405 16                  Options.bottommost_compression_opts.enabled: false
2025/07/31-11:19:02.844406 16         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.844408 16         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.844409 16            Options.compression_opts.window_bits: -14
2025/07/31-11:19:02.844411 16                  Options.compression_opts.level: 32767
2025/07/31-11:19:02.844413 16               Options.compression_opts.strategy: 0
2025/07/31-11:19:02.844415 16         Options.compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844420 16         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844426 16         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.844427 16         Options.compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844428 16                  Options.compression_opts.enabled: false
2025/07/31-11:19:02.844430 16         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.844432 16      Options.level0_file_num_compaction_trigger: 4
2025/07/31-11:19:02.844434 16          Options.level0_slowdown_writes_trigger: 20
2025/07/31-11:19:02.844435 16              Options.level0_stop_writes_trigger: 36
2025/07/31-11:19:02.844437 16                   Options.target_file_size_base: 67108864
2025/07/31-11:19:02.844438 16             Options.target_file_size_multiplier: 1
2025/07/31-11:19:02.844440 16                Options.max_bytes_for_level_base: 268435456
2025/07/31-11:19:02.844441 16 Options.level_compaction_dynamic_level_bytes: 1
2025/07/31-11:19:02.844444 16          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/31-11:19:02.844446 16 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/31-11:19:02.844449 16 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/31-11:19:02.844450 16 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/31-11:19:02.844452 16 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/31-11:19:02.844453 16 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/31-11:19:02.844455 16 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/31-11:19:02.844456 16 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/31-11:19:02.844458 16       Options.max_sequential_skip_in_iterations: 8
2025/07/31-11:19:02.844460 16                    Options.max_compaction_bytes: 1677721600
2025/07/31-11:19:02.844462 16   Options.ignore_max_compaction_bytes_for_input: true
2025/07/31-11:19:02.844464 16                        Options.arena_block_size: 1048576
2025/07/31-11:19:02.844465 16   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/31-11:19:02.844470 16   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/31-11:19:02.844480 16                Options.disable_auto_compactions: 0
2025/07/31-11:19:02.844482 16                        Options.compaction_style: kCompactionStyleLevel
2025/07/31-11:19:02.844484 16                          Options.compaction_pri: kMinOverlappingRatio
2025/07/31-11:19:02.844486 16 Options.compaction_options_universal.size_ratio: 1
2025/07/31-11:19:02.844491 16 Options.compaction_options_universal.min_merge_width: 2
2025/07/31-11:19:02.844493 16 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/31-11:19:02.844495 16 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/31-11:19:02.844496 16 Options.compaction_options_universal.compression_size_percent: -1
2025/07/31-11:19:02.844498 16 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/31-11:19:02.844499 16 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/31-11:19:02.844501 16 Options.compaction_options_fifo.allow_compaction: 0
2025/07/31-11:19:02.844505 16                   Options.table_properties_collectors: 
2025/07/31-11:19:02.844506 16                   Options.inplace_update_support: 0
2025/07/31-11:19:02.844508 16                 Options.inplace_update_num_locks: 10000
2025/07/31-11:19:02.844510 16               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/31-11:19:02.844514 16               Options.memtable_whole_key_filtering: 0
2025/07/31-11:19:02.844516 16   Options.memtable_huge_page_size: 0
2025/07/31-11:19:02.844518 16                           Options.bloom_locality: 0
2025/07/31-11:19:02.844526 16                    Options.max_successive_merges: 0
2025/07/31-11:19:02.844528 16                Options.optimize_filters_for_hits: 0
2025/07/31-11:19:02.844529 16                Options.paranoid_file_checks: 0
2025/07/31-11:19:02.844530 16                Options.force_consistency_checks: 1
2025/07/31-11:19:02.844532 16                Options.report_bg_io_stats: 0
2025/07/31-11:19:02.844533 16                               Options.ttl: 2592000
2025/07/31-11:19:02.844534 16          Options.periodic_compaction_seconds: 0
2025/07/31-11:19:02.844536 16                        Options.default_temperature: kUnknown
2025/07/31-11:19:02.844537 16  Options.preclude_last_level_data_seconds: 0
2025/07/31-11:19:02.844539 16    Options.preserve_internal_time_seconds: 0
2025/07/31-11:19:02.844540 16                       Options.enable_blob_files: false
2025/07/31-11:19:02.844541 16                           Options.min_blob_size: 0
2025/07/31-11:19:02.844543 16                          Options.blob_file_size: 268435456
2025/07/31-11:19:02.844544 16                   Options.blob_compression_type: NoCompression
2025/07/31-11:19:02.844546 16          Options.enable_blob_garbage_collection: false
2025/07/31-11:19:02.844548 16      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/31-11:19:02.844550 16 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/31-11:19:02.844552 16          Options.blob_compaction_readahead_size: 0
2025/07/31-11:19:02.844553 16                Options.blob_file_starting_level: 0
2025/07/31-11:19:02.844555 16         Options.experimental_mempurge_threshold: 0.000000
2025/07/31-11:19:02.844557 16            Options.memtable_max_range_deletions: 0
2025/07/31-11:19:02.844636 16               Options.comparator: leveldb.BytewiseComparator
2025/07/31-11:19:02.844641 16           Options.merge_operator: None
2025/07/31-11:19:02.844643 16        Options.compaction_filter: None
2025/07/31-11:19:02.844645 16        Options.compaction_filter_factory: None
2025/07/31-11:19:02.844646 16  Options.sst_partitioner_factory: None
2025/07/31-11:19:02.844647 16         Options.memtable_factory: SkipListFactory
2025/07/31-11:19:02.844649 16            Options.table_factory: BlockBasedTable
2025/07/31-11:19:02.844677 16            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x71d8880025e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x71d888002950
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/31-11:19:02.844682 16        Options.write_buffer_size: 10485760
2025/07/31-11:19:02.844685 16  Options.max_write_buffer_number: 2
2025/07/31-11:19:02.844687 16          Options.compression: LZ4
2025/07/31-11:19:02.844688 16                  Options.bottommost_compression: Disabled
2025/07/31-11:19:02.844690 16       Options.prefix_extractor: nullptr
2025/07/31-11:19:02.844691 16   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/31-11:19:02.844693 16             Options.num_levels: 7
2025/07/31-11:19:02.844694 16        Options.min_write_buffer_number_to_merge: 1
2025/07/31-11:19:02.844696 16     Options.max_write_buffer_number_to_maintain: 0
2025/07/31-11:19:02.844697 16     Options.max_write_buffer_size_to_maintain: 0
2025/07/31-11:19:02.844698 16            Options.bottommost_compression_opts.window_bits: -14
2025/07/31-11:19:02.844700 16                  Options.bottommost_compression_opts.level: 32767
2025/07/31-11:19:02.844701 16               Options.bottommost_compression_opts.strategy: 0
2025/07/31-11:19:02.844702 16         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844704 16         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844705 16         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844706 16                  Options.bottommost_compression_opts.enabled: false
2025/07/31-11:19:02.844708 16         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.844709 16         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.844710 16            Options.compression_opts.window_bits: -14
2025/07/31-11:19:02.844712 16                  Options.compression_opts.level: 32767
2025/07/31-11:19:02.844713 16               Options.compression_opts.strategy: 0
2025/07/31-11:19:02.844715 16         Options.compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844717 16         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844719 16         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.844720 16         Options.compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844722 16                  Options.compression_opts.enabled: false
2025/07/31-11:19:02.844723 16         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.844725 16      Options.level0_file_num_compaction_trigger: 4
2025/07/31-11:19:02.844726 16          Options.level0_slowdown_writes_trigger: 20
2025/07/31-11:19:02.844727 16              Options.level0_stop_writes_trigger: 36
2025/07/31-11:19:02.844729 16                   Options.target_file_size_base: 67108864
2025/07/31-11:19:02.844730 16             Options.target_file_size_multiplier: 1
2025/07/31-11:19:02.844733 16                Options.max_bytes_for_level_base: 268435456
2025/07/31-11:19:02.844735 16 Options.level_compaction_dynamic_level_bytes: 1
2025/07/31-11:19:02.844739 16          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/31-11:19:02.844741 16 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/31-11:19:02.844743 16 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/31-11:19:02.844744 16 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/31-11:19:02.844745 16 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/31-11:19:02.844747 16 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/31-11:19:02.844748 16 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/31-11:19:02.844749 16 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/31-11:19:02.844750 16       Options.max_sequential_skip_in_iterations: 8
2025/07/31-11:19:02.844752 16                    Options.max_compaction_bytes: 1677721600
2025/07/31-11:19:02.844754 16   Options.ignore_max_compaction_bytes_for_input: true
2025/07/31-11:19:02.844755 16                        Options.arena_block_size: 1048576
2025/07/31-11:19:02.844756 16   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/31-11:19:02.844758 16   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/31-11:19:02.844760 16                Options.disable_auto_compactions: 0
2025/07/31-11:19:02.844761 16                        Options.compaction_style: kCompactionStyleLevel
2025/07/31-11:19:02.844763 16                          Options.compaction_pri: kMinOverlappingRatio
2025/07/31-11:19:02.844765 16 Options.compaction_options_universal.size_ratio: 1
2025/07/31-11:19:02.844766 16 Options.compaction_options_universal.min_merge_width: 2
2025/07/31-11:19:02.844768 16 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/31-11:19:02.844769 16 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/31-11:19:02.844771 16 Options.compaction_options_universal.compression_size_percent: -1
2025/07/31-11:19:02.844772 16 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/31-11:19:02.844774 16 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/31-11:19:02.844776 16 Options.compaction_options_fifo.allow_compaction: 0
2025/07/31-11:19:02.844779 16                   Options.table_properties_collectors: 
2025/07/31-11:19:02.844781 16                   Options.inplace_update_support: 0
2025/07/31-11:19:02.844784 16                 Options.inplace_update_num_locks: 10000
2025/07/31-11:19:02.844786 16               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/31-11:19:02.844788 16               Options.memtable_whole_key_filtering: 0
2025/07/31-11:19:02.844790 16   Options.memtable_huge_page_size: 0
2025/07/31-11:19:02.844792 16                           Options.bloom_locality: 0
2025/07/31-11:19:02.844793 16                    Options.max_successive_merges: 0
2025/07/31-11:19:02.844794 16                Options.optimize_filters_for_hits: 0
2025/07/31-11:19:02.844795 16                Options.paranoid_file_checks: 0
2025/07/31-11:19:02.844797 16                Options.force_consistency_checks: 1
2025/07/31-11:19:02.844798 16                Options.report_bg_io_stats: 0
2025/07/31-11:19:02.844799 16                               Options.ttl: 2592000
2025/07/31-11:19:02.844801 16          Options.periodic_compaction_seconds: 0
2025/07/31-11:19:02.844802 16                        Options.default_temperature: kUnknown
2025/07/31-11:19:02.844804 16  Options.preclude_last_level_data_seconds: 0
2025/07/31-11:19:02.844805 16    Options.preserve_internal_time_seconds: 0
2025/07/31-11:19:02.844807 16                       Options.enable_blob_files: false
2025/07/31-11:19:02.844809 16                           Options.min_blob_size: 0
2025/07/31-11:19:02.844810 16                          Options.blob_file_size: 268435456
2025/07/31-11:19:02.844811 16                   Options.blob_compression_type: NoCompression
2025/07/31-11:19:02.844813 16          Options.enable_blob_garbage_collection: false
2025/07/31-11:19:02.844815 16      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/31-11:19:02.844818 16 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/31-11:19:02.844820 16          Options.blob_compaction_readahead_size: 0
2025/07/31-11:19:02.844822 16                Options.blob_file_starting_level: 0
2025/07/31-11:19:02.844823 16         Options.experimental_mempurge_threshold: 0.000000
2025/07/31-11:19:02.844825 16            Options.memtable_max_range_deletions: 0
2025/07/31-11:19:02.844891 16               Options.comparator: leveldb.BytewiseComparator
2025/07/31-11:19:02.844894 16           Options.merge_operator: None
2025/07/31-11:19:02.844896 16        Options.compaction_filter: None
2025/07/31-11:19:02.844898 16        Options.compaction_filter_factory: None
2025/07/31-11:19:02.844899 16  Options.sst_partitioner_factory: None
2025/07/31-11:19:02.844901 16         Options.memtable_factory: SkipListFactory
2025/07/31-11:19:02.844902 16            Options.table_factory: BlockBasedTable
2025/07/31-11:19:02.844923 16            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x71d8880025e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x71d888002950
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/31-11:19:02.844927 16        Options.write_buffer_size: 10485760
2025/07/31-11:19:02.844929 16  Options.max_write_buffer_number: 2
2025/07/31-11:19:02.844930 16          Options.compression: LZ4
2025/07/31-11:19:02.844932 16                  Options.bottommost_compression: Disabled
2025/07/31-11:19:02.844934 16       Options.prefix_extractor: nullptr
2025/07/31-11:19:02.844935 16   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/31-11:19:02.844936 16             Options.num_levels: 7
2025/07/31-11:19:02.844938 16        Options.min_write_buffer_number_to_merge: 1
2025/07/31-11:19:02.844939 16     Options.max_write_buffer_number_to_maintain: 0
2025/07/31-11:19:02.844940 16     Options.max_write_buffer_size_to_maintain: 0
2025/07/31-11:19:02.844942 16            Options.bottommost_compression_opts.window_bits: -14
2025/07/31-11:19:02.844945 16                  Options.bottommost_compression_opts.level: 32767
2025/07/31-11:19:02.844946 16               Options.bottommost_compression_opts.strategy: 0
2025/07/31-11:19:02.844949 16         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844950 16         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844952 16         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844953 16                  Options.bottommost_compression_opts.enabled: false
2025/07/31-11:19:02.844964 16         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.845002 16         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.845004 16            Options.compression_opts.window_bits: -14
2025/07/31-11:19:02.845006 16                  Options.compression_opts.level: 32767
2025/07/31-11:19:02.845007 16               Options.compression_opts.strategy: 0
2025/07/31-11:19:02.845009 16         Options.compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.845013 16         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.845072 16         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.845086 16         Options.compression_opts.parallel_threads: 1
2025/07/31-11:19:02.845094 16                  Options.compression_opts.enabled: false
2025/07/31-11:19:02.845096 16         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.845098 16      Options.level0_file_num_compaction_trigger: 4
2025/07/31-11:19:02.845099 16          Options.level0_slowdown_writes_trigger: 20
2025/07/31-11:19:02.845101 16              Options.level0_stop_writes_trigger: 36
2025/07/31-11:19:02.845103 16                   Options.target_file_size_base: 67108864
2025/07/31-11:19:02.845105 16             Options.target_file_size_multiplier: 1
2025/07/31-11:19:02.845106 16                Options.max_bytes_for_level_base: 268435456
2025/07/31-11:19:02.845110 16 Options.level_compaction_dynamic_level_bytes: 1
2025/07/31-11:19:02.845113 16          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/31-11:19:02.845121 16 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/31-11:19:02.845123 16 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/31-11:19:02.845125 16 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/31-11:19:02.845127 16 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/31-11:19:02.845128 16 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/31-11:19:02.845136 16 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/31-11:19:02.845138 16 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/31-11:19:02.845140 16       Options.max_sequential_skip_in_iterations: 8
2025/07/31-11:19:02.845141 16                    Options.max_compaction_bytes: 1677721600
2025/07/31-11:19:02.845143 16   Options.ignore_max_compaction_bytes_for_input: true
2025/07/31-11:19:02.845144 16                        Options.arena_block_size: 1048576
2025/07/31-11:19:02.845146 16   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/31-11:19:02.845147 16   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/31-11:19:02.845149 16                Options.disable_auto_compactions: 0
2025/07/31-11:19:02.845151 16                        Options.compaction_style: kCompactionStyleLevel
2025/07/31-11:19:02.845153 16                          Options.compaction_pri: kMinOverlappingRatio
2025/07/31-11:19:02.845154 16 Options.compaction_options_universal.size_ratio: 1
2025/07/31-11:19:02.845155 16 Options.compaction_options_universal.min_merge_width: 2
2025/07/31-11:19:02.845157 16 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/31-11:19:02.845158 16 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/31-11:19:02.845160 16 Options.compaction_options_universal.compression_size_percent: -1
2025/07/31-11:19:02.845161 16 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/31-11:19:02.845163 16 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/31-11:19:02.845165 16 Options.compaction_options_fifo.allow_compaction: 0
2025/07/31-11:19:02.845169 16                   Options.table_properties_collectors: 
2025/07/31-11:19:02.845170 16                   Options.inplace_update_support: 0
2025/07/31-11:19:02.845172 16                 Options.inplace_update_num_locks: 10000
2025/07/31-11:19:02.845175 16               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/31-11:19:02.845176 16               Options.memtable_whole_key_filtering: 0
2025/07/31-11:19:02.845178 16   Options.memtable_huge_page_size: 0
2025/07/31-11:19:02.845179 16                           Options.bloom_locality: 0
2025/07/31-11:19:02.845180 16                    Options.max_successive_merges: 0
2025/07/31-11:19:02.845182 16                Options.optimize_filters_for_hits: 0
2025/07/31-11:19:02.845190 16                Options.paranoid_file_checks: 0
2025/07/31-11:19:02.845196 16                Options.force_consistency_checks: 1
2025/07/31-11:19:02.845198 16                Options.report_bg_io_stats: 0
2025/07/31-11:19:02.845199 16                               Options.ttl: 2592000
2025/07/31-11:19:02.845201 16          Options.periodic_compaction_seconds: 0
2025/07/31-11:19:02.845202 16                        Options.default_temperature: kUnknown
2025/07/31-11:19:02.845204 16  Options.preclude_last_level_data_seconds: 0
2025/07/31-11:19:02.845205 16    Options.preserve_internal_time_seconds: 0
2025/07/31-11:19:02.845207 16                       Options.enable_blob_files: false
2025/07/31-11:19:02.845208 16                           Options.min_blob_size: 0
2025/07/31-11:19:02.845250 16                          Options.blob_file_size: 268435456
2025/07/31-11:19:02.845255 16                   Options.blob_compression_type: NoCompression
2025/07/31-11:19:02.845257 16          Options.enable_blob_garbage_collection: false
2025/07/31-11:19:02.845259 16      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/31-11:19:02.845261 16 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/31-11:19:02.845263 16          Options.blob_compaction_readahead_size: 0
2025/07/31-11:19:02.845264 16                Options.blob_file_starting_level: 0
2025/07/31-11:19:02.845266 16         Options.experimental_mempurge_threshold: 0.000000
2025/07/31-11:19:02.845267 16            Options.memtable_max_range_deletions: 0
2025/07/31-11:19:02.869720 16 DB pointer 0x71d888012400
