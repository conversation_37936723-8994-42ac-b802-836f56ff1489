2025/07/31-11:19:02.841932 17 RocksDB version: 8.10.0
2025/07/31-11:19:02.842916 17 Compile date 2023-12-15 13:01:14
2025/07/31-11:19:02.842933 17 DB SUMMARY
2025/07/31-11:19:02.842944 17 Host name (Env):  681d1fb24e9c
2025/07/31-11:19:02.842947 17 DB Session ID:  A10HZFNMYI1ESUQFAAAB
2025/07/31-11:19:02.843168 17 CURRENT file:  CURRENT
2025/07/31-11:19:02.843176 17 IDENTITY file:  IDENTITY
2025/07/31-11:19:02.843183 17 MANIFEST file:  MANIFEST-000610 size: 12873 Bytes
2025/07/31-11:19:02.843188 17 SST files in ./storage/collections/mem0/0/segments/6ef6b6fc-7059-4c41-8184-da0474d73c33 dir, Total Num: 139, files: 000034.sst 000040.sst 000046.sst 000053.sst 000060.sst 000066.sst 000072.sst 000198.sst 000199.sst 
2025/07/31-11:19:02.843191 17 Write Ahead Log file in ./storage/collections/mem0/0/segments/6ef6b6fc-7059-4c41-8184-da0474d73c33: 000609.log size: 0 ; 
2025/07/31-11:19:02.843193 17                         Options.error_if_exists: 0
2025/07/31-11:19:02.843195 17                       Options.create_if_missing: 1
2025/07/31-11:19:02.843197 17                         Options.paranoid_checks: 1
2025/07/31-11:19:02.843198 17             Options.flush_verify_memtable_count: 1
2025/07/31-11:19:02.843200 17          Options.compaction_verify_record_count: 1
2025/07/31-11:19:02.843202 17                               Options.track_and_verify_wals_in_manifest: 0
2025/07/31-11:19:02.843204 17        Options.verify_sst_unique_id_in_manifest: 1
2025/07/31-11:19:02.843207 17                                     Options.env: 0x71d8880015b0
2025/07/31-11:19:02.843209 17                                      Options.fs: PosixFileSystem
2025/07/31-11:19:02.843210 17                                Options.info_log: 0x71d89400b290
2025/07/31-11:19:02.843212 17                Options.max_file_opening_threads: 16
2025/07/31-11:19:02.843214 17                              Options.statistics: (nil)
2025/07/31-11:19:02.843215 17                               Options.use_fsync: 0
2025/07/31-11:19:02.843217 17                       Options.max_log_file_size: 1048576
2025/07/31-11:19:02.843218 17                  Options.max_manifest_file_size: 1073741824
2025/07/31-11:19:02.843220 17                   Options.log_file_time_to_roll: 0
2025/07/31-11:19:02.843221 17                       Options.keep_log_file_num: 1
2025/07/31-11:19:02.843223 17                    Options.recycle_log_file_num: 0
2025/07/31-11:19:02.843224 17                         Options.allow_fallocate: 1
2025/07/31-11:19:02.843225 17                        Options.allow_mmap_reads: 0
2025/07/31-11:19:02.843227 17                       Options.allow_mmap_writes: 0
2025/07/31-11:19:02.843228 17                        Options.use_direct_reads: 0
2025/07/31-11:19:02.843229 17                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/31-11:19:02.843231 17          Options.create_missing_column_families: 1
2025/07/31-11:19:02.843232 17                              Options.db_log_dir: 
2025/07/31-11:19:02.843234 17                                 Options.wal_dir: 
2025/07/31-11:19:02.843235 17                Options.table_cache_numshardbits: 6
2025/07/31-11:19:02.843236 17                         Options.WAL_ttl_seconds: 0
2025/07/31-11:19:02.843237 17                       Options.WAL_size_limit_MB: 0
2025/07/31-11:19:02.843239 17                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/31-11:19:02.843240 17             Options.manifest_preallocation_size: 4194304
2025/07/31-11:19:02.843242 17                     Options.is_fd_close_on_exec: 1
2025/07/31-11:19:02.843243 17                   Options.advise_random_on_open: 1
2025/07/31-11:19:02.843244 17                    Options.db_write_buffer_size: 0
2025/07/31-11:19:02.843246 17                    Options.write_buffer_manager: 0x71d89400ace0
2025/07/31-11:19:02.843247 17         Options.access_hint_on_compaction_start: 1
2025/07/31-11:19:02.843249 17           Options.random_access_max_buffer_size: 1048576
2025/07/31-11:19:02.843250 17                      Options.use_adaptive_mutex: 0
2025/07/31-11:19:02.843251 17                            Options.rate_limiter: (nil)
2025/07/31-11:19:02.843257 17     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/31-11:19:02.843259 17                       Options.wal_recovery_mode: 0
2025/07/31-11:19:02.843261 17                  Options.enable_thread_tracking: 0
2025/07/31-11:19:02.843262 17                  Options.enable_pipelined_write: 0
2025/07/31-11:19:02.843263 17                  Options.unordered_write: 0
2025/07/31-11:19:02.843265 17         Options.allow_concurrent_memtable_write: 1
2025/07/31-11:19:02.843266 17      Options.enable_write_thread_adaptive_yield: 1
2025/07/31-11:19:02.843267 17             Options.write_thread_max_yield_usec: 100
2025/07/31-11:19:02.843269 17            Options.write_thread_slow_yield_usec: 3
2025/07/31-11:19:02.843270 17                               Options.row_cache: None
2025/07/31-11:19:02.843271 17                              Options.wal_filter: None
2025/07/31-11:19:02.843273 17             Options.avoid_flush_during_recovery: 0
2025/07/31-11:19:02.843274 17             Options.allow_ingest_behind: 0
2025/07/31-11:19:02.843275 17             Options.two_write_queues: 0
2025/07/31-11:19:02.843278 17             Options.manual_wal_flush: 0
2025/07/31-11:19:02.843279 17             Options.wal_compression: 0
2025/07/31-11:19:02.843280 17             Options.atomic_flush: 0
2025/07/31-11:19:02.843282 17             Options.avoid_unnecessary_blocking_io: 0
2025/07/31-11:19:02.843283 17                 Options.persist_stats_to_disk: 0
2025/07/31-11:19:02.843284 17                 Options.write_dbid_to_manifest: 0
2025/07/31-11:19:02.843286 17                 Options.log_readahead_size: 0
2025/07/31-11:19:02.843287 17                 Options.file_checksum_gen_factory: Unknown
2025/07/31-11:19:02.843289 17                 Options.best_efforts_recovery: 0
2025/07/31-11:19:02.843292 17                Options.max_bgerror_resume_count: 2147483647
2025/07/31-11:19:02.843293 17            Options.bgerror_resume_retry_interval: 1000000
2025/07/31-11:19:02.843295 17             Options.allow_data_in_errors: 0
2025/07/31-11:19:02.843296 17             Options.db_host_id: __hostname__
2025/07/31-11:19:02.843297 17             Options.enforce_single_del_contracts: true
2025/07/31-11:19:02.843299 17             Options.max_background_jobs: 2
2025/07/31-11:19:02.843301 17             Options.max_background_compactions: -1
2025/07/31-11:19:02.843302 17             Options.max_subcompactions: 1
2025/07/31-11:19:02.843304 17             Options.avoid_flush_during_shutdown: 0
2025/07/31-11:19:02.843305 17           Options.writable_file_max_buffer_size: 1048576
2025/07/31-11:19:02.843307 17             Options.delayed_write_rate : 16777216
2025/07/31-11:19:02.843308 17             Options.max_total_wal_size: 0
2025/07/31-11:19:02.843309 17             Options.delete_obsolete_files_period_micros: 180000000
2025/07/31-11:19:02.843311 17                   Options.stats_dump_period_sec: 600
2025/07/31-11:19:02.843312 17                 Options.stats_persist_period_sec: 600
2025/07/31-11:19:02.843314 17                 Options.stats_history_buffer_size: 1048576
2025/07/31-11:19:02.843315 17                          Options.max_open_files: 256
2025/07/31-11:19:02.843316 17                          Options.bytes_per_sync: 0
2025/07/31-11:19:02.843317 17                      Options.wal_bytes_per_sync: 0
2025/07/31-11:19:02.843319 17                   Options.strict_bytes_per_sync: 0
2025/07/31-11:19:02.843320 17       Options.compaction_readahead_size: 2097152
2025/07/31-11:19:02.843321 17                  Options.max_background_flushes: -1
2025/07/31-11:19:02.843323 17 Options.daily_offpeak_time_utc: 
2025/07/31-11:19:02.843324 17 Compression algorithms supported:
2025/07/31-11:19:02.843326 17 	kZSTD supported: 0
2025/07/31-11:19:02.843327 17 	kXpressCompression supported: 0
2025/07/31-11:19:02.843329 17 	kBZip2Compression supported: 0
2025/07/31-11:19:02.843330 17 	kZSTDNotFinalCompression supported: 0
2025/07/31-11:19:02.843332 17 	kLZ4Compression supported: 1
2025/07/31-11:19:02.843333 17 	kZlibCompression supported: 0
2025/07/31-11:19:02.843337 17 	kLZ4HCCompression supported: 1
2025/07/31-11:19:02.843339 17 	kSnappyCompression supported: 1
2025/07/31-11:19:02.843341 17 Fast CRC32 supported: Not supported on x86
2025/07/31-11:19:02.843343 17 DMutex implementation: pthread_mutex_t
2025/07/31-11:19:02.843546 17               Options.comparator: leveldb.BytewiseComparator
2025/07/31-11:19:02.843563 17           Options.merge_operator: None
2025/07/31-11:19:02.843565 17        Options.compaction_filter: None
2025/07/31-11:19:02.843568 17        Options.compaction_filter_factory: None
2025/07/31-11:19:02.843569 17  Options.sst_partitioner_factory: None
2025/07/31-11:19:02.843571 17         Options.memtable_factory: SkipListFactory
2025/07/31-11:19:02.843572 17            Options.table_factory: BlockBasedTable
2025/07/31-11:19:02.843611 17            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x71d8940012b0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x71d8940015e0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/31-11:19:02.843615 17        Options.write_buffer_size: 10485760
2025/07/31-11:19:02.843617 17  Options.max_write_buffer_number: 2
2025/07/31-11:19:02.843619 17          Options.compression: LZ4
2025/07/31-11:19:02.843621 17                  Options.bottommost_compression: Disabled
2025/07/31-11:19:02.843623 17       Options.prefix_extractor: nullptr
2025/07/31-11:19:02.843624 17   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/31-11:19:02.843625 17             Options.num_levels: 7
2025/07/31-11:19:02.843627 17        Options.min_write_buffer_number_to_merge: 1
2025/07/31-11:19:02.843628 17     Options.max_write_buffer_number_to_maintain: 0
2025/07/31-11:19:02.843630 17     Options.max_write_buffer_size_to_maintain: 0
2025/07/31-11:19:02.843631 17            Options.bottommost_compression_opts.window_bits: -14
2025/07/31-11:19:02.843632 17                  Options.bottommost_compression_opts.level: 32767
2025/07/31-11:19:02.843634 17               Options.bottommost_compression_opts.strategy: 0
2025/07/31-11:19:02.843635 17         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.843637 17         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.843638 17         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/31-11:19:02.843640 17                  Options.bottommost_compression_opts.enabled: false
2025/07/31-11:19:02.843641 17         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.843643 17         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.843644 17            Options.compression_opts.window_bits: -14
2025/07/31-11:19:02.843645 17                  Options.compression_opts.level: 32767
2025/07/31-11:19:02.843647 17               Options.compression_opts.strategy: 0
2025/07/31-11:19:02.843649 17         Options.compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.843650 17         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.843653 17         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.843655 17         Options.compression_opts.parallel_threads: 1
2025/07/31-11:19:02.843656 17                  Options.compression_opts.enabled: false
2025/07/31-11:19:02.843658 17         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.843659 17      Options.level0_file_num_compaction_trigger: 4
2025/07/31-11:19:02.843661 17          Options.level0_slowdown_writes_trigger: 20
2025/07/31-11:19:02.843663 17              Options.level0_stop_writes_trigger: 36
2025/07/31-11:19:02.843664 17                   Options.target_file_size_base: 67108864
2025/07/31-11:19:02.843666 17             Options.target_file_size_multiplier: 1
2025/07/31-11:19:02.843668 17                Options.max_bytes_for_level_base: 268435456
2025/07/31-11:19:02.843669 17 Options.level_compaction_dynamic_level_bytes: 1
2025/07/31-11:19:02.843671 17          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/31-11:19:02.843674 17 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/31-11:19:02.843676 17 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/31-11:19:02.843677 17 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/31-11:19:02.843679 17 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/31-11:19:02.843680 17 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/31-11:19:02.843681 17 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/31-11:19:02.843683 17 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/31-11:19:02.843684 17       Options.max_sequential_skip_in_iterations: 8
2025/07/31-11:19:02.843685 17                    Options.max_compaction_bytes: 1677721600
2025/07/31-11:19:02.843687 17   Options.ignore_max_compaction_bytes_for_input: true
2025/07/31-11:19:02.843697 17                        Options.arena_block_size: 1048576
2025/07/31-11:19:02.843700 17   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/31-11:19:02.843702 17   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/31-11:19:02.843703 17                Options.disable_auto_compactions: 0
2025/07/31-11:19:02.843705 17                        Options.compaction_style: kCompactionStyleLevel
2025/07/31-11:19:02.843706 17                          Options.compaction_pri: kMinOverlappingRatio
2025/07/31-11:19:02.843708 17 Options.compaction_options_universal.size_ratio: 1
2025/07/31-11:19:02.843710 17 Options.compaction_options_universal.min_merge_width: 2
2025/07/31-11:19:02.843711 17 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/31-11:19:02.843712 17 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/31-11:19:02.843714 17 Options.compaction_options_universal.compression_size_percent: -1
2025/07/31-11:19:02.843715 17 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/31-11:19:02.843717 17 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/31-11:19:02.843718 17 Options.compaction_options_fifo.allow_compaction: 0
2025/07/31-11:19:02.843723 17                   Options.table_properties_collectors: 
2025/07/31-11:19:02.843725 17                   Options.inplace_update_support: 0
2025/07/31-11:19:02.843726 17                 Options.inplace_update_num_locks: 10000
2025/07/31-11:19:02.843728 17               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/31-11:19:02.843730 17               Options.memtable_whole_key_filtering: 0
2025/07/31-11:19:02.843731 17   Options.memtable_huge_page_size: 0
2025/07/31-11:19:02.843732 17                           Options.bloom_locality: 0
2025/07/31-11:19:02.843734 17                    Options.max_successive_merges: 0
2025/07/31-11:19:02.843735 17                Options.optimize_filters_for_hits: 0
2025/07/31-11:19:02.843736 17                Options.paranoid_file_checks: 0
2025/07/31-11:19:02.843738 17                Options.force_consistency_checks: 1
2025/07/31-11:19:02.843739 17                Options.report_bg_io_stats: 0
2025/07/31-11:19:02.843742 17                               Options.ttl: 2592000
2025/07/31-11:19:02.843744 17          Options.periodic_compaction_seconds: 0
2025/07/31-11:19:02.843746 17                        Options.default_temperature: kUnknown
2025/07/31-11:19:02.843749 17  Options.preclude_last_level_data_seconds: 0
2025/07/31-11:19:02.843752 17    Options.preserve_internal_time_seconds: 0
2025/07/31-11:19:02.843754 17                       Options.enable_blob_files: false
2025/07/31-11:19:02.843755 17                           Options.min_blob_size: 0
2025/07/31-11:19:02.843757 17                          Options.blob_file_size: 268435456
2025/07/31-11:19:02.843758 17                   Options.blob_compression_type: NoCompression
2025/07/31-11:19:02.843760 17          Options.enable_blob_garbage_collection: false
2025/07/31-11:19:02.843761 17      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/31-11:19:02.843763 17 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/31-11:19:02.843765 17          Options.blob_compaction_readahead_size: 0
2025/07/31-11:19:02.843766 17                Options.blob_file_starting_level: 0
2025/07/31-11:19:02.843768 17         Options.experimental_mempurge_threshold: 0.000000
2025/07/31-11:19:02.843769 17            Options.memtable_max_range_deletions: 0
2025/07/31-11:19:02.843888 17               Options.comparator: leveldb.BytewiseComparator
2025/07/31-11:19:02.843914 17           Options.merge_operator: None
2025/07/31-11:19:02.843916 17        Options.compaction_filter: None
2025/07/31-11:19:02.843937 17        Options.compaction_filter_factory: None
2025/07/31-11:19:02.843939 17  Options.sst_partitioner_factory: None
2025/07/31-11:19:02.843941 17         Options.memtable_factory: SkipListFactory
2025/07/31-11:19:02.843942 17            Options.table_factory: BlockBasedTable
2025/07/31-11:19:02.843987 17            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x71d8940012b0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x71d8940015e0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/31-11:19:02.843991 17        Options.write_buffer_size: 10485760
2025/07/31-11:19:02.843994 17  Options.max_write_buffer_number: 2
2025/07/31-11:19:02.843995 17          Options.compression: LZ4
2025/07/31-11:19:02.843997 17                  Options.bottommost_compression: Disabled
2025/07/31-11:19:02.843999 17       Options.prefix_extractor: nullptr
2025/07/31-11:19:02.844000 17   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/31-11:19:02.844002 17             Options.num_levels: 7
2025/07/31-11:19:02.844003 17        Options.min_write_buffer_number_to_merge: 1
2025/07/31-11:19:02.844005 17     Options.max_write_buffer_number_to_maintain: 0
2025/07/31-11:19:02.844008 17     Options.max_write_buffer_size_to_maintain: 0
2025/07/31-11:19:02.844009 17            Options.bottommost_compression_opts.window_bits: -14
2025/07/31-11:19:02.844011 17                  Options.bottommost_compression_opts.level: 32767
2025/07/31-11:19:02.844012 17               Options.bottommost_compression_opts.strategy: 0
2025/07/31-11:19:02.844015 17         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844017 17         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844018 17         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844020 17                  Options.bottommost_compression_opts.enabled: false
2025/07/31-11:19:02.844021 17         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.844023 17         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.844026 17            Options.compression_opts.window_bits: -14
2025/07/31-11:19:02.844028 17                  Options.compression_opts.level: 32767
2025/07/31-11:19:02.844030 17               Options.compression_opts.strategy: 0
2025/07/31-11:19:02.844032 17         Options.compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844033 17         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844034 17         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.844035 17         Options.compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844037 17                  Options.compression_opts.enabled: false
2025/07/31-11:19:02.844038 17         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.844039 17      Options.level0_file_num_compaction_trigger: 4
2025/07/31-11:19:02.844041 17          Options.level0_slowdown_writes_trigger: 20
2025/07/31-11:19:02.844042 17              Options.level0_stop_writes_trigger: 36
2025/07/31-11:19:02.844043 17                   Options.target_file_size_base: 67108864
2025/07/31-11:19:02.844045 17             Options.target_file_size_multiplier: 1
2025/07/31-11:19:02.844046 17                Options.max_bytes_for_level_base: 268435456
2025/07/31-11:19:02.844047 17 Options.level_compaction_dynamic_level_bytes: 1
2025/07/31-11:19:02.844050 17          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/31-11:19:02.844052 17 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/31-11:19:02.844053 17 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/31-11:19:02.844056 17 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/31-11:19:02.844058 17 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/31-11:19:02.844060 17 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/31-11:19:02.844061 17 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/31-11:19:02.844063 17 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/31-11:19:02.844064 17       Options.max_sequential_skip_in_iterations: 8
2025/07/31-11:19:02.844065 17                    Options.max_compaction_bytes: 1677721600
2025/07/31-11:19:02.844067 17   Options.ignore_max_compaction_bytes_for_input: true
2025/07/31-11:19:02.844069 17                        Options.arena_block_size: 1048576
2025/07/31-11:19:02.844071 17   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/31-11:19:02.844073 17   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/31-11:19:02.844074 17                Options.disable_auto_compactions: 0
2025/07/31-11:19:02.844080 17                        Options.compaction_style: kCompactionStyleLevel
2025/07/31-11:19:02.844082 17                          Options.compaction_pri: kMinOverlappingRatio
2025/07/31-11:19:02.844083 17 Options.compaction_options_universal.size_ratio: 1
2025/07/31-11:19:02.844085 17 Options.compaction_options_universal.min_merge_width: 2
2025/07/31-11:19:02.844086 17 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/31-11:19:02.844087 17 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/31-11:19:02.844089 17 Options.compaction_options_universal.compression_size_percent: -1
2025/07/31-11:19:02.844091 17 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/31-11:19:02.844093 17 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/31-11:19:02.844097 17 Options.compaction_options_fifo.allow_compaction: 0
2025/07/31-11:19:02.844114 17                   Options.table_properties_collectors: 
2025/07/31-11:19:02.844124 17                   Options.inplace_update_support: 0
2025/07/31-11:19:02.844126 17                 Options.inplace_update_num_locks: 10000
2025/07/31-11:19:02.844128 17               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/31-11:19:02.844130 17               Options.memtable_whole_key_filtering: 0
2025/07/31-11:19:02.844132 17   Options.memtable_huge_page_size: 0
2025/07/31-11:19:02.844133 17                           Options.bloom_locality: 0
2025/07/31-11:19:02.844135 17                    Options.max_successive_merges: 0
2025/07/31-11:19:02.844136 17                Options.optimize_filters_for_hits: 0
2025/07/31-11:19:02.844138 17                Options.paranoid_file_checks: 0
2025/07/31-11:19:02.844140 17                Options.force_consistency_checks: 1
2025/07/31-11:19:02.844141 17                Options.report_bg_io_stats: 0
2025/07/31-11:19:02.844142 17                               Options.ttl: 2592000
2025/07/31-11:19:02.844144 17          Options.periodic_compaction_seconds: 0
2025/07/31-11:19:02.844146 17                        Options.default_temperature: kUnknown
2025/07/31-11:19:02.844148 17  Options.preclude_last_level_data_seconds: 0
2025/07/31-11:19:02.844150 17    Options.preserve_internal_time_seconds: 0
2025/07/31-11:19:02.844151 17                       Options.enable_blob_files: false
2025/07/31-11:19:02.844153 17                           Options.min_blob_size: 0
2025/07/31-11:19:02.844154 17                          Options.blob_file_size: 268435456
2025/07/31-11:19:02.844159 17                   Options.blob_compression_type: NoCompression
2025/07/31-11:19:02.844161 17          Options.enable_blob_garbage_collection: false
2025/07/31-11:19:02.844163 17      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/31-11:19:02.844165 17 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/31-11:19:02.844167 17          Options.blob_compaction_readahead_size: 0
2025/07/31-11:19:02.844168 17                Options.blob_file_starting_level: 0
2025/07/31-11:19:02.844170 17         Options.experimental_mempurge_threshold: 0.000000
2025/07/31-11:19:02.844171 17            Options.memtable_max_range_deletions: 0
2025/07/31-11:19:02.844319 17               Options.comparator: leveldb.BytewiseComparator
2025/07/31-11:19:02.844335 17           Options.merge_operator: None
2025/07/31-11:19:02.844337 17        Options.compaction_filter: None
2025/07/31-11:19:02.844340 17        Options.compaction_filter_factory: None
2025/07/31-11:19:02.844343 17  Options.sst_partitioner_factory: None
2025/07/31-11:19:02.844345 17         Options.memtable_factory: SkipListFactory
2025/07/31-11:19:02.844346 17            Options.table_factory: BlockBasedTable
2025/07/31-11:19:02.844377 17            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x71d8940012b0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x71d8940015e0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/31-11:19:02.844380 17        Options.write_buffer_size: 10485760
2025/07/31-11:19:02.844382 17  Options.max_write_buffer_number: 2
2025/07/31-11:19:02.844383 17          Options.compression: LZ4
2025/07/31-11:19:02.844385 17                  Options.bottommost_compression: Disabled
2025/07/31-11:19:02.844386 17       Options.prefix_extractor: nullptr
2025/07/31-11:19:02.844387 17   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/31-11:19:02.844389 17             Options.num_levels: 7
2025/07/31-11:19:02.844392 17        Options.min_write_buffer_number_to_merge: 1
2025/07/31-11:19:02.844393 17     Options.max_write_buffer_number_to_maintain: 0
2025/07/31-11:19:02.844394 17     Options.max_write_buffer_size_to_maintain: 0
2025/07/31-11:19:02.844397 17            Options.bottommost_compression_opts.window_bits: -14
2025/07/31-11:19:02.844398 17                  Options.bottommost_compression_opts.level: 32767
2025/07/31-11:19:02.844400 17               Options.bottommost_compression_opts.strategy: 0
2025/07/31-11:19:02.844401 17         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844402 17         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844404 17         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844405 17                  Options.bottommost_compression_opts.enabled: false
2025/07/31-11:19:02.844407 17         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.844408 17         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.844409 17            Options.compression_opts.window_bits: -14
2025/07/31-11:19:02.844412 17                  Options.compression_opts.level: 32767
2025/07/31-11:19:02.844413 17               Options.compression_opts.strategy: 0
2025/07/31-11:19:02.844415 17         Options.compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844418 17         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844419 17         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.844420 17         Options.compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844430 17                  Options.compression_opts.enabled: false
2025/07/31-11:19:02.844432 17         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.844433 17      Options.level0_file_num_compaction_trigger: 4
2025/07/31-11:19:02.844435 17          Options.level0_slowdown_writes_trigger: 20
2025/07/31-11:19:02.844436 17              Options.level0_stop_writes_trigger: 36
2025/07/31-11:19:02.844438 17                   Options.target_file_size_base: 67108864
2025/07/31-11:19:02.844439 17             Options.target_file_size_multiplier: 1
2025/07/31-11:19:02.844441 17                Options.max_bytes_for_level_base: 268435456
2025/07/31-11:19:02.844442 17 Options.level_compaction_dynamic_level_bytes: 1
2025/07/31-11:19:02.844445 17          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/31-11:19:02.844447 17 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/31-11:19:02.844449 17 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/31-11:19:02.844451 17 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/31-11:19:02.844452 17 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/31-11:19:02.844453 17 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/31-11:19:02.844455 17 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/31-11:19:02.844457 17 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/31-11:19:02.844458 17       Options.max_sequential_skip_in_iterations: 8
2025/07/31-11:19:02.844460 17                    Options.max_compaction_bytes: 1677721600
2025/07/31-11:19:02.844462 17   Options.ignore_max_compaction_bytes_for_input: true
2025/07/31-11:19:02.844463 17                        Options.arena_block_size: 1048576
2025/07/31-11:19:02.844465 17   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/31-11:19:02.844470 17   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/31-11:19:02.844476 17                Options.disable_auto_compactions: 0
2025/07/31-11:19:02.844478 17                        Options.compaction_style: kCompactionStyleLevel
2025/07/31-11:19:02.844479 17                          Options.compaction_pri: kMinOverlappingRatio
2025/07/31-11:19:02.844481 17 Options.compaction_options_universal.size_ratio: 1
2025/07/31-11:19:02.844482 17 Options.compaction_options_universal.min_merge_width: 2
2025/07/31-11:19:02.844484 17 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/31-11:19:02.844486 17 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/31-11:19:02.844488 17 Options.compaction_options_universal.compression_size_percent: -1
2025/07/31-11:19:02.844491 17 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/31-11:19:02.844494 17 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/31-11:19:02.844495 17 Options.compaction_options_fifo.allow_compaction: 0
2025/07/31-11:19:02.844499 17                   Options.table_properties_collectors: 
2025/07/31-11:19:02.844502 17                   Options.inplace_update_support: 0
2025/07/31-11:19:02.844503 17                 Options.inplace_update_num_locks: 10000
2025/07/31-11:19:02.844505 17               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/31-11:19:02.844507 17               Options.memtable_whole_key_filtering: 0
2025/07/31-11:19:02.844508 17   Options.memtable_huge_page_size: 0
2025/07/31-11:19:02.844510 17                           Options.bloom_locality: 0
2025/07/31-11:19:02.844518 17                    Options.max_successive_merges: 0
2025/07/31-11:19:02.844522 17                Options.optimize_filters_for_hits: 0
2025/07/31-11:19:02.844523 17                Options.paranoid_file_checks: 0
2025/07/31-11:19:02.844525 17                Options.force_consistency_checks: 1
2025/07/31-11:19:02.844526 17                Options.report_bg_io_stats: 0
2025/07/31-11:19:02.844528 17                               Options.ttl: 2592000
2025/07/31-11:19:02.844530 17          Options.periodic_compaction_seconds: 0
2025/07/31-11:19:02.844531 17                        Options.default_temperature: kUnknown
2025/07/31-11:19:02.844533 17  Options.preclude_last_level_data_seconds: 0
2025/07/31-11:19:02.844534 17    Options.preserve_internal_time_seconds: 0
2025/07/31-11:19:02.844535 17                       Options.enable_blob_files: false
2025/07/31-11:19:02.844536 17                           Options.min_blob_size: 0
2025/07/31-11:19:02.844538 17                          Options.blob_file_size: 268435456
2025/07/31-11:19:02.844539 17                   Options.blob_compression_type: NoCompression
2025/07/31-11:19:02.844540 17          Options.enable_blob_garbage_collection: false
2025/07/31-11:19:02.844542 17      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/31-11:19:02.844544 17 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/31-11:19:02.844546 17          Options.blob_compaction_readahead_size: 0
2025/07/31-11:19:02.844547 17                Options.blob_file_starting_level: 0
2025/07/31-11:19:02.844549 17         Options.experimental_mempurge_threshold: 0.000000
2025/07/31-11:19:02.844553 17            Options.memtable_max_range_deletions: 0
2025/07/31-11:19:02.844638 17               Options.comparator: leveldb.BytewiseComparator
2025/07/31-11:19:02.844642 17           Options.merge_operator: None
2025/07/31-11:19:02.844644 17        Options.compaction_filter: None
2025/07/31-11:19:02.844645 17        Options.compaction_filter_factory: None
2025/07/31-11:19:02.844647 17  Options.sst_partitioner_factory: None
2025/07/31-11:19:02.844648 17         Options.memtable_factory: SkipListFactory
2025/07/31-11:19:02.844650 17            Options.table_factory: BlockBasedTable
2025/07/31-11:19:02.844678 17            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x71d8940012b0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x71d8940015e0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/31-11:19:02.844682 17        Options.write_buffer_size: 10485760
2025/07/31-11:19:02.844685 17  Options.max_write_buffer_number: 2
2025/07/31-11:19:02.844687 17          Options.compression: LZ4
2025/07/31-11:19:02.844689 17                  Options.bottommost_compression: Disabled
2025/07/31-11:19:02.844690 17       Options.prefix_extractor: nullptr
2025/07/31-11:19:02.844692 17   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/31-11:19:02.844693 17             Options.num_levels: 7
2025/07/31-11:19:02.844695 17        Options.min_write_buffer_number_to_merge: 1
2025/07/31-11:19:02.844696 17     Options.max_write_buffer_number_to_maintain: 0
2025/07/31-11:19:02.844697 17     Options.max_write_buffer_size_to_maintain: 0
2025/07/31-11:19:02.844699 17            Options.bottommost_compression_opts.window_bits: -14
2025/07/31-11:19:02.844703 17                  Options.bottommost_compression_opts.level: 32767
2025/07/31-11:19:02.844705 17               Options.bottommost_compression_opts.strategy: 0
2025/07/31-11:19:02.844706 17         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844707 17         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844709 17         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844710 17                  Options.bottommost_compression_opts.enabled: false
2025/07/31-11:19:02.844711 17         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.844713 17         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.844715 17            Options.compression_opts.window_bits: -14
2025/07/31-11:19:02.844717 17                  Options.compression_opts.level: 32767
2025/07/31-11:19:02.844718 17               Options.compression_opts.strategy: 0
2025/07/31-11:19:02.844720 17         Options.compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.844721 17         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.844723 17         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.844724 17         Options.compression_opts.parallel_threads: 1
2025/07/31-11:19:02.844726 17                  Options.compression_opts.enabled: false
2025/07/31-11:19:02.844727 17         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.844728 17      Options.level0_file_num_compaction_trigger: 4
2025/07/31-11:19:02.844730 17          Options.level0_slowdown_writes_trigger: 20
2025/07/31-11:19:02.844731 17              Options.level0_stop_writes_trigger: 36
2025/07/31-11:19:02.844732 17                   Options.target_file_size_base: 67108864
2025/07/31-11:19:02.844734 17             Options.target_file_size_multiplier: 1
2025/07/31-11:19:02.844735 17                Options.max_bytes_for_level_base: 268435456
2025/07/31-11:19:02.844737 17 Options.level_compaction_dynamic_level_bytes: 1
2025/07/31-11:19:02.844755 17          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/31-11:19:02.844758 17 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/31-11:19:02.844759 17 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/31-11:19:02.844761 17 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/31-11:19:02.844762 17 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/31-11:19:02.844765 17 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/31-11:19:02.844767 17 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/31-11:19:02.844768 17 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/31-11:19:02.844770 17       Options.max_sequential_skip_in_iterations: 8
2025/07/31-11:19:02.844771 17                    Options.max_compaction_bytes: 1677721600
2025/07/31-11:19:02.844772 17   Options.ignore_max_compaction_bytes_for_input: true
2025/07/31-11:19:02.844775 17                        Options.arena_block_size: 1048576
2025/07/31-11:19:02.844776 17   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/31-11:19:02.844777 17   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/31-11:19:02.844779 17                Options.disable_auto_compactions: 0
2025/07/31-11:19:02.844781 17                        Options.compaction_style: kCompactionStyleLevel
2025/07/31-11:19:02.844782 17                          Options.compaction_pri: kMinOverlappingRatio
2025/07/31-11:19:02.844784 17 Options.compaction_options_universal.size_ratio: 1
2025/07/31-11:19:02.844785 17 Options.compaction_options_universal.min_merge_width: 2
2025/07/31-11:19:02.844787 17 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/31-11:19:02.844789 17 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/31-11:19:02.844791 17 Options.compaction_options_universal.compression_size_percent: -1
2025/07/31-11:19:02.844792 17 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/31-11:19:02.844793 17 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/31-11:19:02.844795 17 Options.compaction_options_fifo.allow_compaction: 0
2025/07/31-11:19:02.844798 17                   Options.table_properties_collectors: 
2025/07/31-11:19:02.844800 17                   Options.inplace_update_support: 0
2025/07/31-11:19:02.844801 17                 Options.inplace_update_num_locks: 10000
2025/07/31-11:19:02.844803 17               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/31-11:19:02.844805 17               Options.memtable_whole_key_filtering: 0
2025/07/31-11:19:02.844808 17   Options.memtable_huge_page_size: 0
2025/07/31-11:19:02.844809 17                           Options.bloom_locality: 0
2025/07/31-11:19:02.844811 17                    Options.max_successive_merges: 0
2025/07/31-11:19:02.844812 17                Options.optimize_filters_for_hits: 0
2025/07/31-11:19:02.844813 17                Options.paranoid_file_checks: 0
2025/07/31-11:19:02.844816 17                Options.force_consistency_checks: 1
2025/07/31-11:19:02.844818 17                Options.report_bg_io_stats: 0
2025/07/31-11:19:02.844819 17                               Options.ttl: 2592000
2025/07/31-11:19:02.844821 17          Options.periodic_compaction_seconds: 0
2025/07/31-11:19:02.844822 17                        Options.default_temperature: kUnknown
2025/07/31-11:19:02.844824 17  Options.preclude_last_level_data_seconds: 0
2025/07/31-11:19:02.844826 17    Options.preserve_internal_time_seconds: 0
2025/07/31-11:19:02.844828 17                       Options.enable_blob_files: false
2025/07/31-11:19:02.844829 17                           Options.min_blob_size: 0
2025/07/31-11:19:02.844830 17                          Options.blob_file_size: 268435456
2025/07/31-11:19:02.844832 17                   Options.blob_compression_type: NoCompression
2025/07/31-11:19:02.844833 17          Options.enable_blob_garbage_collection: false
2025/07/31-11:19:02.844835 17      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/31-11:19:02.844839 17 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/31-11:19:02.844841 17          Options.blob_compaction_readahead_size: 0
2025/07/31-11:19:02.844842 17                Options.blob_file_starting_level: 0
2025/07/31-11:19:02.844844 17         Options.experimental_mempurge_threshold: 0.000000
2025/07/31-11:19:02.844845 17            Options.memtable_max_range_deletions: 0
2025/07/31-11:19:02.844917 17               Options.comparator: leveldb.BytewiseComparator
2025/07/31-11:19:02.844921 17           Options.merge_operator: None
2025/07/31-11:19:02.844922 17        Options.compaction_filter: None
2025/07/31-11:19:02.844924 17        Options.compaction_filter_factory: None
2025/07/31-11:19:02.844925 17  Options.sst_partitioner_factory: None
2025/07/31-11:19:02.844927 17         Options.memtable_factory: SkipListFactory
2025/07/31-11:19:02.844928 17            Options.table_factory: BlockBasedTable
2025/07/31-11:19:02.844955 17            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x71d8940012b0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x71d8940015e0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/31-11:19:02.844958 17        Options.write_buffer_size: 10485760
2025/07/31-11:19:02.844960 17  Options.max_write_buffer_number: 2
2025/07/31-11:19:02.844961 17          Options.compression: LZ4
2025/07/31-11:19:02.844963 17                  Options.bottommost_compression: Disabled
2025/07/31-11:19:02.844964 17       Options.prefix_extractor: nullptr
2025/07/31-11:19:02.844993 17   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/31-11:19:02.844995 17             Options.num_levels: 7
2025/07/31-11:19:02.844997 17        Options.min_write_buffer_number_to_merge: 1
2025/07/31-11:19:02.844998 17     Options.max_write_buffer_number_to_maintain: 0
2025/07/31-11:19:02.845000 17     Options.max_write_buffer_size_to_maintain: 0
2025/07/31-11:19:02.845001 17            Options.bottommost_compression_opts.window_bits: -14
2025/07/31-11:19:02.845003 17                  Options.bottommost_compression_opts.level: 32767
2025/07/31-11:19:02.845005 17               Options.bottommost_compression_opts.strategy: 0
2025/07/31-11:19:02.845006 17         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.845008 17         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.845013 17         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/31-11:19:02.845026 17                  Options.bottommost_compression_opts.enabled: false
2025/07/31-11:19:02.845028 17         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.845029 17         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.845031 17            Options.compression_opts.window_bits: -14
2025/07/31-11:19:02.845032 17                  Options.compression_opts.level: 32767
2025/07/31-11:19:02.845033 17               Options.compression_opts.strategy: 0
2025/07/31-11:19:02.845034 17         Options.compression_opts.max_dict_bytes: 0
2025/07/31-11:19:02.845039 17         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/31-11:19:02.845041 17         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/31-11:19:02.845043 17         Options.compression_opts.parallel_threads: 1
2025/07/31-11:19:02.845044 17                  Options.compression_opts.enabled: false
2025/07/31-11:19:02.845046 17         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/31-11:19:02.845047 17      Options.level0_file_num_compaction_trigger: 4
2025/07/31-11:19:02.845048 17          Options.level0_slowdown_writes_trigger: 20
2025/07/31-11:19:02.845050 17              Options.level0_stop_writes_trigger: 36
2025/07/31-11:19:02.845051 17                   Options.target_file_size_base: 67108864
2025/07/31-11:19:02.845053 17             Options.target_file_size_multiplier: 1
2025/07/31-11:19:02.845054 17                Options.max_bytes_for_level_base: 268435456
2025/07/31-11:19:02.845056 17 Options.level_compaction_dynamic_level_bytes: 1
2025/07/31-11:19:02.845059 17          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/31-11:19:02.845061 17 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/31-11:19:02.845072 17 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/31-11:19:02.845080 17 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/31-11:19:02.845082 17 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/31-11:19:02.845090 17 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/31-11:19:02.845092 17 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/31-11:19:02.845093 17 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/31-11:19:02.845095 17       Options.max_sequential_skip_in_iterations: 8
2025/07/31-11:19:02.845096 17                    Options.max_compaction_bytes: 1677721600
2025/07/31-11:19:02.845100 17   Options.ignore_max_compaction_bytes_for_input: true
2025/07/31-11:19:02.845103 17                        Options.arena_block_size: 1048576
2025/07/31-11:19:02.845104 17   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/31-11:19:02.845105 17   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/31-11:19:02.845107 17                Options.disable_auto_compactions: 0
2025/07/31-11:19:02.845109 17                        Options.compaction_style: kCompactionStyleLevel
2025/07/31-11:19:02.845111 17                          Options.compaction_pri: kMinOverlappingRatio
2025/07/31-11:19:02.845112 17 Options.compaction_options_universal.size_ratio: 1
2025/07/31-11:19:02.845113 17 Options.compaction_options_universal.min_merge_width: 2
2025/07/31-11:19:02.845124 17 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/31-11:19:02.845126 17 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/31-11:19:02.845128 17 Options.compaction_options_universal.compression_size_percent: -1
2025/07/31-11:19:02.845129 17 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/31-11:19:02.845131 17 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/31-11:19:02.845133 17 Options.compaction_options_fifo.allow_compaction: 0
2025/07/31-11:19:02.845137 17                   Options.table_properties_collectors: 
2025/07/31-11:19:02.845139 17                   Options.inplace_update_support: 0
2025/07/31-11:19:02.845140 17                 Options.inplace_update_num_locks: 10000
2025/07/31-11:19:02.845142 17               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/31-11:19:02.845144 17               Options.memtable_whole_key_filtering: 0
2025/07/31-11:19:02.845145 17   Options.memtable_huge_page_size: 0
2025/07/31-11:19:02.845146 17                           Options.bloom_locality: 0
2025/07/31-11:19:02.845148 17                    Options.max_successive_merges: 0
2025/07/31-11:19:02.845149 17                Options.optimize_filters_for_hits: 0
2025/07/31-11:19:02.845150 17                Options.paranoid_file_checks: 0
2025/07/31-11:19:02.845155 17                Options.force_consistency_checks: 1
2025/07/31-11:19:02.845156 17                Options.report_bg_io_stats: 0
2025/07/31-11:19:02.845158 17                               Options.ttl: 2592000
2025/07/31-11:19:02.845159 17          Options.periodic_compaction_seconds: 0
2025/07/31-11:19:02.845162 17                        Options.default_temperature: kUnknown
2025/07/31-11:19:02.845165 17  Options.preclude_last_level_data_seconds: 0
2025/07/31-11:19:02.845166 17    Options.preserve_internal_time_seconds: 0
2025/07/31-11:19:02.845168 17                       Options.enable_blob_files: false
2025/07/31-11:19:02.845169 17                           Options.min_blob_size: 0
2025/07/31-11:19:02.845171 17                          Options.blob_file_size: 268435456
2025/07/31-11:19:02.845173 17                   Options.blob_compression_type: NoCompression
2025/07/31-11:19:02.845174 17          Options.enable_blob_garbage_collection: false
2025/07/31-11:19:02.845178 17      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/31-11:19:02.845180 17 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/31-11:19:02.845182 17          Options.blob_compaction_readahead_size: 0
2025/07/31-11:19:02.845195 17                Options.blob_file_starting_level: 0
2025/07/31-11:19:02.845197 17         Options.experimental_mempurge_threshold: 0.000000
2025/07/31-11:19:02.845199 17            Options.memtable_max_range_deletions: 0
2025/07/31-11:19:02.867414 17 DB pointer 0x71d894011240
