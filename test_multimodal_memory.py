#!/usr/bin/env python3
"""
测试Mem0多模态记忆功能
包括图像处理和多模态内容的记忆创建
"""

import requests
import json
import time
import uuid
import base64
import io
from PIL import Image, ImageDraw, ImageFont

API_BASE = "http://localhost:8000"

def create_test_image():
    """创建一个测试图像"""
    # 创建一个简单的测试图像
    img = Image.new('RGB', (300, 200), color=(135, 206, 235))  # 天蓝色背景
    draw = ImageDraw.Draw(img)
    
    # 绘制一些内容
    draw.rectangle([50, 50, 250, 150], fill=(255, 255, 255), outline=(0, 0, 0), width=2)
    
    try:
        # 尝试使用默认字体
        font = ImageFont.load_default()
        draw.text((60, 70), "Test Document", fill=(0, 0, 0), font=font)
        draw.text((60, 100), "Meeting Notes", fill=(0, 0, 0), font=font)
        draw.text((60, 120), "2024-07-31", fill=(0, 0, 0), font=font)
    except:
        # 如果字体有问题，只绘制简单文本
        draw.text((60, 70), "Test Document", fill=(0, 0, 0))
        draw.text((60, 100), "Meeting Notes", fill=(0, 0, 0))
        draw.text((60, 120), "2024-07-31", fill=(0, 0, 0))
    
    # 转换为base64
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
    
    return img_base64

def test_multimodal_memory():
    """测试多模态记忆功能"""
    print("🖼️ 测试Mem0多模态记忆功能")
    print("=" * 60)
    
    test_user = f"multimodal_test_{str(uuid.uuid4())[:8]}"
    
    # 1. 测试基本多模态记忆（文本 + 图像描述）
    print(f"\n=== 测试用户: {test_user} ===")
    print("1. 测试文本多模态记忆")
    
    try:
        # 创建测试图像
        test_image_b64 = create_test_image()
        
        # 构建包含图像的消息
        multimodal_message = {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "I just attended an important business meeting. Here's a photo of my meeting notes:"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{test_image_b64}"
                    }
                }
            ]
        }
        
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [multimodal_message],
                "user_id": test_user
            },
            timeout=60  # 增加超时时间，因为图像处理可能需要更长时间
        )
        
        print(f"多模态记忆创建响应状态: {response.status_code}")
        
        if response.status_code == 200:
            memories_created = response.json()
            print(f"创建的记忆数量: {len(memories_created)}")
            
            if memories_created:
                print("创建的记忆详情:")
                for i, memory in enumerate(memories_created):
                    print(f"  记忆 {i+1}: {memory.get('memory', '')}")
                    print(f"  事件: {memory.get('event', '')}")
                    print(f"  ID: {memory.get('id', '')}")
            else:
                print("  没有创建记忆（可能因为多模态功能未启用或配置问题）")
            
            # 等待处理完成
            time.sleep(3)
            
            # 获取用户的所有记忆
            get_response = requests.get(
                f"{API_BASE}/v1/memories/",
                params={"user_id": test_user, "limit": 50},
                timeout=10
            )
            
            if get_response.status_code == 200:
                all_memories = get_response.json()
                print(f"\n用户记忆总数: {len(all_memories)}")
                
                has_image_related = False
                for i, memory in enumerate(all_memories):
                    memory_text = memory.get('memory', '').lower()
                    categories = memory.get('metadata', {}).get('categories', [])
                    
                    print(f"记忆 {i+1}: {memory.get('memory', '')} -> Categories: {categories}")
                    
                    # 检查是否包含图像相关内容
                    image_keywords = ['meeting', 'notes', 'document', 'photo', 'image']
                    if any(keyword in memory_text for keyword in image_keywords):
                        has_image_related = True
                        print(f"  ✓ 发现图像相关内容")
                
                if has_image_related:
                    print("✅ 多模态记忆功能工作正常!")
                else:
                    print("⚠️ 未发现明显的图像相关记忆内容")
            else:
                print(f"获取记忆失败: {get_response.status_code}")
                
        else:
            print(f"多模态记忆创建失败: {response.status_code}")
            print(f"错误信息: {response.text[:300]}")
            
    except Exception as e:
        print(f"多模态测试异常: {e}")
    
    # 2. 测试纯文本但描述视觉内容的记忆
    print(f"\n2. 测试描述性视觉内容记忆")
    
    try:
        visual_user = f"visual_test_{str(uuid.uuid4())[:8]}"
        
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{
                    "role": "user", 
                    "content": "I saw a beautiful sunset at the beach today. The sky was painted with vibrant oranges and pinks, and I took several photos to remember the moment."
                }],
                "user_id": visual_user
            },
            timeout=30
        )
        
        print(f"视觉描述记忆创建响应状态: {response.status_code}")
        
        if response.status_code == 200:
            memories_created = response.json()
            print(f"创建的记忆数量: {len(memories_created)}")
            
            time.sleep(2)
            
            # 获取记忆查看是否正确分类
            get_response = requests.get(
                f"{API_BASE}/v1/memories/",
                params={"user_id": visual_user, "limit": 50}
            )
            
            if get_response.status_code == 200:
                all_memories = get_response.json()
                print(f"视觉描述记忆总数: {len(all_memories)}")
                
                for i, memory in enumerate(all_memories):
                    categories = memory.get('metadata', {}).get('categories', [])
                    print(f"记忆 {i+1}: {memory.get('memory', '')} -> Categories: {categories}")
                    
                    # 检查是否合理分类
                    visual_categories = ['entertainment', 'hobbies', 'personal_details', 'misc']
                    found_visual_cats = [cat for cat in visual_categories if cat in categories]
                    if found_visual_cats:
                        print(f"  ✓ 发现合理的视觉相关分类: {found_visual_cats}")
                
                print("✅ 视觉描述记忆功能正常!")
            else:
                print(f"获取视觉描述记忆失败: {get_response.status_code}")
        else:
            print(f"视觉描述记忆创建失败: {response.text[:200]}")
            
    except Exception as e:
        print(f"视觉描述测试异常: {e}")
    
    # 3. 测试配置检查
    print(f"\n3. 检查多模态配置")
    
    try:
        # 尝试获取系统信息或配置（如果有相关端点）
        # 这里我们通过创建一个明确包含图像的请求来测试
        config_user = f"config_test_{str(uuid.uuid4())[:8]}"
        
        test_image_b64 = create_test_image()
        
        config_test_message = {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "Testing multimodal capabilities"
                },
                {
                    "type": "image_url", 
                    "image_url": {
                        "url": f"data:image/png;base64,{test_image_b64}",
                        "detail": "high"
                    }
                }
            ]
        }
        
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [config_test_message],
                "user_id": config_user
            },
            timeout=60
        )
        
        if response.status_code == 200:
            print("✅ 多模态配置似乎可用")
        elif response.status_code == 400:
            print("⚠️ 多模态功能可能未启用或配置不正确")
            print(f"   错误详情: {response.text[:200]}")
        else:
            print(f"❓ 多模态配置状态未知: {response.status_code}")
            
    except Exception as e:
        print(f"配置检查异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 多模态记忆功能测试完成!")

if __name__ == "__main__":
    test_multimodal_memory()