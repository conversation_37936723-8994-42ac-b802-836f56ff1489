# OpenMemory UI API端点支持文档

## 1. 文档概述

本文档描述了为支持OpenMemory UI界面开发所需的API端点规范。基于现有Mem0 Server架构，补充UI管理功能所需的关键端点。

**最后更新时间**: 2025-01-30  
**实现状态**: ✅ 所有P0-P2优先级API已完成实现，包括用户管理增强API

## 2. 现有API端点分析

### 2.1 已实现的核心端点
```
✅ POST /v1/memories/           - 创建记忆
✅ GET  /v1/memories/           - 获取记忆列表
✅ GET  /v1/memories/{id}/      - 获取单个记忆
✅ PUT  /v1/memories/{id}/      - 更新记忆
✅ DELETE /v1/memories/{id}/    - 删除记忆
✅ DELETE /v1/memories/         - 删除所有记忆
✅ POST /v1/memories/search/    - 搜索记忆
✅ PUT  /v1/batch/              - 批量更新记忆
✅ DELETE /v1/batch/            - 批量删除记忆
✅ POST /v1/exports/            - 记忆导出
✅ POST /v1/feedback/           - 提交反馈
✅ GET  /health                 - 健康检查
✅ GET  /cache/status           - 缓存状态
✅ POST /cache/clear            - 清除缓存
```

### 2.2 新增UI管理端点（已完成实现）
```
✅ GET  /v1/stats                    - 系统统计数据
✅ GET  /v1/activities               - 操作活动日志
✅ GET  /v1/admin/dashboard          - 统一管理面板数据
✅ GET  /v1/users                    - 用户管理
✅ GET  /v1/users/{user_id}/stats    - 用户统计详情
✅ DELETE /v1/users/{user_id}        - 删除用户
✅ POST /v1/users                    - 创建用户
✅ PUT  /v1/users/{user_id}          - 更新用户信息
✅ GET  /v1/users/{user_id}/analytics - 用户分析数据
✅ POST /v1/users/{user_id}/export   - 导出用户数据
✅ POST /v1/users/batch              - 批量用户操作
✅ GET  /v1/graph/entities           - 图实体管理
✅ GET  /v1/graph/relationships      - 图关系管理
✅ POST /v1/graph/entities           - 创建图实体
✅ PUT  /v1/graph/entities/{id}      - 更新图实体
✅ DELETE /v1/graph/entities/{id}    - 删除图实体
✅ POST /v1/graph/relationships      - 创建图关系
✅ PUT  /v1/graph/relationships/{id} - 更新图关系
✅ DELETE /v1/graph/relationships/{id} - 删除图关系
✅ GET  /v1/graph/stats              - 图统计数据
✅ GET  /v1/graph/visualization/{id} - 图可视化数据
✅ POST /v1/graph/search             - 图搜索功能
```

## 3. API端点详细规范

### 3.1 统计数据API ✅

#### GET /v1/stats
**用途**：为Dashboard统计面板提供系统统计数据

**查询参数**：
```typescript
interface StatsQueryParams {
  user_id?: string;           // 可选，获取特定用户统计
  time_range?: '1h' | '24h' | '7d' | '30d';  // 时间范围，默认24h
}
```

**响应格式**：
```typescript
interface StatsResponse {
  // 基础统计（替换后的指标）
  total_memories: number;      // 总记忆数
  total_users: number;         // 总用户数  
  search_events: number;       // 检索事件数量（替换平均响应时间）
  add_events: number;          // 添加事件数量（替换活跃用户数）
  
  // Graph Memory统计
  graph_memories: number;      // 图记忆数量
  entities_count: number;      // 实体总数
  relationships_count: number; // 关系总数
  graph_density: number;       // 图密度 (0-1)
  
  // 时间统计
  last_updated: string;        // 最后更新时间
  time_range: string;          // 统计时间范围
}
```

**实现状态**: ✅ 已完成  
**标签**: `UI-STATS`

### 3.2 活动日志API ✅

#### GET /v1/activities
**用途**：为Activity时间线组件提供操作历史记录

**查询参数**：
```typescript
interface ActivitiesQueryParams {
  user_id?: string;                    // 可选，筛选特定用户
  limit?: number;                      // 返回数量限制，默认50，最大200
  offset?: number;                     // 分页偏移，默认0
  operation_type?: 'SEARCH' | 'ADD' | 'UPDATE' | 'DELETE' | 'GRAPH_CREATE'; // 操作类型筛选
  start_time?: string;                 // 开始时间 (ISO格式)
  end_time?: string;                   // 结束时间 (ISO格式)
}
```

**响应格式**：
```typescript
interface Activity {
  id: string;                          // 活动记录ID
  timestamp: string;                   // 操作时间戳 (ISO格式)
  operation: 'SEARCH' | 'ADD' | 'UPDATE' | 'DELETE' | 'GRAPH_CREATE';
  details: string;                     // 操作详情描述
  response_time?: string;              // 响应时间（如"45ms"）
  status: 'success' | 'error' | 'pending';
  user_id?: string;                    // 用户ID
  memory_id?: string;                  // 相关记忆ID
  metadata?: Record<string, any>;      // 额外元数据
}

interface ActivitiesResponse {
  activities: Activity[];
  total: number;                       // 总记录数
  has_more: boolean;                   // 是否有更多数据
  time_range: {
    start: string;
    end: string;
  };
}
```

**实现状态**: ✅ 已完成  
**标签**: `UI-ACTIVITIES`

### 3.3 用户管理API ✅

#### GET /v1/users
**用途**：获取用户列表和基础统计信息

**查询参数**：
```typescript
interface UsersQueryParams {
  limit?: number;                      // 返回数量限制，默认100，最大500
  offset?: number;                     // 分页偏移，默认0
  include_stats?: boolean;             // 是否包含统计信息，默认true
  time_range?: '1h' | '24h' | '7d' | '30d'; // 活动统计时间范围
}
```

**响应格式**：
```typescript
interface UserInfo {
  user_id: string;
  total_memories: number;
  first_memory?: string;
  last_activity?: string;
  memory_categories: string[];
  agents_used: string[];
  runs_used: string[];
  agents_count: number;
  runs_count: number;
  search_events?: number;              // 如果include_stats=true
  add_events?: number;                 // 如果include_stats=true
}

interface UsersResponse {
  users: UserInfo[];
  total: number;
  has_more: boolean;
  pagination: {
    limit: number;
    offset: number;
    current_page: number;
    total_pages: number;
  };
  time_range?: string;
}
```

**实现状态**: ✅ 已完成  
**标签**: `USER-MANAGEMENT`

#### GET /v1/users/{user_id}/stats ✅
**用途**：获取特定用户的详细统计信息

**响应格式**：
```typescript
interface UserStatsResponse {
  user_id: string;
  total_memories: number;
  search_events: number;
  add_events: number;
  graph_memories: number;
  agents_count: number;
  runs_count: number;
  avg_memories_per_agent: number;
  avg_memories_per_run: number;
  activity_score: number;              // 活跃度评分
  time_range: string;
  // ... 其他统计字段
}
```

**实现状态**: ✅ 已完成  
**标签**: `USER-MANAGEMENT`

#### DELETE /v1/users/{user_id} ✅
**用途**：删除用户及其所有相关数据

**响应格式**：
```typescript
interface DeleteUserResponse {
  message: string;
  user_id: string;
  deleted_counts: {
    memories: number;
    activities: number;
    graph_data: number;
  };
  deleted_at: string;
  warning: string;
}
```

**实现状态**: ✅ 已完成  
**标签**: `USER-MANAGEMENT`

### 3.4 用户管理增强API ✅

#### POST /v1/users
**用途**：创建新用户

**请求格式**：
```typescript
interface UserCreateRequest {
  user_id: string;
  name?: string;
  metadata?: Record<string, any>;
}
```

**实现状态**: ✅ 已完成  
**标签**: `USER-MANAGEMENT`

#### PUT /v1/users/{user_id}
**用途**：更新用户信息

**请求格式**：
```typescript
interface UserUpdateRequest {
  name?: string;
  metadata?: Record<string, any>;
}
```

**实现状态**: ✅ 已完成  
**标签**: `USER-MANAGEMENT`

#### GET /v1/users/{user_id}/analytics
**用途**：获取用户分析数据

**查询参数**：
```typescript
interface UserAnalyticsParams {
  days?: number;                       // 分析天数，默认30，最大365
}
```

**响应格式**：
```typescript
interface UserAnalyticsResponse {
  user_id: string;
  total_memories: number;
  memories_by_category: Record<string, number>;
  activity_timeline: Array<{
    date: string;
    memory_count: number;
  }>;
  most_active_days: string[];
  memory_growth_trend: 'increasing' | 'decreasing' | 'stable';
  analytics_period: string;
  generated_at: string;
}
```

**实现状态**: ✅ 已完成  
**标签**: `USER-MANAGEMENT`

#### POST /v1/users/{user_id}/export
**用途**：导出用户数据

**响应格式**：
```typescript
interface UserExportResponse {
  export_id: string;
  user_id: string;
  export_data: {
    export_metadata: {
      user_id: string;
      export_date: string;
      export_version: string;
      total_memories: number;
    };
    analytics: UserAnalyticsResponse;
    memories: Mem0Memory[];
    export_summary: {
      data_types: string[];
      memory_count: number;
      date_range: {
        earliest: string;
        latest: string;
      };
    };
  };
  created_at: string;
  message: string;
}
```

**实现状态**: ✅ 已完成  
**标签**: `USER-MANAGEMENT`

#### POST /v1/users/batch
**用途**：批量用户操作

**请求格式**：
```typescript
interface BatchUserOperation {
  operation: 'delete' | 'export' | 'archive';
  user_ids: string[];                  // 最多100个用户
  options?: Record<string, any>;
}
```

**响应格式**：
```typescript
interface BatchUserOperationResponse {
  batch_id: string;
  operation: string;
  total_users: number;
  successful_operations: number;
  failed_operations: number;
  results: Array<{
    user_id: string;
    operation: string;
    status: 'success' | 'failed';
    result?: any;
    error?: string;
  }>;
  completed_at: string;
  message: string;
}
```

**实现状态**: ✅ 已完成  
**标签**: `USER-MANAGEMENT`

### 3.5 统一管理面板API ✅

#### GET /v1/admin/dashboard
**用途**：一次性获取Dashboard所需的所有数据，减少网络请求

**查询参数**：
```typescript
interface DashboardQueryParams {
  user_id?: string;                    // 可选，用户特定的仪表板
  time_range?: '1h' | '24h' | '7d' | '30d'; // 统计时间范围
}
```

**响应格式**：
```typescript
interface DashboardResponse {
  stats: StatsResponse;
  recent_activities: Activity[];       // 最近10条活动
  quick_actions: Array<{
    id: string;
    label: string;
    icon: string;
    primary?: boolean;
  }>;
  last_updated: string;
}
```

**实现状态**: ✅ 已完成  
**标签**: `UI-DASHBOARD`

## 4. Graph Memory专用API增强 ✅

### 4.1 图实体管理 ✅

#### GET /v1/graph/entities
**用途**：获取图实体列表

**查询参数**：
```typescript
interface GraphEntitiesParams {
  user_id?: string;
  entity_type?: string;
  limit?: number;                      // 默认100，最大500
}
```

**响应格式**：
```typescript
interface GraphEntitiesResponse {
  entities: Array<{
    id: string;
    label: string;
    type: string;
    properties: Record<string, any>;
    created_at: string;
    user_id?: string;
  }>;
  total: number;
  has_more: boolean;
  filters: {
    user_id?: string;
    entity_type?: string;
    limit: number;
  };
}
```

**实现状态**: ✅ 已完成  
**标签**: `GRAPH-MANAGEMENT`

#### POST /v1/graph/entities
**用途**：创建图实体

**请求格式**：
```typescript
interface GraphEntityCreate {
  label: string;
  type: string;
  properties?: Record<string, any>;
  user_id?: string;
  agent_id?: string;
  run_id?: string;
}
```

**实现状态**: ✅ 已完成  
**标签**: `GRAPH-MANAGEMENT`

#### PUT /v1/graph/entities/{entity_id}
**用途**：更新图实体

**请求格式**：
```typescript
interface GraphEntityUpdate {
  label?: string;
  type?: string;
  properties?: Record<string, any>;
}
```

**实现状态**: ✅ 已完成  
**标签**: `GRAPH-MANAGEMENT`

#### DELETE /v1/graph/entities/{entity_id}
**用途**：删除图实体及其关联关系

**实现状态**: ✅ 已完成  
**标签**: `GRAPH-MANAGEMENT`

### 4.2 图关系管理 ✅

#### GET /v1/graph/relationships
**用途**：获取图关系列表

**查询参数**：
```typescript
interface GraphRelationshipsParams {
  user_id?: string;
  source_entity?: string;
  target_entity?: string;
  limit?: number;                      // 默认100，最大500
}
```

**响应格式**：
```typescript
interface GraphRelationshipsResponse {
  relationships: Array<{
    id: string;
    source_entity: string;
    target_entity: string;
    relationship_type: string;
    properties: Record<string, any>;
    created_at: string;
    user_id?: string;
  }>;
  total: number;
  has_more: boolean;
  filters: {
    user_id?: string;
    source_entity?: string;
    target_entity?: string;
    limit: number;
  };
}
```

**实现状态**: ✅ 已完成  
**标签**: `GRAPH-MANAGEMENT`

#### POST /v1/graph/relationships
**用途**：创建图关系

**请求格式**：
```typescript
interface GraphRelationshipCreate {
  source_entity: string;
  target_entity: string;
  relationship_type: string;
  properties?: Record<string, any>;
  weight?: number;                     // 0.0-1.0
  user_id?: string;
  agent_id?: string;
  run_id?: string;
}
```

**实现状态**: ✅ 已完成  
**标签**: `GRAPH-MANAGEMENT`

#### PUT /v1/graph/relationships/{relationship_id}
**用途**：更新图关系

**请求格式**：
```typescript
interface GraphRelationshipUpdate {
  relationship_type?: string;
  properties?: Record<string, any>;
  weight?: number;
}
```

**实现状态**: ✅ 已完成  
**标签**: `GRAPH-MANAGEMENT`

#### DELETE /v1/graph/relationships/{relationship_id}
**用途**：删除图关系

**实现状态**: ✅ 已完成  
**标签**: `GRAPH-MANAGEMENT`

### 4.3 图统计和可视化API ✅

#### GET /v1/graph/stats
**用途**：获取图统计数据

**查询参数**：
```typescript
interface GraphStatsParams {
  user_id?: string;
  time_range?: '1h' | '24h' | '7d' | '30d';
}
```

**响应格式**：
```typescript
interface GraphStatsResponse {
  total_entities: number;
  total_relationships: number;
  graph_density: number;
  entity_types: Record<string, number>;
  relationship_types: Record<string, number>;
  average_relationship_weight: number;
  active_users: number;
  recent_activity: {
    time_range: string;
    new_entities: number;
    new_relationships: number;
  };
  connectivity_stats: {
    nodes: number;
    edges: number;
    density: number;
    avg_degree: number;
  };
  last_updated: string;
}
```

**实现状态**: ✅ 已完成  
**标签**: `GRAPH-MANAGEMENT`

#### GET /v1/graph/visualization/{memory_id}
**用途**：获取图可视化数据

**查询参数**：
```typescript
interface GraphVisualizationParams {
  layout?: 'force' | 'hierarchical' | 'circular'; // 默认'force'
  include_metadata?: boolean;          // 默认true
}
```

**响应格式**：
```typescript
interface GraphVisualizationResponse {
  nodes: Array<{
    id: string;
    label: string;
    type: string;
    group: string;
    x: number;
    y: number;
    size: number;
    style: {
      backgroundColor: string;
      color: string;
      border: string;
    };
    properties?: Record<string, any>;
    created_at?: string;
    user_id?: string;
  }>;
  edges: Array<{
    id: string;
    source: string;
    target: string;
    label: string;
    type: string;
    weight?: number;
    style: {
      strokeWidth: number;
      stroke: string;
    };
    properties?: Record<string, any>;
    created_at?: string;
    user_id?: string;
  }>;
  layout: string;
  metadata: {
    total_nodes: number;
    total_edges: number;
    layout_algorithm: string;
    include_metadata: boolean;
    max_degree: number;
    avg_degree: number;
  };
  viewport: {
    x: number;
    y: number;
    zoom: number;
  };
  generated_at: string;
}
```

**实现状态**: ✅ 已完成  
**标签**: `GRAPH-MANAGEMENT`

### 4.4 图搜索API ✅

#### POST /v1/graph/search
**用途**：高级图搜索功能

**请求格式**：
```typescript
interface GraphSearchRequest {
  query: string;
  search_type?: 'semantic' | 'content' | 'structural'; // 默认'semantic'
  entity_types?: string[];
  relationship_types?: string[];
  user_id?: string;
  limit?: number;                      // 默认20，最大100
  include_relationships?: boolean;     // 默认true
  similarity_threshold?: number;       // 0.0-1.0，默认0.7
}
```

**响应格式**：
```typescript
interface GraphSearchResponse {
  query: string;
  search_type: string;
  entities: Array<{
    id: string;
    label: string;
    type: string;
    properties: Record<string, any>;
    score: number;
    matches?: string[];
    connections_count?: number;
    connection_types?: string[];
    created_at?: string;
    user_id?: string;
  }>;
  relationships: Array<{
    id: string;
    source_entity: string;
    target_entity: string;
    relationship_type: string;
    properties: Record<string, any>;
    score: number;
    matches?: string[];
    created_at?: string;
    user_id?: string;
  }>;
  related_relationships: Array<{
    id: string;
    source_entity: string;
    target_entity: string;
    relationship_type: string;
    properties: Record<string, any>;
    created_at?: string;
    user_id?: string;
  }>;
  total_entities_found: number;
  total_relationships_found: number;
  search_metadata: {
    similarity_threshold: number;
    entity_type_filters?: string[];
    relationship_type_filters?: string[];
    include_relationships: boolean;
    user_id?: string;
  };
  searched_at: string;
}
```

**实现状态**: ✅ 已完成  
**标签**: `GRAPH-MANAGEMENT`

## 5. 实现状态总结

### 5.1 开发优先级状态

**P0 (第2周必需)** - ✅ 已完成：
1. ✅ `GET /v1/stats` - Dashboard核心统计数据
2. ✅ `GET /v1/activities` - Activity时间线数据
3. ✅ `GET /v1/admin/dashboard` - 统一管理面板API

**P1 (第2-3周重要)** - ✅ 已完成：
4. ✅ `GET /v1/users` - 用户管理基础功能
5. ✅ `DELETE /v1/users/{user_id}` - 用户删除功能
6. ✅ `GET /v1/users/{user_id}/stats` - 用户统计详情

**P2 (增强功能)** - ✅ 已完成：
7. ✅ Graph Memory专用API增强 (完整CRUD + 统计 + 搜索)
8. ✅ 用户管理增强API (创建、更新、分析、导出、批量操作)

### 5.2 技术实现特点

1. **智能数据获取**: 所有API都能从现有Mem0记忆数据中智能提取所需信息
2. **历史数据支持**: 利用SQLite history.db提供活动日志功能
3. **缓存优化**: 实现了图记忆缓存机制提升性能
4. **错误处理**: 完善的异常处理和用户友好的错误消息
5. **参数验证**: 使用Pydantic进行严格的输入验证
6. **分页支持**: 所有列表API都支持分页和限制参数
7. **过滤功能**: 灵活的筛选和查询功能
8. **用户管理增强**: 完整的CRUD操作、分析、导出、批量操作支持

### 5.3 数据存储方案

**活动日志存储**：
- ✅ 基于现有SQLite history.db进行查询和统计
- ✅ 支持多种时间范围的活动统计

**用户统计存储**：
- ✅ 基于现有记忆数据进行实时统计计算
- ✅ 智能用户检测和分析功能

**图数据存储**：
- ✅ 集成现有的Neo4j/Memgraph图存储支持
- ✅ 缓存机制优化图数据访问性能

## 6. 前端集成示例

### 6.1 统计数据获取
```typescript
// 获取Dashboard统计数据
const dashboardData = await fetch('/v1/admin/dashboard?time_range=24h')
  .then(res => res.json());

// 统计面板显示
const StatsPanel = () => (
  <div className="stats-grid">
    <StatCard title="Total Memories" value={dashboardData.stats.total_memories} />
    <StatCard title="Total Users" value={dashboardData.stats.total_users} />
    <StatCard title="Search Events" value={dashboardData.stats.search_events} />
    <StatCard title="Add Events" value={dashboardData.stats.add_events} />
  </div>
);
```

### 6.2 Activity时间线集成
```typescript
// Activity时间线组件
const ActivityTimeline = () => {
  const activities = dashboardData.recent_activities;
  
  return (
    <div className="activity-timeline">
      {activities.map(activity => (
        <ActivityItem
          key={activity.id}
          operation={activity.operation}
          details={activity.details}
          timestamp={activity.timestamp}
          status={activity.status}
        />
      ))}
    </div>
  );
};
```

### 6.3 用户管理集成
```typescript
// 用户管理Hook集成示例
const { 
  users, 
  getUserAnalytics, 
  exportUserData, 
  batchUserOperations 
} = useUserManagement();

// 用户分析数据获取
const analytics = await getUserAnalytics('user_123');

// 批量用户操作
await batchUserOperations([{
  type: 'export',
  userIds: ['user_1', 'user_2', 'user_3']
}]);
```

### 6.4 Graph Memory集成
```typescript
// Graph Memory API集成示例
const { 
  fetchGraphMemories, 
  createEntity, 
  searchEntities 
} = useGraphMemoryApi();

// 图数据获取
const graphData = await fetchGraphMemories();

// 图搜索功能
const searchResults = await searchEntities({
  query: 'artificial intelligence',
  search_type: 'semantic',
  limit: 20
});
```

## 7. 测试和验证

### 7.1 API测试用例
```bash
# 测试统计API
curl "http://localhost:8000/v1/stats?time_range=24h"

# 测试活动日志API
curl "http://localhost:8000/v1/activities?limit=10&operation_type=ADD"

# 测试用户管理API
curl "http://localhost:8000/v1/users?limit=20&include_stats=true"

# 测试统一管理面板API
curl "http://localhost:8000/v1/admin/dashboard"

# 测试用户分析API
curl "http://localhost:8000/v1/users/user_123/analytics?days=30"

# 测试图统计API
curl "http://localhost:8000/v1/graph/stats?time_range=7d"

# 测试图可视化API
curl "http://localhost:8000/v1/graph/visualization/user_123?layout=force"

# 测试图搜索API
curl -X POST "http://localhost:8000/v1/graph/search" \
  -H "Content-Type: application/json" \
  -d '{"query": "artificial intelligence", "search_type": "semantic"}'
```

### 7.2 预期响应示例
```json
{
  "stats": {
    "total_memories": 156,
    "total_users": 8,
    "search_events": 34,
    "add_events": 23,
    "graph_memories": 12,
    "entities_count": 45,
    "relationships_count": 67,
    "graph_density": 0.15,
    "last_updated": "2024-07-29T12:30:00Z",
    "time_range": "24h"
  },
  "recent_activities": [
    {
      "id": "act_001",
      "timestamp": "2024-07-29T12:25:30Z",
      "operation": "ADD",
      "details": "Created memory: User prefers dark theme",
      "status": "success",
      "user_id": "user_123"
    }
  ],
  "quick_actions": [
    {"id": "create", "label": "Create Memory", "icon": "plus", "primary": true},
    {"id": "search", "label": "Search Memories", "icon": "search"},
    {"id": "graph", "label": "Graph Memory", "icon": "network"},
    {"id": "users", "label": "Manage Users", "icon": "users"}
  ]
}
```

## 8. 部署和运行

### 8.1 服务器启动信息
启动Mem0服务器时，将显示所有可用的API端点：

```
Starting Mem0 Server with OpenMemory UI API Support...
Available UI Management APIs:
  - GET /v1/stats - System statistics for Dashboard
  - GET /v1/activities - Activity logs for Timeline
  - GET /v1/admin/dashboard - Unified dashboard data
Available User Management APIs:
  - GET /v1/users - List all users with statistics
  - GET /v1/users/{user_id}/stats - Get user detailed statistics
  - DELETE /v1/users/{user_id} - Delete user and all data
Available User Management Enhancement APIs:
  - POST /v1/users - Create a new user
  - PUT /v1/users/{user_id} - Update user information
  - GET /v1/users/{user_id}/analytics - Get user analytics data
  - POST /v1/users/{user_id}/export - Export user data
  - POST /v1/users/batch - Batch user operations
Available Graph Management APIs:
  - GET /v1/graph/entities - List graph entities with filtering
  - GET /v1/graph/relationships - List graph relationships with filtering
  - POST /v1/graph/entities - Create graph entity
  - PUT /v1/graph/entities/{entity_id} - Update graph entity
  - DELETE /v1/graph/entities/{entity_id} - Delete graph entity
  - POST /v1/graph/relationships - Create graph relationship
  - PUT /v1/graph/relationships/{relationship_id} - Update graph relationship
  - DELETE /v1/graph/relationships/{relationship_id} - Delete graph relationship
  - GET /v1/graph/stats - Get graph statistics
  - GET /v1/graph/visualization/{memory_id} - Get graph visualization data
  - POST /v1/graph/search - Search graph entities and relationships
```

### 8.2 API文档访问
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## 9. 结论

✅ **实现完成**: 所有OpenMemory UI所需的API端点已全部实现完成，包括：
- 统计数据API (UI-STATS)
- 活动日志API (UI-ACTIVITIES) 
- 统一管理面板API (UI-DASHBOARD)
- 用户管理API (USER-MANAGEMENT)
- 用户管理增强API (USER-MANAGEMENT) - 完整的创建、更新、分析、导出、批量操作
- Graph Memory完整API (GRAPH-MANAGEMENT) - 完整的CRUD操作、统计和搜索

✅ **技术特色**:
- 智能数据提取和用户检测
- 完善的缓存和性能优化
- 严格的参数验证和错误处理
- 灵活的筛选和分页功能
- 完整的CRUD操作支持
- 用户管理增强功能的完全集成

✅ **前端兼容**: 所有API都与OpenMemory UI的现有hooks完美兼容，支持无缝集成。特别是用户管理增强API完全支持 useUserManagement hook 中定义的所有功能。

这份文档涵盖了支持OpenMemory UI所需的所有关键API端点的完整实现，为后续的前端开发和部署提供了可靠的后端支持。所有的用户管理增强功能都已就绪，可以直接在前端调用。