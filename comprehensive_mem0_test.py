#!/usr/bin/env python3
"""
Mem0 综合功能测试脚本
测试所有主要功能：记忆添加、搜索、删除、Categories自动分类、多用户等
"""

import requests
import json
import time
from datetime import datetime

API_BASE = "http://localhost:8000"

class Mem0Tester:
    def __init__(self):
        self.api_base = API_BASE
        self.test_results = {
            "passed": 0,
            "failed": 0,
            "details": []
        }
        
    def log_test(self, test_name, success, message="", data=None):
        """记录测试结果"""
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"{status} {test_name}: {message}")
        
        if success:
            self.test_results["passed"] += 1
        else:
            self.test_results["failed"] += 1
            
        self.test_results["details"].append({
            "test": test_name,
            "success": success,
            "message": message,
            "data": data
        })
        
    def test_api_health(self):
        """测试API健康状态"""
        try:
            response = requests.get(f"{self.api_base}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.log_test("API健康检查", True, f"状态: {data.get('status')}")
                return True
            else:
                self.log_test("API健康检查", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("API健康检查", False, f"连接失败: {e}")
            return False

    def test_add_memory(self):
        """测试添加记忆功能"""
        print("\\n=== 测试基本记忆添加功能 ===")
        
        test_cases = [
            {
                "name": "个人信息记忆",
                "messages": [{"role": "user", "content": "My name is Bob and I'm 25 years old. I work as a data scientist at Microsoft."}],
                "user_id": "test_bob",
                "expected_categories": ["personal_details", "professional_details"]
            },
            {
                "name": "爱好兴趣记忆", 
                "messages": [{"role": "user", "content": "I love playing basketball and watching sci-fi movies. I also enjoy cooking Italian food."}],
                "user_id": "test_bob",
                "expected_categories": ["sports", "entertainment", "food", "hobbies"]
            },
            {
                "name": "家庭关系记忆",
                "messages": [{"role": "user", "content": "I have a younger sister named Emma who is studying medicine. My parents live in Seattle."}],
                "user_id": "test_bob", 
                "expected_categories": ["family"]
            },
            {
                "name": "旅行经历记忆",
                "messages": [{"role": "user", "content": "Last year I traveled to Japan, Thailand, and South Korea. I really want to visit Iceland next."}],
                "user_id": "test_bob",
                "expected_categories": ["travel"]
            },
            {
                "name": "健康信息记忆",
                "messages": [{"role": "user", "content": "I go to the gym 3 times a week and follow a Mediterranean diet. I'm allergic to peanuts."}],
                "user_id": "test_bob",
                "expected_categories": ["health"]
            }
        ]
        
        created_memories = []
        
        for case in test_cases:
            try:
                response = requests.post(
                    f"{self.api_base}/v1/memories/",
                    json=case,
                    timeout=30
                )
                
                if response.status_code == 200:
                    memories = response.json()
                    if memories:
                        self.log_test(f"添加{case['name']}", True, f"创建了 {len(memories)} 条记忆")
                        created_memories.extend(memories)
                        
                        # 检查Categories
                        for memory in memories:
                            memory_id = memory['id']
                            # 获取记忆详情检查categories
                            detail_response = requests.get(f"{self.api_base}/v1/memories/{memory_id}")
                            if detail_response.status_code == 200:
                                detail = detail_response.json()
                                categories = detail.get('categories', [])
                                if categories:
                                    self.log_test(f"{case['name']} - Categories检查", True, f"自动生成分类: {categories}")
                                else:
                                    self.log_test(f"{case['name']} - Categories检查", False, "未生成Categories")
                    else:
                        self.log_test(f"添加{case['name']}", False, "返回空结果")
                else:
                    self.log_test(f"添加{case['name']}", False, f"HTTP {response.status_code}: {response.text[:100]}")
                    
            except Exception as e:
                self.log_test(f"添加{case['name']}", False, f"异常: {e}")
                
            time.sleep(1)  # 避免API过载
            
        return created_memories

    def test_search_memory(self):
        """测试记忆搜索功能"""
        print("\\n=== 测试记忆搜索功能 ===")
        
        search_queries = [
            {
                "query": "What's my job?",
                "user_id": "test_bob",
                "expected_keywords": ["data scientist", "Microsoft"]
            },
            {
                "query": "What sports do I like?", 
                "user_id": "test_bob",
                "expected_keywords": ["basketball"]
            },
            {
                "query": "Tell me about my family",
                "user_id": "test_bob", 
                "expected_keywords": ["sister", "Emma", "parents", "Seattle"]
            },
            {
                "query": "Where have I traveled?",
                "user_id": "test_bob",
                "expected_keywords": ["Japan", "Thailand", "Korea"]
            },
            {
                "query": "What are my health habits?",
                "user_id": "test_bob",
                "expected_keywords": ["gym", "Mediterranean", "allergic", "peanuts"]
            }
        ]
        
        for query_case in search_queries:
            try:
                response = requests.post(
                    f"{self.api_base}/v2/memories/search/",
                    json={
                        "query": query_case["query"],
                        "user_id": query_case["user_id"],
                        "limit": 10
                    },
                    timeout=15
                )
                
                if response.status_code == 200:
                    results = response.json()
                    memories = results.get('results', [])
                    
                    if memories:
                        # 检查是否包含预期关键词
                        all_text = " ".join([mem.get('memory', '') for mem in memories]).lower()
                        found_keywords = [kw for kw in query_case['expected_keywords'] if kw.lower() in all_text]
                        
                        if found_keywords:
                            self.log_test(f"搜索: {query_case['query']}", True, 
                                        f"找到 {len(memories)} 条记忆，匹配关键词: {found_keywords}")
                        else:
                            self.log_test(f"搜索: {query_case['query']}", False, 
                                        f"找到 {len(memories)} 条记忆，但未匹配预期关键词")
                    else:
                        self.log_test(f"搜索: {query_case['query']}", False, "未找到相关记忆")
                else:
                    self.log_test(f"搜索: {query_case['query']}", False, f"HTTP {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"搜索: {query_case['query']}", False, f"异常: {e}")
                
            time.sleep(1)

    def test_get_memories(self):
        """测试获取记忆列表功能"""  
        print("\\n=== 测试获取记忆列表 ===")
        
        try:
            response = requests.get(
                f"{self.api_base}/v1/memories/",
                params={"user_id": "test_bob", "limit": 20},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                memories = data.get('results', [])
                self.log_test("获取记忆列表", True, f"获取到 {len(memories)} 条记忆")
                
                # 检查Categories分布
                categorized_count = 0
                categories_set = set()
                
                for memory in memories:
                    categories = memory.get('categories', [])
                    if categories:
                        categorized_count += 1
                        categories_set.update(categories)
                
                self.log_test("记忆分类统计", True, 
                            f"有分类记忆: {categorized_count}/{len(memories)}, 分类种类: {len(categories_set)}")
                            
                if categories_set:
                    print(f"    发现的分类: {sorted(list(categories_set))}")
                    
            else:
                self.log_test("获取记忆列表", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("获取记忆列表", False, f"异常: {e}")

    def test_custom_categories(self):
        """测试自定义Categories功能"""
        print("\\n=== 测试自定义Categories功能 ===")
        
        try:
            response = requests.post(
                f"{self.api_base}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": "I completed my machine learning certification from Stanford."}],
                    "user_id": "test_custom_cat",
                    "custom_categories": ["education", "achievement"]
                },
                timeout=15
            )
            
            if response.status_code == 200:
                memories = response.json()
                if memories:
                    memory_id = memories[0]['id']
                    
                    # 获取记忆详情检查categories
                    detail_response = requests.get(f"{self.api_base}/v1/memories/{memory_id}")
                    if detail_response.status_code == 200:
                        detail = detail_response.json()
                        categories = detail.get('categories', [])
                        
                        if 'education' in categories and 'achievement' in categories:
                            self.log_test("自定义Categories", True, f"正确使用自定义分类: {categories}")
                        else:
                            self.log_test("自定义Categories", False, f"自定义分类不正确: {categories}")
                    else:
                        self.log_test("自定义Categories", False, "无法获取记忆详情")
                else:
                    self.log_test("自定义Categories", False, "未创建记忆")
            else:
                self.log_test("自定义Categories", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("自定义Categories", False, f"异常: {e}")

    def test_memory_update(self):
        """测试记忆更新功能"""
        print("\\n=== 测试记忆更新功能 ===")
        
        try:
            # 先创建一条记忆
            create_response = requests.post(
                f"{self.api_base}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": "I work at Google as a software engineer."}],
                    "user_id": "test_update"
                },
                timeout=15
            )
            
            if create_response.status_code == 200:
                memories = create_response.json()
                if memories:
                    memory_id = memories[0]['id']
                    
                    # 更新记忆
                    update_response = requests.put(
                        f"{self.api_base}/v1/memories/{memory_id}/",
                        json={
                            "text": "I work at Microsoft as a senior software engineer and team lead."
                        },
                        timeout=10
                    )
                    
                    if update_response.status_code == 200:
                        self.log_test("记忆更新", True, "成功更新记忆内容")
                        
                        # 验证更新结果
                        get_response = requests.get(f"{self.api_base}/v1/memories/{memory_id}")
                        if get_response.status_code == 200:
                            updated_memory = get_response.json()
                            if "Microsoft" in updated_memory.get('memory', ''):
                                self.log_test("更新内容验证", True, "记忆内容正确更新")
                            else:
                                self.log_test("更新内容验证", False, "记忆内容未正确更新")
                    else:
                        self.log_test("记忆更新", False, f"HTTP {update_response.status_code}")
                else:
                    self.log_test("记忆更新", False, "无法创建测试记忆")
            else:
                self.log_test("记忆更新", False, f"创建测试记忆失败: HTTP {create_response.status_code}")
                
        except Exception as e:
            self.log_test("记忆更新", False, f"异常: {e}")

    def test_memory_history(self):
        """测试记忆历史功能"""
        print("\\n=== 测试记忆历史功能 ===")
        
        try:
            # 获取任一记忆的历史
            get_response = requests.get(
                f"{self.api_base}/v1/memories/",
                params={"user_id": "test_bob", "limit": 1}
            )
            
            if get_response.status_code == 200:
                data = get_response.json()
                memories = data.get('results', [])
                
                if memories:
                    memory_id = memories[0]['id']
                    
                    # 获取历史记录
                    history_response = requests.get(f"{self.api_base}/v1/memories/{memory_id}/history/")
                    
                    if history_response.status_code == 200:
                        history = history_response.json()
                        self.log_test("记忆历史", True, f"获取到 {len(history)} 条历史记录")
                    else:
                        self.log_test("记忆历史", False, f"HTTP {history_response.status_code}")
                else:
                    self.log_test("记忆历史", False, "没有记忆可测试历史功能")
            else:
                self.log_test("记忆历史", False, f"无法获取测试记忆: HTTP {get_response.status_code}")
                
        except Exception as e:
            self.log_test("记忆历史", False, f"异常: {e}")

    def test_multi_user_isolation(self):
        """测试多用户记忆隔离"""
        print("\\n=== 测试多用户记忆隔离 ===")
        
        users = ["alice_isolation", "bob_isolation"]
        
        # 为每个用户创建不同的记忆
        for i, user in enumerate(users):
            try:
                response = requests.post(
                    f"{self.api_base}/v1/memories/",
                    json={
                        "messages": [{"role": "user", "content": f"I am user {i+1} and this is my private information."}],
                        "user_id": user
                    },
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.log_test(f"创建{user}的记忆", True, "成功创建私有记忆")
                else:
                    self.log_test(f"创建{user}的记忆", False, f"HTTP {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"创建{user}的记忆", False, f"异常: {e}")
        
        # 验证用户记忆隔离
        for user in users:
            try:
                response = requests.get(
                    f"{self.api_base}/v1/memories/",
                    params={"user_id": user}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    memories = data.get('results', [])
                    
                    # 检查是否只包含该用户的记忆
                    user_specific = all(user in str(mem) for mem in memories)
                    if user_specific or len(memories) <= 1:
                        self.log_test(f"{user}记忆隔离", True, f"正确隔离，获取到 {len(memories)} 条记忆")
                    else:
                        self.log_test(f"{user}记忆隔离", False, "可能存在记忆泄露")
                else:
                    self.log_test(f"{user}记忆隔离", False, f"HTTP {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"{user}记忆隔离", False, f"异常: {e}")

    def test_batch_operations(self):
        """测试批量操作"""
        print("\\n=== 测试批量删除操作 ===")
        
        try:
            # 创建测试记忆用于批量删除
            memories_to_delete = []
            
            for i in range(3):
                response = requests.post(
                    f"{self.api_base}/v1/memories/",
                    json={
                        "messages": [{"role": "user", "content": f"This is test memory {i+1} for batch deletion."}],
                        "user_id": "batch_test_user"
                    },
                    timeout=10
                )
                
                if response.status_code == 200:
                    memories = response.json()
                    if memories:
                        memories_to_delete.append({"memory_id": memories[0]['id']})
            
            if len(memories_to_delete) >= 2:
                # 执行批量删除
                batch_response = requests.post(
                    f"{self.api_base}/v1/memories/batch_delete/",
                    json={"memories": memories_to_delete[:2]},  # 只删除前两个
                    timeout=15
                )
                
                if batch_response.status_code == 200:
                    result = batch_response.json()
                    successful = result.get('successful_deletions', [])
                    self.log_test("批量删除", True, f"成功删除 {len(successful)} 条记忆")
                else:
                    self.log_test("批量删除", False, f"HTTP {batch_response.status_code}")
            else:
                self.log_test("批量删除", False, "无法创建足够的测试记忆")
                
        except Exception as e:
            self.log_test("批量删除", False, f"异常: {e}")

    def test_custom_instructions(self):
        """测试自定义指令功能"""
        print("\n=== 测试自定义指令功能 ===")
        
        try:
            # 测试带自定义指令的记忆添加
            response = requests.post(
                f"{self.api_base}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": "I prefer Japanese cuisine and I'm a vegetarian."}],
                    "user_id": "custom_instruction_user",
                    "custom_fact_extraction_prompt": "Extract only dietary preferences and food-related information from the conversation. Always include the user's dietary restrictions as a separate fact."
                },
                timeout=15
            )
            
            if response.status_code == 200:
                memories = response.json()
                if memories:
                    self.log_test("自定义指令记忆创建", True, f"创建了 {len(memories)} 条记忆")
                    
                    # 检查记忆内容是否按自定义指令处理
                    memory_contents = [mem.get('memory', '') for mem in memories]
                    food_related = any('cuisine' in content.lower() or 'vegetarian' in content.lower() 
                                     for content in memory_contents)
                    
                    if food_related:
                        self.log_test("自定义指令内容检查", True, "成功按自定义指令提取食物相关信息")
                    else:
                        self.log_test("自定义指令内容检查", False, "未按自定义指令正确提取信息")
                else:
                    self.log_test("自定义指令记忆创建", False, "未创建记忆")
            else:
                self.log_test("自定义指令记忆创建", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("自定义指令功能", False, f"异常: {e}")

    def test_advanced_retrieval(self):
        """测试高级检索功能"""
        print("\n=== 测试高级检索功能 ===")
        
        # 测试带过滤条件的检索
        try:
            # 先创建带特定metadata的记忆
            response = requests.post(
                f"{self.api_base}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": "I completed my PhD in Computer Science from MIT in 2020."}],
                    "user_id": "advanced_retrieval_user",
                    "metadata": {"education_level": "PhD", "institution": "MIT", "year": 2020}
                },
                timeout=15
            )
            
            if response.status_code == 200:
                self.log_test("创建带metadata的记忆", True, "成功创建")
                
                # 测试带metadata过滤的搜索
                search_response = requests.post(
                    f"{self.api_base}/v2/memories/search/",
                    json={
                        "query": "education",
                        "user_id": "advanced_retrieval_user",
                        "filters": {"education_level": "PhD"},
                        "limit": 10
                    },
                    timeout=15
                )
                
                if search_response.status_code == 200:
                    results = search_response.json()
                    memories = results.get('results', [])
                    if memories:
                        self.log_test("metadata过滤搜索", True, f"找到 {len(memories)} 条匹配记忆")
                    else:
                        self.log_test("metadata过滤搜索", False, "未找到匹配记忆")
                else:
                    self.log_test("metadata过滤搜索", False, f"HTTP {search_response.status_code}")
            else:
                self.log_test("创建带metadata的记忆", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test("高级检索功能", False, f"异常: {e}")

    def test_contextual_adding(self):
        """测试上下文添加功能"""
        print("\n=== 测试上下文添加功能 ===")
        
        try:
            # 创建第一条记忆
            first_response = requests.post(
                f"{self.api_base}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": "I work as a software engineer at Google."}],
                    "user_id": "context_user"
                },
                timeout=15
            )
            
            if first_response.status_code == 200:
                self.log_test("创建初始上下文记忆", True, "成功")
                
                # 添加相关上下文记忆
                context_response = requests.post(
                    f"{self.api_base}/v1/memories/",
                    json={
                        "messages": [
                            {"role": "user", "content": "I work as a software engineer at Google."},
                            {"role": "assistant", "content": "That's great! What projects are you working on?"},
                            {"role": "user", "content": "I'm currently working on a machine learning project for recommendation systems."}
                        ],
                        "user_id": "context_user"
                    },
                    timeout=15
                )
                
                if context_response.status_code == 200:
                    memories = context_response.json()
                    if memories:
                        self.log_test("上下文记忆添加", True, f"成功添加 {len(memories)} 条上下文记忆")
                        
                        # 验证上下文信息是否正确关联
                        search_response = requests.post(
                            f"{self.api_base}/v2/memories/search/",
                            json={
                                "query": "machine learning project",
                                "user_id": "context_user",
                                "limit": 5
                            }
                        )
                        
                        if search_response.status_code == 200:
                            results = search_response.json()
                            found_memories = results.get('results', [])
                            if found_memories:
                                self.log_test("上下文记忆检索", True, f"成功检索到 {len(found_memories)} 条相关记忆")
                            else:
                                self.log_test("上下文记忆检索", False, "未检索到相关记忆")
                    else:
                        self.log_test("上下文记忆添加", False, "未创建记忆")
                else:
                    self.log_test("上下文记忆添加", False, f"HTTP {context_response.status_code}")
            else:
                self.log_test("创建初始上下文记忆", False, f"HTTP {first_response.status_code}")
                
        except Exception as e:
            self.log_test("上下文添加功能", False, f"异常: {e}")

    def test_all_api_endpoints(self):
        """测试所有API端点"""
        print("\n=== 测试所有API端点 ===")
        
        # 测试所有主要API端点
        endpoints_to_test = [
            {"method": "GET", "url": "/health", "name": "健康检查端点"},
            {"method": "GET", "url": "/v1/memories/", "name": "获取记忆列表端点", "params": {"user_id": "api_test_user"}},
            {"method": "GET", "url": "/", "name": "根端点"},
            {"method": "GET", "url": "/v1/memories/stats/", "name": "记忆统计端点", "params": {"user_id": "api_test_user"}},
        ]
        
        for endpoint in endpoints_to_test:
            try:
                if endpoint["method"] == "GET":
                    params = endpoint.get("params", {})
                    response = requests.get(f"{self.api_base}{endpoint['url']}", params=params, timeout=10)
                elif endpoint["method"] == "POST":
                    data = endpoint.get("data", {})
                    response = requests.post(f"{self.api_base}{endpoint['url']}", json=data, timeout=10)
                
                if response.status_code in [200, 201]:
                    self.log_test(f"API端点 - {endpoint['name']}", True, f"状态码: {response.status_code}")
                else:
                    self.log_test(f"API端点 - {endpoint['name']}", False, f"状态码: {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"API端点 - {endpoint['name']}", False, f"异常: {e}")

    def test_multimodal_memory(self):
        """测试多模态记忆功能"""
        print("\n=== 测试多模态记忆功能 ===")
        
        try:
            # 测试文本+图像记忆（模拟）
            multimodal_response = requests.post(
                f"{self.api_base}/v1/memories/",
                json={
                    "messages": [
                        {
                            "role": "user", 
                            "content": "I took a photo of the beautiful sunset at the beach yesterday. The colors were amazing - orange, pink, and purple."
                        }
                    ],
                    "user_id": "multimodal_user",
                    "metadata": {"content_type": "text_with_image_description", "location": "beach"}
                },
                timeout=15
            )
            
            if multimodal_response.status_code == 200:
                memories = multimodal_response.json()
                if memories:
                    self.log_test("多模态记忆创建", True, f"成功创建 {len(memories)} 条多模态记忆")
                    
                    # 测试多模态内容搜索
                    search_response = requests.post(
                        f"{self.api_base}/v2/memories/search/",
                        json={
                            "query": "sunset beach photo",
                            "user_id": "multimodal_user",
                            "limit": 5
                        }
                    )
                    
                    if search_response.status_code == 200:
                        results = search_response.json()
                        found_memories = results.get('results', [])
                        if found_memories:
                            self.log_test("多模态记忆搜索", True, f"找到 {len(found_memories)} 条相关记忆")
                        else:
                            self.log_test("多模态记忆搜索", False, "未找到相关记忆")
                else:
                    self.log_test("多模态记忆创建", False, "未创建记忆")
            else:
                self.log_test("多模态记忆创建", False, f"HTTP {multimodal_response.status_code}")
                
        except Exception as e:
            self.log_test("多模态记忆功能", False, f"异常: {e}")

    def test_memory_customization(self):
        """测试记忆定制功能"""
        print("\n=== 测试记忆定制功能 ===")
        
        try:
            # 测试自定义配置的记忆创建
            customization_response = requests.post(
                f"{self.api_base}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": "I'm learning Python programming and completed my first machine learning course."}],
                    "user_id": "customization_user",
                    "custom_categories": ["education", "programming", "achievement"],
                    "metadata": {
                        "priority": "high",
                        "source": "learning_tracker",
                        "tags": ["python", "ml", "course"]
                    },
                    "custom_fact_extraction_prompt": "Focus on extracting learning achievements and technical skills. Categorize information by skill level and subject area."
                },
                timeout=20
            )
            
            if customization_response.status_code == 200:
                memories = customization_response.json()
                if memories:
                    self.log_test("记忆定制创建", True, f"成功创建 {len(memories)} 条定制记忆")
                    
                    # 验证定制配置是否生效
                    memory_id = memories[0]['id']
                    detail_response = requests.get(f"{self.api_base}/v1/memories/{memory_id}")
                    
                    if detail_response.status_code == 200:
                        detail = detail_response.json()
                        categories = detail.get('categories', [])
                        metadata = detail.get('metadata', {})
                        
                        # 检查自定义categories
                        custom_categories_present = any(cat in categories for cat in ["education", "programming", "achievement"])
                        
                        # 检查自定义metadata
                        custom_metadata_present = metadata.get('priority') == 'high' and 'tags' in metadata
                        
                        if custom_categories_present:
                            self.log_test("自定义Categories验证", True, f"发现自定义分类: {categories}")
                        else:
                            self.log_test("自定义Categories验证", False, f"未发现预期的自定义分类: {categories}")
                            
                        if custom_metadata_present:
                            self.log_test("自定义Metadata验证", True, "成功保存自定义元数据")
                        else:
                            self.log_test("自定义Metadata验证", False, "自定义元数据未正确保存")
                    else:
                        self.log_test("记忆定制验证", False, "无法获取记忆详情")
                else:
                    self.log_test("记忆定制创建", False, "未创建记忆")
            else:
                self.log_test("记忆定制创建", False, f"HTTP {customization_response.status_code}")
                
        except Exception as e:
            self.log_test("记忆定制功能", False, f"异常: {e}")

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始Mem0综合功能测试")
        print("=" * 80)
        
        start_time = datetime.now()
        
        # 1. 基础健康检查
        if not self.test_api_health():
            print("API不可用，终止测试")
            return
            
        # 2. 记忆添加测试（包含Categories测试）
        self.test_add_memory()
        
        # 3. 记忆搜索测试
        self.test_search_memory()
        
        # 4. 记忆列表获取测试
        self.test_get_memories()
        
        # 5. 自定义Categories测试
        self.test_custom_categories()
        
        # 6. 记忆更新测试
        self.test_memory_update()
        
        # 7. 记忆历史测试
        self.test_memory_history()
        
        # 8. 多用户隔离测试
        self.test_multi_user_isolation()
        
        # 9. 批量操作测试
        self.test_batch_operations()
        
        # 10. 记忆定制测试
        self.test_memory_customization()
        
        # 11. 自定义指令测试
        self.test_custom_instructions()
        
        # 12. 高级检索测试
        self.test_advanced_retrieval()
        
        # 13. 上下文添加测试
        self.test_contextual_adding()
        
        # 14. 多模态记忆测试
        self.test_multimodal_memory()
        
        # 15. 所有API端点测试
        self.test_all_api_endpoints()
        
        # 测试结果统计
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print("\\n" + "=" * 80)
        print("🎯 测试结果统计")
        print("=" * 80)
        print(f"总测试项目: {self.test_results['passed'] + self.test_results['failed']}")
        print(f"✅ 通过: {self.test_results['passed']}")
        print(f"❌ 失败: {self.test_results['failed']}")
        print(f"⏱️ 耗时: {duration:.1f} 秒")
        
        success_rate = (self.test_results['passed'] / (self.test_results['passed'] + self.test_results['failed'])) * 100
        print(f"📊 成功率: {success_rate:.1f}%")
        
        if self.test_results['failed'] > 0:
            print("\\n❌ 失败的测试:")
            for detail in self.test_results['details']:
                if not detail['success']:
                    print(f"  - {detail['test']}: {detail['message']}")
        
        print("\\n🏁 测试完成!")
        
        return self.test_results

if __name__ == "__main__":
    tester = Mem0Tester()
    results = tester.run_all_tests()