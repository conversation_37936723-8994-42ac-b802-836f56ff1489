#!/usr/bin/env python3
"""
Categories格式测试脚本
"""

import requests
import json

API_BASE = "http://localhost:8000"

def test_categories_formats():
    print("🧪 测试Categories不同格式")
    print("=" * 50)
    
    # 测试不同的custom_categories格式
    formats = [
        {
            "name": "字符串数组格式",
            "data": ["education", "achievement"],
            "expected": True
        },
        {
            "name": "字典数组格式",
            "data": [{"name": "education"}, {"name": "achievement"}],
            "expected": True
        },
        {
            "name": "直接字符串格式",
            "data": "education,achievement",
            "expected": False
        }
    ]
    
    for i, fmt in enumerate(formats):
        print(f"\n--- 测试格式 {i+1}: {fmt['name']} ---")
        
        try:
            payload = {
                "messages": [{"role": "user", "content": f"Format test {i+1}: I completed my degree."}],
                "user_id": f"format_test_{i+1}",
                "custom_categories": fmt["data"]
            }
            
            print(f"发送数据: {json.dumps(payload, indent=2)}")
            
            response = requests.post(
                f"{API_BASE}/v1/memories/",
                json=payload,
                timeout=20
            )
            
            print(f"响应状态: {response.status_code}")
            
            if response.status_code == 200:
                memories = response.json()
                print(f"✓ 成功创建 {len(memories)} 条记忆")
                
                if memories:
                    # 获取记忆详情
                    user_response = requests.get(
                        f"{API_BASE}/v1/memories/",
                        params={"user_id": f"format_test_{i+1}", "limit": 10}
                    )
                    
                    if user_response.status_code == 200:
                        user_memories = user_response.json()
                        if user_memories:
                            categories = user_memories[0].get('metadata', {}).get('categories', [])
                            print(f"✓ 发现categories: {categories}")
                        else:
                            print("⚠️ 无法获取记忆")
            else:
                print(f"✗ 请求失败: {response.text[:200]}")
                
        except Exception as e:
            print(f"✗ 异常: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Categories格式测试完成!")

if __name__ == "__main__":
    test_categories_formats()