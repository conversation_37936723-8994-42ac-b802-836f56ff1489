/**
 * Chrome开发者工具调试脚本
 * 用于诊断GraphMemory页面的数据显示不一致问题
 * 
 * 使用方法：
 * 1. 打开Chrome浏览器访问 http://localhost:3000/graph-memory
 * 2. 打开开发者工具 (F12)
 * 3. 在Console中粘贴并运行此脚本
 */

console.log('🔧 [Chrome Debug] GraphMemory数据诊断脚本启动...');

// 全局调试对象
window.GraphMemoryDebugger = {
    // 检查Redux store状态
    checkReduxState() {
        console.log('📊 [Redux State] 检查Redux store状态...');
        
        // 尝试获取Redux store
        const storeDiv = document.querySelector('#__next');
        if (storeDiv && storeDiv._reactInternalFiber) {
            console.log('✅ React应用已挂载');
        }
        
        // 检查是否有window.__REDUX_DEVTOOLS_EXTENSION__
        if (window.__REDUX_DEVTOOLS_EXTENSION__) {
            console.log('✅ Redux DevTools已安装');
        } else {
            console.warn('⚠️ Redux DevTools未安装，无法直接检查store状态');
        }
        
        // 尝试从React组件中获取状态
        try {
            const reactRoot = document.querySelector('[data-reactroot]') || document.querySelector('#__next');
            if (reactRoot) {
                console.log('✅ 找到React根节点');
            }
        } catch (error) {
            console.error('❌ 无法访问React组件:', error);
        }
    },

    // 检查API请求
    monitorAPIRequests() {
        console.log('🌐 [API Monitor] 开始监控API请求...');
        
        // 拦截fetch请求
        const originalFetch = window.fetch;
        window.fetch = async function(...args) {
            const [url, options] = args;
            console.log(`📡 [API Request] ${options?.method || 'GET'} ${url}`);
            
            try {
                const response = await originalFetch.apply(this, args);
                const clonedResponse = response.clone();
                
                // 如果是图内存API请求
                if (url.includes('/graph/memories') || url.includes('/v1/graph')) {
                    console.log(`✅ [API Response] ${response.status} ${url}`);
                    try {
                        const data = await clonedResponse.json();
                        console.log('📊 [Graph API Data]:', {
                            url,
                            status: response.status,
                            dataStructure: {
                                entities: Array.isArray(data?.entities) ? data.entities.length : 'undefined',
                                relationships: Array.isArray(data?.relationships) ? data.relationships.length : 'undefined',
                                relations: Array.isArray(data?.relations) ? data.relations.length : 'undefined',
                                total_entities: data?.total_entities,
                                total_relationships: data?.total_relationships,
                                total_relations: data?.total_relations
                            },
                            sampleEntity: data?.entities?.[0] ? {
                                id: data.entities[0].id,
                                name: data.entities[0].name,
                                label: data.entities[0].label,
                                type: data.entities[0].type
                            } : null,
                            sampleRelation: (data?.relationships || data?.relations)?.[0] ? {
                                id: (data.relationships || data.relations)[0].id,
                                source_entity: (data.relationships || data.relations)[0].source_entity,
                                target_entity: (data.relationships || data.relations)[0].target_entity,
                                relationship_type: (data.relationships || data.relations)[0].relationship_type,
                                relation_type: (data.relationships || data.relations)[0].relation_type
                            } : null
                        });
                    } catch (jsonError) {
                        console.warn('⚠️ [API Response] 无法解析JSON响应:', jsonError);
                    }
                }
                
                return response;
            } catch (error) {
                console.error(`❌ [API Error] ${url}:`, error);
                throw error;
            }
        };
        
        console.log('✅ API请求监控已激活');
    },

    // 检查DOM元素和数据显示
    checkDOMElements() {
        console.log('🔍 [DOM Check] 检查页面元素和数据显示...');
        
        // 检查统计数据显示
        const statsElements = {
            entities: document.querySelector('[data-testid="entities-count"], .text-muted-foreground:contains("entities")'),
            relations: document.querySelector('[data-testid="relations-count"], .text-muted-foreground:contains("relations")'),
            operations: document.querySelector('[data-testid="operations-count"], .text-muted-foreground:contains("operations")')
        };
        
        console.log('📊 [DOM Stats] 统计元素检查:', {
            entitiesFound: !!statsElements.entities,
            relationsFound: !!statsElements.relations,
            operationsFound: !!statsElements.operations
        });
        
        // 检查图可视化元素
        const graphContainer = document.querySelector('[class*="react-flow"], .graph-container, svg');
        console.log('🎨 [Graph Visualization]:', {
            containerFound: !!graphContainer,
            containerType: graphContainer?.tagName,
            hasChildren: graphContainer?.children?.length > 0
        });
        
        // 检查错误提示
        const errorElements = document.querySelectorAll('[class*="error"], [class*="destructive"], .text-red');
        if (errorElements.length > 0) {
            console.warn('⚠️ [Error Elements] 发现错误提示:', Array.from(errorElements).map(el => el.textContent));
        }
        
        // 检查加载状态
        const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spinner"], [class*="skeleton"]');
        console.log('⏳ [Loading State]:', {
            loadingElementsCount: loadingElements.length,
            isLoading: loadingElements.length > 0
        });
    },

    // 检查控制台错误
    checkConsoleErrors() {
        console.log('🐛 [Console Errors] 检查控制台错误...');
        
        // 重写console.error以捕获错误
        const originalError = console.error;
        console.error = function(...args) {
            console.log('🚨 [Captured Error]:', args);
            return originalError.apply(this, args);
        };
        
        // 监听未捕获的错误
        window.addEventListener('error', (event) => {
            console.log('🚨 [Uncaught Error]:', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });
        
        // 监听Promise rejection
        window.addEventListener('unhandledrejection', (event) => {
            console.log('🚨 [Unhandled Promise Rejection]:', event.reason);
        });
        
        console.log('✅ 错误监控已激活');
    },

    // 模拟数据加载
    simulateDataLoad() {
        console.log('🔄 [Simulate] 模拟数据加载...');
        
        // 尝试触发刷新按钮
        const refreshButton = document.querySelector('button[class*="refresh"], button:contains("Refresh")');
        if (refreshButton) {
            console.log('🔄 点击刷新按钮...');
            refreshButton.click();
        } else {
            console.warn('⚠️ 未找到刷新按钮');
        }
        
        // 检查是否有手动触发数据加载的方法
        if (window.location.pathname.includes('graph-memory')) {
            console.log('📍 当前在GraphMemory页面');
            // 可以尝试手动发起API请求进行测试
            const testApiCall = async () => {
                try {
                    const response = await fetch('http://localhost:8000/v1/graph/memories?user_id=test_bob&limit=10');
                    const data = await response.json();
                    console.log('🧪 [Test API] 手动API调用结果:', data);
                } catch (error) {
                    console.error('❌ [Test API] 手动API调用失败:', error);
                }
            };
            testApiCall();
        }
    },

    // 数据流追踪
    traceDataFlow() {
        console.log('🔍 [Data Flow] 开始追踪数据流...');
        
        // 定期检查页面状态
        const checkInterval = setInterval(() => {
            const stats = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                
                // DOM状态
                entitiesDisplayed: document.querySelector('.text-muted-foreground')?.textContent?.match(/(\d+)\s+entities/)?.[1] || '0',
                relationsDisplayed: document.querySelector('.text-muted-foreground')?.textContent?.match(/(\d+)\s+relations/)?.[1] || '0',
                
                // 加载状态
                hasLoadingElements: document.querySelectorAll('[class*="loading"], [class*="spinner"]').length > 0,
                hasErrorElements: document.querySelectorAll('[class*="error"], .text-red').length > 0,
                
                // 图可视化状态
                hasGraphElements: document.querySelectorAll('svg, canvas, [class*="react-flow"]').length > 0
            };
            
            console.log('📊 [Data Flow Snapshot]:', stats);
        }, 5000);
        
        // 30秒后停止追踪
        setTimeout(() => {
            clearInterval(checkInterval);
            console.log('⏹️ [Data Flow] 追踪结束');
        }, 30000);
    },

    // 运行完整诊断
    runFullDiagnosis() {
        console.log('🚀 [Full Diagnosis] 开始完整诊断...');
        
        this.checkReduxState();
        this.monitorAPIRequests();
        this.checkDOMElements();
        this.checkConsoleErrors();
        this.simulateDataLoad();
        this.traceDataFlow();
        
        console.log('✅ [Full Diagnosis] 诊断脚本已启动，请观察后续输出');
        console.log('💡 提示: 可以使用 GraphMemoryDebugger.checkDOMElements() 等方法单独运行诊断');
    }
};

// 自动运行诊断
console.log('🎯 运行 GraphMemoryDebugger.runFullDiagnosis() 开始完整诊断');
console.log('📋 可用方法: checkReduxState(), monitorAPIRequests(), checkDOMElements(), checkConsoleErrors(), simulateDataLoad(), traceDataFlow()');

// 等待页面加载完成后自动运行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => GraphMemoryDebugger.runFullDiagnosis(), 1000);
    });
} else {
    setTimeout(() => GraphMemoryDebugger.runFullDiagnosis(), 1000);
}