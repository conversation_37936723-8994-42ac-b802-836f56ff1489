#!/usr/bin/env python3
"""
测试Mem0自定义指令功能
包括自定义事实提取和自定义记忆更新指令
"""

import requests
import json
import time
import uuid

API_BASE = "http://localhost:8000"

def test_custom_instructions():
    """测试自定义指令功能"""
    print("📝 测试Mem0自定义指令功能")
    print("=" * 60)
    
    # 1. 测试默认行为（作为基准）
    print("\n=== 1. 测试默认事实提取行为 ===")
    
    default_user = f"default_test_{str(uuid.uuid4())[:8]}"
    
    try:
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{
                    "role": "user", 
                    "content": "I work at Google as a software engineer. I love hiking and my favorite programming language is Python. I'm working on a machine learning project."
                }],
                "user_id": default_user
            },
            timeout=30
        )
        
        print(f"默认行为响应状态: {response.status_code}")
        
        if response.status_code == 200:
            memories_created = response.json()
            print(f"默认创建的记忆数量: {len(memories_created)}")
            
            time.sleep(2)
            
            # 获取记忆
            get_response = requests.get(
                f"{API_BASE}/v1/memories/",
                params={"user_id": default_user, "limit": 50}
            )
            
            if get_response.status_code == 200:
                all_memories = get_response.json()
                print(f"默认记忆总数: {len(all_memories)}")
                
                print("默认提取的记忆:")
                for i, memory in enumerate(all_memories):
                    categories = memory.get('metadata', {}).get('categories', [])
                    print(f"  {i+1}. {memory.get('memory', '')} -> Categories: {categories}")
                
                print("✅ 默认行为记录完成")
            else:
                print(f"获取默认记忆失败: {get_response.status_code}")
        else:
            print(f"默认行为测试失败: {response.text[:200]}")
            
    except Exception as e:
        print(f"默认行为测试异常: {e}")
    
    # 2. 测试使用自定义事实提取指令
    print(f"\n=== 2. 测试自定义事实提取指令 ===")
    
    custom_user = f"custom_inst_{str(uuid.uuid4())[:8]}"
    
    # 注意：这个测试假设API支持自定义指令参数
    # 如果不支持，我们需要通过配置文件或其他方式测试
    try:
        # 首先测试是否支持custom_instruction参数
        custom_instruction = """
        You are a specialized memory extractor focused on professional and technical information.
        
        Rules:
        1. Extract only work-related facts, technical skills, and project information
        2. Ignore personal hobbies and preferences unless they relate to professional development
        3. Always include the company name and job title if mentioned
        4. Focus on technical tools, programming languages, and project details
        
        Return facts in this format: {"facts": ["fact1", "fact2"], "categories": ["category1"]}
        """
        
        # 尝试不同的参数名称来测试自定义指令支持
        test_payloads = [
            {
                "messages": [{
                    "role": "user", 
                    "content": "I work at Microsoft as a data scientist. I enjoy rock climbing and cooking. I'm building a recommendation system using TensorFlow and Python."
                }],
                "user_id": custom_user,
                "custom_instruction": custom_instruction
            },
            {
                "messages": [{
                    "role": "user", 
                    "content": "I work at Microsoft as a data scientist. I enjoy rock climbing and cooking. I'm building a recommendation system using TensorFlow and Python."
                }],
                "user_id": custom_user + "_v2",
                "custom_fact_extraction_prompt": custom_instruction
            },
            {
                "messages": [{
                    "role": "user", 
                    "content": "I work at Microsoft as a data scientist. I enjoy rock climbing and cooking. I'm building a recommendation system using TensorFlow and Python."
                }],
                "user_id": custom_user + "_v3", 
                "prompt": custom_instruction
            }
        ]
        
        custom_worked = False
        
        for i, payload in enumerate(test_payloads):
            print(f"\n  测试自定义指令变体 {i+1}...")
            
            try:
                response = requests.post(
                    f"{API_BASE}/v1/memories/",
                    json=payload,
                    timeout=30
                )
                
                print(f"  响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    memories_created = response.json()
                    print(f"  创建的记忆数量: {len(memories_created)}")
                    
                    if memories_created:
                        time.sleep(2)
                        
                        get_response = requests.get(
                            f"{API_BASE}/v1/memories/",
                            params={"user_id": payload["user_id"], "limit": 50}
                        )
                        
                        if get_response.status_code == 200:
                            all_memories = get_response.json()
                            print(f"  记忆总数: {len(all_memories)}")
                            
                            print("  自定义指令提取的记忆:")
                            professional_count = 0
                            personal_count = 0
                            
                            for j, memory in enumerate(all_memories):
                                memory_text = memory.get('memory', '').lower()
                                categories = memory.get('metadata', {}).get('categories', [])
                                print(f"    {j+1}. {memory.get('memory', '')} -> Categories: {categories}")
                                
                                # 检查是否更偏向专业内容
                                professional_keywords = ['work', 'microsoft', 'data scientist', 'tensorflow', 'python', 'recommendation system']
                                personal_keywords = ['rock climbing', 'cooking', 'enjoy']
                                
                                if any(keyword in memory_text for keyword in professional_keywords):
                                    professional_count += 1
                                if any(keyword in memory_text for keyword in personal_keywords):
                                    personal_count += 1
                            
                            print(f"  专业相关记忆: {professional_count}, 个人相关记忆: {personal_count}")
                            
                            if professional_count > personal_count:
                                print(f"  ✅ 自定义指令变体 {i+1} 工作正常 - 偏向专业内容!")
                                custom_worked = True
                            else:
                                print(f"  ❓ 自定义指令变体 {i+1} 效果不明显")
                        else:
                            print(f"  获取记忆失败: {get_response.status_code}")
                    else:
                        print("  没有创建记忆")
                elif response.status_code == 422:
                    print(f"  参数不支持: {response.text[:100]}")
                else:
                    print(f"  请求失败: {response.status_code}")
                    
            except Exception as e:
                print(f"  变体 {i+1} 测试异常: {e}")
        
        if custom_worked:
            print("✅ 找到了工作的自定义指令方式!")
        else:
            print("⚠️ 自定义指令功能可能不可用或需要不同的配置方式")
            
    except Exception as e:
        print(f"自定义指令测试异常: {e}")
    
    # 3. 测试通过配置检查自定义指令支持
    print(f"\n=== 3. 检查自定义指令配置支持 ===")
    
    try:
        # 尝试发送一个包含明确指令的消息，看系统如何响应
        instruction_user = f"instruction_test_{str(uuid.uuid4())[:8]}"
        
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{
                    "role": "system",
                    "content": "Extract only technical and professional information. Ignore personal hobbies."
                }, {
                    "role": "user", 
                    "content": "I'm a DevOps engineer at Amazon. I love playing video games and I'm implementing Kubernetes clusters."
                }],
                "user_id": instruction_user
            },
            timeout=30
        )
        
        print(f"系统消息测试响应状态: {response.status_code}")
        
        if response.status_code == 200:
            memories_created = response.json()
            print(f"系统消息创建的记忆数量: {len(memories_created)}")
            
            if memories_created:
                time.sleep(2)
                
                get_response = requests.get(
                    f"{API_BASE}/v1/memories/",
                    params={"user_id": instruction_user, "limit": 50}
                )
                
                if get_response.status_code == 200:
                    all_memories = get_response.json()
                    
                    print("系统消息测试结果:")
                    for i, memory in enumerate(all_memories):
                        print(f"  {i+1}. {memory.get('memory', '')}")
                    
                    # 检查是否遵循了系统指令
                    tech_count = 0
                    game_count = 0
                    
                    for memory in all_memories:
                        memory_text = memory.get('memory', '').lower()
                        if any(word in memory_text for word in ['devops', 'amazon', 'kubernetes', 'engineer']):
                            tech_count += 1
                        if any(word in memory_text for word in ['video games', 'playing', 'games']):
                            game_count += 1
                    
                    print(f"技术相关: {tech_count}, 游戏相关: {game_count}")
                    
                    if tech_count > 0 and game_count == 0:
                        print("✅ 系统消息指令有效!")
                    elif tech_count > game_count:
                        print("✅ 系统消息指令部分有效!")
                    else:
                        print("❓ 系统消息指令效果不明确")
                else:
                    print(f"获取系统消息测试记忆失败: {get_response.status_code}")
            else:
                print("系统消息测试没有创建记忆")
        else:
            print(f"系统消息测试失败: {response.text[:200]}")
            
    except Exception as e:
        print(f"系统消息测试异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 自定义指令功能测试完成!")

if __name__ == "__main__":
    test_custom_instructions()