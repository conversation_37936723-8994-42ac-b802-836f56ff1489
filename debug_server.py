#!/usr/bin/env python3
"""
简单的HTTP服务器，用于提供GraphMemory调试工具
运行: python3 debug_server.py
访问: http://localhost:8080
"""

import http.server
import socketserver
import os
import sys

class DebugHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS头部以允许跨域请求
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def do_GET(self):
        # 处理根路径请求
        if self.path == '/':
            self.path = '/debug_tools.html'
        
        # 处理脚本文件请求
        elif self.path == '/chrome_debug_script.js':
            self.send_response(200)
            self.send_header('Content-type', 'application/javascript')
            self.end_headers()
            try:
                with open('chrome_debug_script.js', 'r', encoding='utf-8') as f:
                    self.wfile.write(f.read().encode('utf-8'))
            except FileNotFoundError:
                self.wfile.write(b'console.error("Debug script not found");')
            return
            
        elif self.path == '/chrome_network_debug.js':
            self.send_response(200)
            self.send_header('Content-type', 'application/javascript')
            self.end_headers()
            try:
                with open('chrome_network_debug.js', 'r', encoding='utf-8') as f:
                    self.wfile.write(f.read().encode('utf-8'))
            except FileNotFoundError:
                self.wfile.write(b'console.error("Network debug script not found");')
            return
        
        # 默认处理
        super().do_GET()

def main():
    port = 8080
    
    # 确保在正确的目录中
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print(f"🚀 启动GraphMemory调试工具服务器...")
    print(f"📍 服务器地址: http://localhost:{port}")
    print(f"🔧 调试工具页面: http://localhost:{port}/debug_tools.html")
    print(f"📁 工作目录: {os.getcwd()}")
    print()
    print("📋 可用文件:")
    for file in ['debug_tools.html', 'chrome_debug_script.js', 'chrome_network_debug.js']:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (未找到)")
    print()
    print("🎯 使用说明:")
    print("  1. 访问 http://localhost:8080 打开调试工具")
    print("  2. 按照页面说明使用Chrome开发者工具")
    print("  3. 按 Ctrl+C 停止服务器")
    print()
    
    try:
        with socketserver.TCPServer(("", port), DebugHTTPRequestHandler) as httpd:
            print(f"✅ 服务器启动成功，监听端口 {port}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except OSError as e:
        print(f"❌ 启动失败: {e}")
        if "Address already in use" in str(e):
            print(f"💡 端口 {port} 已被占用，请尝试其他端口或关闭占用该端口的进程")
        sys.exit(1)

if __name__ == "__main__":
    main()