# Mem0 Docker 部署解决方案

## 问题解决摘要

✅ **问题已彻底解决**：Docker服务重构冲突、数据持久化和权限问题

## 解决方案概述

### 1. 清理旧环境
- 停止并删除所有冲突的容器
- 清理20个重复的数据卷
- 移除冲突的网络配置

### 2. 统一数据架构
- 采用绑定挂载替代命名卷
- 统一数据目录结构：`/opt/mem0ai/data/{mem0,qdrant,neo4j}`
- 确保跨主机迁移的兼容性

### 3. 权限管理
- Mem0: UID 1000:1000 (容器内mem0用户)
- Qdrant: UID 1000:1000 (默认用户)  
- Neo4j: UID 7474:7474 (官方容器用户)
- 提供自动化权限管理脚本

### 4. 配置优化
- 修改 `/opt/mem0ai/server/docker-compose.yaml`
- 使用绑定挂载确保权限正确
- 保留原有性能优化和健康检查

## 当前服务状态

```
✅ mem0-api      健康 (http://localhost:8000)
✅ mem0-qdrant   运行 (http://localhost:6333)  
✅ mem0-neo4j    健康 (http://localhost:7474)
```

## 数据持久化验证

```
/opt/mem0ai/data/
├── mem0/         (1000:1000) ✅ 包含.mem0、history.db、vector_store
├── qdrant/       (1000:1000) ✅ 包含collections、raft_state.json
└── neo4j/        (7474:7474) ✅ 包含data、logs、plugins目录
```

## 管理工具

### 权限管理脚本
```bash
# 重新配置权限
sudo /opt/mem0ai/setup-permissions.sh

# 仅验证权限
sudo /opt/mem0ai/setup-permissions.sh --verify-only

# 仅创建备份
sudo /opt/mem0ai/setup-permissions.sh --backup-only
```

### 服务管理
```bash
cd /opt/mem0ai/server

# 启动服务
docker compose up -d

# 查看状态
docker compose ps

# 查看日志
docker compose logs -f

# 停止服务
docker compose down
```

## 迁移部署保证

1. **权限自动化**：`setup-permissions.sh` 确保任何环境下权限正确
2. **数据持久化**：使用绑定挂载，数据直接存储在主机
3. **配置统一**：标准化的 docker-compose.yaml 配置
4. **健康检查**：完整的服务健康监控
5. **备份保护**：自动创建数据备份

## 故障排查

如果遇到权限问题：
```bash
sudo /opt/mem0ai/setup-permissions.sh
```

如果遇到容器冲突：
```bash
docker compose down
docker system prune -f
sudo /opt/mem0ai/setup-permissions.sh
docker compose up -d
```

---

✨ **部署完成**：Mem0 Docker 环境已彻底解决所有权限和持久化问题，可安全迁移部署！