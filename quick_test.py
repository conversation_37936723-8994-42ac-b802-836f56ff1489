#!/usr/bin/env python3
"""
Mem0 快速功能测试脚本
测试核心功能：记忆添加、Categories自动分类、搜索
"""

import requests
import json
import time

API_BASE = "http://localhost:8000"

def quick_test():
    print("🚀 开始Mem0快速功能测试")
    print("=" * 60)
    
    # 1. API健康检查
    try:
        response = requests.get(f"{API_BASE}/health", timeout=5)
        if response.status_code == 200:
            print("✓ API健康检查: 正常")
        else:
            print("✗ API健康检查: 失败")
            return
    except Exception as e:
        print(f"✗ API连接失败: {e}")
        return
    
    # 2. 测试记忆添加和Categories自动分类
    print("\n=== 测试记忆添加和Categories自动分类 ===")
    test_memories = [
        {
            "content": "My name is <PERSON> and I'm 28 years old. I work as a data scientist at Microsoft.",
            "expected_categories": ["personal_details", "professional_details"]
        },
        {
            "content": "I love playing tennis and hiking on weekends. I also enjoy reading science fiction books.",
            "expected_categories": ["sports", "hobbies", "entertainment"]
        },
        {
            "content": "I went to Paris last summer and visited the Eiffel Tower. Next year I want to go to Japan.",
            "expected_categories": ["travel"]
        }
    ]
    
    created_memories = []
    
    for i, test_case in enumerate(test_memories):
        try:
            response = requests.post(
                f"{API_BASE}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": test_case["content"]}],
                    "user_id": "quick_test_user"
                },
                timeout=15
            )
            
            if response.status_code == 200:
                memories = response.json()
                if memories:
                    print(f"✓ 记忆 {i+1}: 创建成功 ({len(memories)} 条)")
                    created_memories.extend(memories)
                    
                    # 检查Categories
                    memory_id = memories[0]['id']
                    detail_response = requests.get(f"{API_BASE}/v1/memories/{memory_id}", timeout=10)
                    if detail_response.status_code == 200:
                        detail = detail_response.json()
                        categories = detail.get('categories', [])
                        if categories:
                            print(f"  Categories: {categories}")
                        else:
                            print("  ⚠️ 未生成Categories")
                else:
                    print(f"✗ 记忆 {i+1}: 创建失败 - 返回空结果")
            else:
                print(f"✗ 记忆 {i+1}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"✗ 记忆 {i+1}: 异常 {e}")
            
        time.sleep(1)
    
    # 3. 测试记忆搜索
    print(f"\n=== 测试记忆搜索 ===")
    search_queries = [
        {"query": "What's my job?", "expected": ["data scientist", "Microsoft"]},
        {"query": "What sports do I like?", "expected": ["tennis"]},
        {"query": "Where did I travel?", "expected": ["Paris", "Eiffel Tower"]}
    ]
    
    for query_case in search_queries:
        try:
            response = requests.post(
                f"{API_BASE}/v2/memories/search/",
                json={
                    "query": query_case["query"],
                    "user_id": "quick_test_user",
                    "limit": 5
                },
                timeout=10
            )
            
            if response.status_code == 200:
                results = response.json()
                memories = results.get('results', [])
                if memories:
                    all_text = " ".join([mem.get('memory', '') for mem in memories]).lower()
                    found_keywords = [kw for kw in query_case['expected'] 
                                    if kw.lower() in all_text]
                    
                    if found_keywords:
                        print(f"✓ 搜索 '{query_case['query']}': 找到 {len(memories)} 条，匹配: {found_keywords}")
                    else:
                        print(f"⚠️ 搜索 '{query_case['query']}': 找到 {len(memories)} 条，但无匹配关键词")
                else:
                    print(f"✗ 搜索 '{query_case['query']}': 无结果")
            else:
                print(f"✗ 搜索 '{query_case['query']}': HTTP {response.status_code}")
                
        except Exception as e:
            print(f"✗ 搜索 '{query_case['query']}': 异常 {e}")
            
        time.sleep(1)
    
    # 4. 测试自定义Categories
    print(f"\n=== 测试自定义Categories ===")
    try:
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": "I completed my machine learning certification from Stanford."}],
                "user_id": "custom_cat_user",
                "custom_categories": ["education", "achievement"]
            },
            timeout=15
        )
        
        if response.status_code == 200:
            memories = response.json()
            if memories:
                memory_id = memories[0]['id']
                detail_response = requests.get(f"{API_BASE}/v1/memories/{memory_id}", timeout=10)
                if detail_response.status_code == 200:
                    detail = detail_response.json()
                    categories = detail.get('categories', [])
                    
                    if 'education' in categories and 'achievement' in categories:
                        print(f"✓ 自定义Categories: 成功使用自定义分类 {categories}")
                    else:
                        print(f"⚠️ 自定义Categories: 分类不正确 {categories}")
                else:
                    print("✗ 自定义Categories: 无法获取记忆详情")
            else:
                print("✗ 自定义Categories: 未创建记忆")
        else:
            print(f"✗ 自定义Categories: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"✗ 自定义Categories: 异常 {e}")
    
    # 5. 统计总记忆数
    print(f"\n=== 记忆统计 ===")
    try:
        response = requests.get(
            f"{API_BASE}/v1/memories/",
            params={"user_id": "quick_test_user", "limit": 100},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            memories = data.get('results', [])
            categorized_count = sum(1 for mem in memories if mem.get('categories'))
            
            print(f"✓ 用户记忆总数: {len(memories)}")
            print(f"✓ 有分类的记忆: {categorized_count}")
            print(f"✓ Categories覆盖率: {categorized_count/len(memories)*100:.1f}%" if memories else "0%")
        else:
            print(f"✗ 记忆统计: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"✗ 记忆统计: 异常 {e}")
    
    print("\n" + "=" * 60)
    print("🎯 快速测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    quick_test()