/**
 * 网络请求和数据转换专项检查脚本
 * 专门诊断GraphMemory数据显示不一致问题
 */

console.log('🔍 [Network Debug] GraphMemory网络请求和数据转换检查脚本');

window.GraphMemoryNetworkDebugger = {
    
    // 详细的API响应分析
    async analyzeAPIResponse() {
        console.log('📡 [API Analysis] 开始分析API响应...');
        
        try {
            // 直接调用API获取原始数据
            const response = await fetch('http://localhost:8000/v1/graph/memories?user_id=test_bob&limit=10');
            const rawData = await response.json();
            
            console.log('📊 [Raw API Data] 后端原始数据:', {
                responseStatus: response.status,
                dataKeys: Object.keys(rawData),
                entitiesType: typeof rawData.entities,
                entitiesIsArray: Array.isArray(rawData.entities),
                entitiesCount: rawData.entities?.length || 0,
                relationshipsType: typeof rawData.relationships,
                relationshipsIsArray: Array.isArray(rawData.relationships),
                relationshipsCount: rawData.relationships?.length || 0,
                relationsExists: 'relations' in rawData,
                totalEntities: rawData.total_entities,
                totalRelationships: rawData.total_relationships,
                totalRelations: rawData.total_relations
            });
            
            // 详细检查实体结构
            if (rawData.entities?.length > 0) {
                const sampleEntity = rawData.entities[0];
                console.log('🏷️ [Entity Structure] 实体数据结构:', {
                    id: sampleEntity.id,
                    name: sampleEntity.name,
                    label: sampleEntity.label,
                    type: sampleEntity.type,
                    hasProperties: !!sampleEntity.properties,
                    allKeys: Object.keys(sampleEntity)
                });
            }
            
            // 详细检查关系结构
            if (rawData.relationships?.length > 0) {
                const sampleRelation = rawData.relationships[0];
                console.log('🔗 [Relationship Structure] 关系数据结构:', {
                    id: sampleRelation.id,
                    source_entity: sampleRelation.source_entity,
                    source_entity_id: sampleRelation.source_entity_id,
                    target_entity: sampleRelation.target_entity,
                    target_entity_id: sampleRelation.target_entity_id,
                    relationship_type: sampleRelation.relationship_type,
                    relation_type: sampleRelation.relation_type,
                    allKeys: Object.keys(sampleRelation)
                });
            }
            
            // 模拟前端数据转换过程
            console.log('🔄 [Data Transformation] 模拟前端数据转换...');
            
            // 步骤1: 客户端转换 (realClient.ts)
            const clientTransformed = {
                entities: rawData.entities || [],
                relations: rawData.relationships || [], // 关键转换: relationships -> relations
                metadata: {
                    total_entities: rawData.total_entities || 0,
                    total_relations: rawData.total_relationships || 0, // 关键转换: total_relationships -> total_relations
                    ...rawData.metadata
                }
            };
            
            console.log('✅ [Client Transform] 客户端转换结果:', {
                entitiesCount: clientTransformed.entities.length,
                relationsCount: clientTransformed.relations.length,
                metadataTotalEntities: clientTransformed.metadata.total_entities,
                metadataTotalRelations: clientTransformed.metadata.total_relations
            });
            
            // 步骤2: 数据转换器 (graph-data-transformer.ts)
            const transformToReactFlow = (graphData) => {
                const entities = graphData?.entities || [];
                const relations = graphData?.relations || [];
                
                const nodes = entities.map((entity, index) => ({
                    id: entity.id,
                    type: 'default',
                    position: { x: Math.random() * 500, y: Math.random() * 500 },
                    data: {
                        id: entity.id,
                        label: entity.label || entity.name || entity.id, // 字段映射
                        type: entity.type?.toLowerCase() || 'default',
                        description: entity.description || '',
                        properties: entity.properties || {}
                    }
                }));
                
                const edges = relations.map(relation => ({
                    id: relation.id,
                    source: relation.source_entity || relation.source_entity_id, // 字段映射
                    target: relation.target_entity || relation.target_entity_id, // 字段映射
                    type: 'default',
                    data: {
                        id: relation.id,
                        label: relation.relationship_type || relation.relation_type || 'related_to', // 字段映射
                        relation_type: relation.relationship_type || relation.relation_type || 'related_to'
                    }
                }));
                
                return { nodes, edges };
            };
            
            const transformed = transformToReactFlow(clientTransformed);
            
            console.log('🎯 [Final Transform] 最终转换结果:', {
                nodesCount: transformed.nodes.length,
                edgesCount: transformed.edges.length,
                sampleNode: transformed.nodes[0] ? {
                    id: transformed.nodes[0].id,
                    label: transformed.nodes[0].data.label,
                    type: transformed.nodes[0].data.type
                } : null,
                sampleEdge: transformed.edges[0] ? {
                    id: transformed.edges[0].id,
                    source: transformed.edges[0].source,
                    target: transformed.edges[0].target,
                    label: transformed.edges[0].data.label
                } : null
            });
            
            // 验证数据一致性
            const isConsistent = (
                rawData.entities.length === transformed.nodes.length &&
                rawData.relationships.length === transformed.edges.length
            );
            
            console.log('✔️ [Consistency Check] 数据一致性验证:', {
                consistent: isConsistent,
                backendEntities: rawData.entities.length,
                frontendNodes: transformed.nodes.length,
                backendRelationships: rawData.relationships.length,
                frontendEdges: transformed.edges.length
            });
            
            return {
                rawData,
                clientTransformed,
                transformed,
                isConsistent
            };
            
        } catch (error) {
            console.error('❌ [API Analysis] API分析失败:', error);
            return null;
        }
    },
    
    // 检查React组件状态
    inspectReactState() {
        console.log('⚛️ [React Inspect] 检查React组件状态...');
        
        // 尝试通过React DevTools获取组件状态
        if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
            console.log('✅ React DevTools已安装');
            
            // 尝试获取React Fiber
            const reactFiber = document.querySelector('#__next')?._reactInternals || 
                              document.querySelector('#__next')?._reactInternalFiber;
            
            if (reactFiber) {
                console.log('✅ 找到React Fiber节点');
                // 这里可以进一步探索组件树
            }
        } else {
            console.warn('⚠️ React DevTools未安装');
        }
        
        // 检查Redux store（如果可访问）
        const reduxScript = `
            // 尝试访问Redux store
            if (window.__REDUX_DEVTOOLS_EXTENSION__) {
                console.log('Redux DevTools可用');
            }
        `;
        
        try {
            eval(reduxScript);
        } catch (error) {
            console.log('Redux访问受限:', error.message);
        }
    },
    
    // 实时监控数据更新
    monitorDataUpdates() {
        console.log('👀 [Data Monitor] 开始监控数据更新...');
        
        let lastCheck = {
            entities: 0,
            relations: 0,
            timestamp: Date.now()
        };
        
        const monitor = setInterval(() => {
            // 检查DOM中显示的数据
            const entitiesText = document.body.textContent.match(/(\d+)\s+entities/);
            const relationsText = document.body.textContent.match(/(\d+)\s+relations/);
            
            const currentCheck = {
                entities: entitiesText ? parseInt(entitiesText[1]) : 0,
                relations: relationsText ? parseInt(relationsText[1]) : 0,
                timestamp: Date.now()
            };
            
            if (currentCheck.entities !== lastCheck.entities || 
                currentCheck.relations !== lastCheck.relations) {
                console.log('📊 [Data Update] 数据已更新:', {
                    previous: lastCheck,
                    current: currentCheck,
                    timeDiff: currentCheck.timestamp - lastCheck.timestamp
                });
                lastCheck = currentCheck;
            }
        }, 2000);
        
        // 10秒后停止监控
        setTimeout(() => {
            clearInterval(monitor);
            console.log('⏹️ [Data Monitor] 监控结束');
        }, 30000);
        
        return monitor;
    },
    
    // 检查本地存储和缓存
    checkLocalStorage() {
        console.log('💾 [Storage Check] 检查本地存储和缓存...');
        
        const storage = {
            localStorage: {},
            sessionStorage: {},
            cookies: document.cookie
        };
        
        // 检查localStorage
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) {
                storage.localStorage[key] = localStorage.getItem(key);
            }
        }
        
        // 检查sessionStorage
        for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            if (key) {
                storage.sessionStorage[key] = sessionStorage.getItem(key);
            }
        }
        
        console.log('💾 [Storage Data] 存储数据:', storage);
        
        // 检查是否有缓存的图数据
        const graphCacheKeys = Object.keys(storage.localStorage).filter(key => 
            key.includes('graph') || key.includes('memory') || key.includes('redux')
        );
        
        if (graphCacheKeys.length > 0) {
            console.log('🗂️ [Graph Cache] 发现图相关缓存:', graphCacheKeys);
        }
    },
    
    // 运行完整网络诊断
    async runNetworkDiagnosis() {
        console.log('🚀 [Network Diagnosis] 开始完整网络诊断...');
        
        const results = await this.analyzeAPIResponse();
        this.inspectReactState();
        this.checkLocalStorage();
        const monitor = this.monitorDataUpdates();
        
        console.log('✅ [Network Diagnosis] 网络诊断完成');
        return results;
    }
};

// 提供使用说明
console.log('📋 [Usage] 可用方法:');
console.log('  - GraphMemoryNetworkDebugger.analyzeAPIResponse() - 分析API响应');
console.log('  - GraphMemoryNetworkDebugger.inspectReactState() - 检查React状态');
console.log('  - GraphMemoryNetworkDebugger.monitorDataUpdates() - 监控数据更新');
console.log('  - GraphMemoryNetworkDebugger.checkLocalStorage() - 检查本地存储');
console.log('  - GraphMemoryNetworkDebugger.runNetworkDiagnosis() - 运行完整诊断');

console.log('🎯 自动运行网络诊断...');
GraphMemoryNetworkDebugger.runNetworkDiagnosis();