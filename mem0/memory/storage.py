import hashlib
import json
import logging
import sqlite3
import threading
import time
import uuid
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class SQLiteManager:
    def __init__(self, db_path: str = ":memory:"):
        self.db_path = db_path
        self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
        self._lock = threading.Lock()
        self._migrate_history_table()
        self._create_history_table()
        self._create_category_cache_table()

    def _migrate_history_table(self) -> None:
        """
        If a pre-existing history table had the old group-chat columns,
        rename it, create the new schema, copy the intersecting data, then
        drop the old table.
        """
        with self._lock:
            try:
                # Start a transaction
                self.connection.execute("BEGIN")
                cur = self.connection.cursor()

                cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='history'")
                if cur.fetchone() is None:
                    self.connection.execute("COMMIT")
                    return  # nothing to migrate

                cur.execute("PRAGMA table_info(history)")
                old_cols = {row[1] for row in cur.fetchall()}

                expected_cols = {
                    "id",
                    "memory_id",
                    "old_memory",
                    "new_memory",
                    "event",
                    "created_at",
                    "updated_at",
                    "is_deleted",
                    "actor_id",
                    "role",
                }

                if old_cols == expected_cols:
                    self.connection.execute("COMMIT")
                    return

                logger.info("Migrating history table to new schema (no convo columns).")

                # Clean up any existing history_old table from previous failed migration
                cur.execute("DROP TABLE IF EXISTS history_old")

                # Rename the current history table
                cur.execute("ALTER TABLE history RENAME TO history_old")

                # Create the new history table with updated schema
                cur.execute(
                    """
                    CREATE TABLE history (
                        id           TEXT PRIMARY KEY,
                        memory_id    TEXT,
                        old_memory   TEXT,
                        new_memory   TEXT,
                        event        TEXT,
                        created_at   DATETIME,
                        updated_at   DATETIME,
                        is_deleted   INTEGER,
                        actor_id     TEXT,
                        role         TEXT
                    )
                """
                )

                # Copy data from old table to new table
                intersecting = list(expected_cols & old_cols)
                if intersecting:
                    cols_csv = ", ".join(intersecting)
                    cur.execute(f"INSERT INTO history ({cols_csv}) SELECT {cols_csv} FROM history_old")

                # Drop the old table
                cur.execute("DROP TABLE history_old")

                # Commit the transaction
                self.connection.execute("COMMIT")
                logger.info("History table migration completed successfully.")

            except Exception as e:
                # Rollback the transaction on any error
                self.connection.execute("ROLLBACK")
                logger.error(f"History table migration failed: {e}")
                raise

    def _create_history_table(self) -> None:
        with self._lock:
            try:
                self.connection.execute("BEGIN")
                self.connection.execute(
                    """
                    CREATE TABLE IF NOT EXISTS history (
                        id           TEXT PRIMARY KEY,
                        memory_id    TEXT,
                        old_memory   TEXT,
                        new_memory   TEXT,
                        event        TEXT,
                        created_at   DATETIME,
                        updated_at   DATETIME,
                        is_deleted   INTEGER,
                        actor_id     TEXT,
                        role         TEXT
                    )
                """
                )
                self.connection.execute("COMMIT")
            except Exception as e:
                self.connection.execute("ROLLBACK")
                logger.error(f"Failed to create history table: {e}")
                raise

    def _create_category_cache_table(self) -> None:
        """Create the category_cache table for caching LLM-generated categories."""
        with self._lock:
            try:
                self.connection.execute("BEGIN")
                self.connection.execute(
                    """
                    CREATE TABLE IF NOT EXISTS category_cache (
                        id              TEXT PRIMARY KEY,
                        content_hash    TEXT UNIQUE,
                        content_text    TEXT,
                        categories      TEXT,
                        source_type     TEXT,
                        user_id         TEXT,
                        agent_id        TEXT,
                        created_at      DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at      DATETIME DEFAULT CURRENT_TIMESTAMP,
                        access_count    INTEGER DEFAULT 0,
                        last_accessed   DATETIME DEFAULT CURRENT_TIMESTAMP,
                        similarity_score REAL DEFAULT 1.0
                    )
                """
                )
                
                # Create indexes for better performance
                self.connection.execute(
                    "CREATE INDEX IF NOT EXISTS idx_category_cache_content_hash ON category_cache (content_hash)"
                )
                self.connection.execute(
                    "CREATE INDEX IF NOT EXISTS idx_category_cache_user_agent ON category_cache (user_id, agent_id)"
                )
                self.connection.execute(
                    "CREATE INDEX IF NOT EXISTS idx_category_cache_last_accessed ON category_cache (last_accessed)"
                )
                
                self.connection.execute("COMMIT")
                logger.debug("Category cache table created successfully")
            except Exception as e:
                self.connection.execute("ROLLBACK")
                logger.error(f"Failed to create category_cache table: {e}")
                raise

    def add_history(
        self,
        memory_id: str,
        old_memory: Optional[str],
        new_memory: Optional[str],
        event: str,
        *,
        created_at: Optional[str] = None,
        updated_at: Optional[str] = None,
        is_deleted: int = 0,
        actor_id: Optional[str] = None,
        role: Optional[str] = None,
    ) -> None:
        with self._lock:
            try:
                self.connection.execute("BEGIN")
                self.connection.execute(
                    """
                    INSERT INTO history (
                        id, memory_id, old_memory, new_memory, event,
                        created_at, updated_at, is_deleted, actor_id, role
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        str(uuid.uuid4()),
                        memory_id,
                        old_memory,
                        new_memory,
                        event,
                        created_at,
                        updated_at,
                        is_deleted,
                        actor_id,
                        role,
                    ),
                )
                self.connection.execute("COMMIT")
            except Exception as e:
                self.connection.execute("ROLLBACK")
                logger.error(f"Failed to add history record: {e}")
                raise

    def get_history(self, memory_id: str) -> List[Dict[str, Any]]:
        with self._lock:
            cur = self.connection.execute(
                """
                SELECT id, memory_id, old_memory, new_memory, event,
                       created_at, updated_at, is_deleted, actor_id, role
                FROM history
                WHERE memory_id = ?
                ORDER BY created_at ASC, DATETIME(updated_at) ASC
            """,
                (memory_id,),
            )
            rows = cur.fetchall()

        return [
            {
                "id": r[0],
                "memory_id": r[1],
                "old_memory": r[2],
                "new_memory": r[3],
                "event": r[4],
                "created_at": r[5],
                "updated_at": r[6],
                "is_deleted": bool(r[7]),
                "actor_id": r[8],
                "role": r[9],
            }
            for r in rows
        ]

    def add_category_cache(
        self,
        content_text: str,
        categories: List[str],
        source_type: str = "auto",
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        similarity_score: float = 1.0
    ) -> str:
        """Add a new category cache entry."""
        with self._lock:
            try:
                # Generate content hash
                content_hash = hashlib.sha256(content_text.encode('utf-8')).hexdigest()
                cache_id = str(uuid.uuid4())
                current_time = time.time()
                
                self.connection.execute("BEGIN")
                self.connection.execute(
                    """
                    INSERT OR REPLACE INTO category_cache (
                        id, content_hash, content_text, categories, source_type,
                        user_id, agent_id, created_at, updated_at, access_count,
                        last_accessed, similarity_score
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        cache_id,
                        content_hash,
                        content_text,
                        json.dumps(categories),
                        source_type,
                        user_id,
                        agent_id,
                        current_time,
                        current_time,
                        0,
                        current_time,
                        similarity_score
                    ),
                )
                self.connection.execute("COMMIT")
                logger.debug(f"Added category cache entry with hash: {content_hash}")
                return cache_id
            except Exception as e:
                self.connection.execute("ROLLBACK")
                logger.error(f"Failed to add category cache: {e}")
                raise

    def get_category_cache_by_hash(
        self,
        content_hash: str,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Get category cache by exact content hash."""
        with self._lock:
            query = """
                SELECT id, content_hash, content_text, categories, source_type,
                       user_id, agent_id, created_at, updated_at, access_count,
                       last_accessed, similarity_score
                FROM category_cache
                WHERE content_hash = ?
            """
            params = [content_hash]
            
            # Add user/agent filtering if provided
            if user_id is not None:
                query += " AND (user_id = ? OR user_id IS NULL)"
                params.append(user_id)
            if agent_id is not None:
                query += " AND (agent_id = ? OR agent_id IS NULL)"
                params.append(agent_id)
                
            query += " ORDER BY created_at DESC LIMIT 1"
            
            cur = self.connection.execute(query, params)
            row = cur.fetchone()
            
            if row:
                # Update access statistics
                self._update_cache_access(row[0])
                
                return {
                    "id": row[0],
                    "content_hash": row[1],
                    "content_text": row[2],
                    "categories": json.loads(row[3]) if row[3] else [],
                    "source_type": row[4],
                    "user_id": row[5],
                    "agent_id": row[6],
                    "created_at": row[7],
                    "updated_at": row[8],
                    "access_count": row[9],
                    "last_accessed": row[10],
                    "similarity_score": row[11]
                }
            return None

    def get_category_cache_by_content(
        self,
        content_text: str,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Get category cache by content text (generates hash automatically)."""
        content_hash = hashlib.sha256(content_text.encode('utf-8')).hexdigest()
        return self.get_category_cache_by_hash(content_hash, user_id, agent_id)

    def search_similar_category_cache(
        self,
        content_text: str,
        limit: int = 5,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Search for similar category cache entries (basic text similarity for now)."""
        with self._lock:
            query = """
                SELECT id, content_hash, content_text, categories, source_type,
                       user_id, agent_id, created_at, updated_at, access_count,
                       last_accessed, similarity_score
                FROM category_cache
                WHERE content_text LIKE ?
            """
            params = [f"%{content_text[:100]}%"]  # Simple substring matching
            
            # Add user/agent filtering if provided
            if user_id is not None:
                query += " AND (user_id = ? OR user_id IS NULL)"
                params.append(user_id)
            if agent_id is not None:
                query += " AND (agent_id = ? OR agent_id IS NULL)"
                params.append(agent_id)
                
            query += " ORDER BY access_count DESC, last_accessed DESC LIMIT ?"
            params.append(limit)
            
            cur = self.connection.execute(query, params)
            rows = cur.fetchall()
            
            results = []
            for row in rows:
                results.append({
                    "id": row[0],
                    "content_hash": row[1],
                    "content_text": row[2],
                    "categories": json.loads(row[3]) if row[3] else [],
                    "source_type": row[4],
                    "user_id": row[5],
                    "agent_id": row[6],
                    "created_at": row[7],
                    "updated_at": row[8],
                    "access_count": row[9],
                    "last_accessed": row[10],
                    "similarity_score": row[11]
                })
            
            return results

    def _update_cache_access(self, cache_id: str) -> None:
        """Update access statistics for a cache entry."""
        try:
            current_time = time.time()
            self.connection.execute(
                """
                UPDATE category_cache 
                SET access_count = access_count + 1,
                    last_accessed = ?,
                    updated_at = ?
                WHERE id = ?
                """,
                (current_time, current_time, cache_id)
            )
            self.connection.commit()
        except Exception as e:
            logger.error(f"Failed to update cache access: {e}")

    def cleanup_category_cache(
        self,
        max_age_days: int = 30,
        max_entries: int = 1000
    ) -> int:
        """Clean up old category cache entries."""
        with self._lock:
            try:
                cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)
                
                self.connection.execute("BEGIN")
                
                # Remove old entries
                cur = self.connection.execute(
                    "DELETE FROM category_cache WHERE last_accessed < ?",
                    (cutoff_time,)
                )
                deleted_count = cur.rowcount
                
                # Keep only the most recently accessed entries if we exceed max_entries
                cur = self.connection.execute(
                    "SELECT COUNT(*) FROM category_cache"
                )
                total_count = cur.fetchone()[0]
                
                if total_count > max_entries:
                    excess = total_count - max_entries
                    self.connection.execute(
                        """
                        DELETE FROM category_cache 
                        WHERE id IN (
                            SELECT id FROM category_cache 
                            ORDER BY last_accessed ASC 
                            LIMIT ?
                        )
                        """,
                        (excess,)
                    )
                    deleted_count += excess
                
                self.connection.execute("COMMIT")
                logger.info(f"Cleaned up {deleted_count} category cache entries")
                return deleted_count
                
            except Exception as e:
                self.connection.execute("ROLLBACK")
                logger.error(f"Failed to cleanup category cache: {e}")
                raise

    def get_category_cache_stats(self) -> Dict[str, Any]:
        """Get statistics about the category cache."""
        with self._lock:
            cur = self.connection.execute(
                """
                SELECT 
                    COUNT(*) as total_entries,
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(DISTINCT agent_id) as unique_agents,
                    AVG(access_count) as avg_access_count,
                    MAX(last_accessed) as latest_access,
                    MIN(created_at) as oldest_entry
                FROM category_cache
                """
            )
            row = cur.fetchone()
            
            return {
                "total_entries": row[0] or 0,
                "unique_users": row[1] or 0,
                "unique_agents": row[2] or 0,
                "avg_access_count": row[3] or 0,
                "latest_access": row[4],
                "oldest_entry": row[5]
            }

    def reset(self) -> None:
        """Drop and recreate all tables."""
        with self._lock:
            try:
                self.connection.execute("BEGIN")
                self.connection.execute("DROP TABLE IF EXISTS history")
                self.connection.execute("DROP TABLE IF EXISTS category_cache")
                self.connection.execute("COMMIT")
                self._create_history_table()
                self._create_category_cache_table()
            except Exception as e:
                self.connection.execute("ROLLBACK")
                logger.error(f"Failed to reset tables: {e}")
                raise

    def close(self) -> None:
        if self.connection:
            self.connection.close()
            self.connection = None

    def __del__(self):
        self.close()
