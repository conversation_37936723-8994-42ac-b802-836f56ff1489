import hashlib
import logging
from typing import Any, Dict, List, Optional, Union

from mem0.memory.storage import SQLiteManager

logger = logging.getLogger(__name__)


class CategoryCacheManager:
    """
    智能分类缓存管理器
    
    功能特性:
    1. 基于内容hash的精确匹配缓存
    2. 支持custom_categories与auto_generated_categories共用
    3. 多租户隔离（user_id/agent_id维度）
    4. 访问统计和LRU策略
    5. 缓存清理和维护
    """
    
    def __init__(self, sqlite_manager: SQLiteManager, embedding_model=None):
        """
        初始化分类缓存管理器
        
        Args:
            sqlite_manager: SQLite数据库管理器实例
            embedding_model: 可选的embedding模型，用于语义相似度匹配
        """
        self.sqlite_manager = sqlite_manager
        self.embedding_model = embedding_model
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "total_queries": 0
        }
    
    def get_categories_from_cache(
        self,
        content: str,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        similarity_threshold: float = 0.8
    ) -> Optional[Dict[str, Any]]:
        """
        从缓存中获取分类
        
        Args:
            content: 内容文本
            user_id: 用户ID（可选）
            agent_id: 代理ID（可选）
            similarity_threshold: 语义相似度阈值
            
        Returns:
            缓存的分类结果，如果未找到返回None
        """
        self.cache_stats["total_queries"] += 1
        
        # 1. 精确匹配查询
        exact_match = self.sqlite_manager.get_category_cache_by_content(
            content, user_id, agent_id
        )
        
        if exact_match:
            self.cache_stats["hits"] += 1
            logger.debug(f"Cache hit (exact): content_hash={exact_match['content_hash']}")
            return exact_match
        
        # 2. 语义相似度匹配（如果有embedding模型）
        if self.embedding_model and similarity_threshold > 0:
            similar_entries = self._find_similar_categories(
                content, user_id, agent_id, similarity_threshold
            )
            
            if similar_entries:
                self.cache_stats["hits"] += 1
                logger.debug(f"Cache hit (similar): similarity={similar_entries[0]['similarity_score']}")
                return similar_entries[0]
        
        # 3. 基础文本相似度匹配
        text_similar = self.sqlite_manager.search_similar_category_cache(
            content, limit=1, user_id=user_id, agent_id=agent_id
        )
        
        if text_similar and len(text_similar) > 0:
            # 简单的文本相似度检查
            similarity = self._calculate_text_similarity(content, text_similar[0]['content_text'])
            if similarity >= similarity_threshold:
                self.cache_stats["hits"] += 1
                logger.debug(f"Cache hit (text similar): similarity={similarity}")
                return text_similar[0]
        
        self.cache_stats["misses"] += 1
        logger.debug("Cache miss: no matching categories found")
        return None
    
    def add_categories_to_cache(
        self,
        content: str,
        categories: List[str],
        source_type: str = "auto",
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        custom_categories: Optional[List[Dict[str, str]]] = None
    ) -> str:
        """
        添加分类到缓存
        
        Args:
            content: 内容文本
            categories: 分类列表
            source_type: 分类来源类型 ('auto', 'custom', 'merged')
            user_id: 用户ID（可选）
            agent_id: 代理ID（可选）
            custom_categories: 自定义分类（可选）
            
        Returns:
            缓存条目ID
        """
        # 合并自定义分类和自动生成分类
        final_categories = categories.copy()
        final_source_type = source_type
        
        if custom_categories:
            # 提取custom_categories中的分类名称
            custom_cat_names = []
            for cat_dict in custom_categories:
                custom_cat_names.extend(cat_dict.keys())
            
            # 合并分类，去重
            all_categories = list(set(custom_cat_names + categories))
            final_categories = all_categories
            final_source_type = "merged" if categories else "custom"
            
            logger.debug(f"Merged categories: custom={custom_cat_names}, auto={categories}, final={final_categories}")
        
        # 添加到缓存
        cache_id = self.sqlite_manager.add_category_cache(
            content_text=content,
            categories=final_categories,
            source_type=final_source_type,
            user_id=user_id,
            agent_id=agent_id
        )
        
        logger.debug(f"Added categories to cache: {len(final_categories)} categories, cache_id={cache_id}")
        return cache_id
    
    def merge_categories(
        self,
        auto_categories: List[str],
        custom_categories: Optional[List[Dict[str, str]]] = None,
        cached_categories: Optional[List[str]] = None
    ) -> List[str]:
        """
        智能合并不同来源的分类
        
        Args:
            auto_categories: LLM自动生成的分类
            custom_categories: 用户自定义分类
            cached_categories: 缓存中的分类
            
        Returns:
            合并后的分类列表
        """
        merged = set()
        
        # 1. 优先级: custom_categories > cached_categories > auto_categories
        if custom_categories:
            for cat_dict in custom_categories:
                merged.update(cat_dict.keys())
            logger.debug(f"Using custom categories: {list(merged)}")
            return list(merged)
        
        # 2. 如果有缓存分类，优先使用缓存
        if cached_categories:
            merged.update(cached_categories)
            logger.debug(f"Using cached categories: {list(merged)}")
        
        # 3. 补充自动生成的分类
        if auto_categories:
            merged.update(auto_categories)
            logger.debug(f"Merged with auto categories: {list(merged)}")
        
        return list(merged)
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算简单的文本相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            相似度分数 (0-1)
        """
        # 简单的基于词汇重叠的相似度计算
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _find_similar_categories(
        self,
        content: str,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        threshold: float = 0.8,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        基于embedding查找相似的分类（未来扩展功能）
        
        Args:
            content: 内容文本
            user_id: 用户ID
            agent_id: 代理ID
            threshold: 相似度阈值
            limit: 返回结果数量限制
            
        Returns:
            相似的分类条目列表
        """
        if not self.embedding_model:
            return []
        
        try:
            # 这里可以实现基于embedding的语义相似度匹配
            # 目前返回空列表，作为未来扩展点
            logger.debug("Embedding-based similarity search not yet implemented")
            return []
            
        except Exception as e:
            logger.error(f"Error in semantic similarity search: {e}")
            return []
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计数据
        """
        db_stats = self.sqlite_manager.get_category_cache_stats()
        
        # 计算命中率
        hit_rate = 0.0
        if self.cache_stats["total_queries"] > 0:
            hit_rate = self.cache_stats["hits"] / self.cache_stats["total_queries"]
        
        return {
            **db_stats,
            "session_hits": self.cache_stats["hits"],
            "session_misses": self.cache_stats["misses"],
            "session_queries": self.cache_stats["total_queries"],
            "hit_rate": hit_rate
        }
    
    def cleanup_cache(
        self,
        max_age_days: int = 30,
        max_entries: int = 1000
    ) -> int:
        """
        清理过期的缓存条目
        
        Args:
            max_age_days: 最大保留天数
            max_entries: 最大条目数
            
        Returns:
            清理的条目数量
        """
        deleted_count = self.sqlite_manager.cleanup_category_cache(
            max_age_days=max_age_days,
            max_entries=max_entries
        )
        
        logger.info(f"Cleaned up {deleted_count} cache entries")
        return deleted_count
    
    def reset_session_stats(self) -> None:
        """重置会话统计"""
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "total_queries": 0
        }
        logger.debug("Reset session cache statistics")
    
    def generate_content_hash(self, content: str) -> str:
        """
        生成内容的SHA256哈希值
        
        Args:
            content: 内容文本
            
        Returns:
            SHA256哈希值
        """
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def is_cache_enabled(self) -> bool:
        """检查缓存是否启用"""
        return self.sqlite_manager is not None