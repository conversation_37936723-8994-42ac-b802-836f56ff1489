<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphMemory调试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #00d4aa;
            padding-bottom: 10px;
        }
        .tool-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #00d4aa;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .button:hover {
            background: #00b894;
        }
        .button.secondary {
            background: #6c757d;
        }
        .button.secondary:hover {
            background: #5a6268;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            background: #e8f4fd;
            border-left: 4px solid #0066cc;
            border-radius: 0 5px 5px 0;
        }
        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border-left: 4px solid #28a745;
            color: #155724;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background: #e9ecef;
            border: 1px solid #ddd;
            cursor: pointer;
            border-bottom: none;
        }
        .tab.active {
            background: white;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
        }
        .tab-content {
            display: none;
            border: 1px solid #ddd;
            padding: 20px;
            background: white;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 GraphMemory 调试工具</h1>
        <p>这个页面提供了用于诊断前端WebUI数据显示不一致问题的调试工具。</p>

        <div class="tabs">
            <div class="tab active" onclick="showTab('instructions')">使用说明</div>
            <div class="tab" onclick="showTab('scripts')">调试脚本</div>
            <div class="tab" onclick="showTab('live')">实时调试</div>
            <div class="tab" onclick="showTab('analysis')">问题分析</div>
        </div>

        <div id="instructions" class="tab-content active">
            <h2>📋 使用说明</h2>
            
            <div class="step">
                <h3>步骤 1: 访问GraphMemory页面</h3>
                <p>首先在新标签页中打开GraphMemory页面：</p>
                <a href="http://localhost:3000/graph-memory" target="_blank" class="button">打开 GraphMemory 页面</a>
            </div>

            <div class="step">
                <h3>步骤 2: 打开Chrome开发者工具</h3>
                <p>在GraphMemory页面中：</p>
                <ul>
                    <li>按 <code>F12</code> 或右键选择"检查"</li>
                    <li>切换到 <code>Console</code> 标签</li>
                    <li>确保Console没有过滤器（显示所有日志级别）</li>
                </ul>
            </div>

            <div class="step">
                <h3>步骤 3: 运行调试脚本</h3>
                <p>复制以下脚本到Console中运行：</p>
                <button class="button" onclick="copyScript('basic')">复制基础调试脚本</button>
                <button class="button secondary" onclick="copyScript('network')">复制网络调试脚本</button>
            </div>

            <div class="step warning">
                <h3>⚠️ 注意事项</h3>
                <ul>
                    <li>确保后端服务运行在 http://localhost:8000</li>
                    <li>确保前端服务运行在 http://localhost:3000</li>
                    <li>调试过程中保持Console打开以观察日志</li>
                    <li>如果页面卡顿，可以刷新页面重新开始</li>
                </ul>
            </div>
        </div>

        <div id="scripts" class="tab-content">
            <h2>🛠️ 调试脚本</h2>
            
            <div class="tool-section">
                <h3>基础调试脚本</h3>
                <p>检查Redux状态、DOM元素、API请求和控制台错误：</p>
                <div class="code-block" id="basicScript">
                    <div>正在加载脚本...</div>
                </div>
                <button class="button" onclick="copyScript('basic')">复制到剪贴板</button>
            </div>

            <div class="tool-section">
                <h3>网络调试脚本</h3>
                <p>详细分析API响应和数据转换过程：</p>
                <div class="code-block" id="networkScript">
                    <div>正在加载脚本...</div>
                </div>
                <button class="button" onclick="copyScript('network')">复制到剪贴板</button>
            </div>

            <div class="tool-section">
                <h3>快速检查命令</h3>
                <p>可以在Console中直接运行的快速检查命令：</p>
                <div class="code-block">
// 检查当前页面数据显示
console.log('当前显示:', {
    entities: document.body.textContent.match(/(\d+)\s+entities/)?.[1] || '0',
    relations: document.body.textContent.match(/(\d+)\s+relations/)?.[1] || '0',
    hasErrors: document.querySelectorAll('[class*="error"]').length > 0,
    isLoading: document.querySelectorAll('[class*="loading"]').length > 0
});

// 手动触发API请求
fetch('http://localhost:8000/v1/graph/memories?user_id=test_bob&limit=10')
    .then(res => res.json())
    .then(data => console.log('API数据:', data));

// 检查React错误边界
console.log('错误元素:', document.querySelectorAll('[class*="error"], .text-red'));
                </div>
                <button class="button secondary" onclick="copyQuickCommands()">复制快速命令</button>
            </div>
        </div>

        <div id="live" class="tab-content">
            <h2>🔴 实时调试</h2>
            <p>在下方iframe中实时查看GraphMemory页面，同时可以看到调试输出：</p>
            
            <div class="step">
                <button class="button" onclick="refreshIframe()">刷新页面</button>
                <button class="button secondary" onclick="openInNewTab()">在新标签页打开</button>
            </div>

            <iframe id="graphMemoryFrame" src="http://localhost:3000/graph-memory"></iframe>
            
            <div class="step success">
                <h3>✅ 实时监控</h3>
                <p>页面已加载在上方iframe中。打开Chrome开发者工具后，可以在iframe中运行调试脚本。</p>
            </div>
        </div>

        <div id="analysis" class="tab-content">
            <h2>🔍 问题分析</h2>
            
            <div class="tool-section">
                <h3>常见问题和解决方案</h3>
                
                <h4>1. 数据不显示或显示为0</h4>
                <ul>
                    <li><strong>检查API连接</strong>: 确认后端服务正常运行</li>
                    <li><strong>检查用户数据</strong>: 确认test_bob用户有图数据</li>
                    <li><strong>检查网络请求</strong>: 在Network标签查看API请求状态</li>
                </ul>

                <h4>2. 数据字段映射错误</h4>
                <ul>
                    <li><strong>实体字段</strong>: 检查是否正确映射label → name</li>
                    <li><strong>关系字段</strong>: 检查relationships → relations转换</li>
                    <li><strong>统计字段</strong>: 检查total_relationships → total_relations</li>
                </ul>

                <h4>3. React组件渲染问题</h4>
                <ul>
                    <li><strong>检查控制台错误</strong>: 查看是否有JavaScript错误</li>
                    <li><strong>检查Redux状态</strong>: 使用Redux DevTools查看状态</li>
                    <li><strong>检查组件生命周期</strong>: 确认useEffect正常触发</li>
                </ul>
            </div>

            <div class="tool-section">
                <h3>已知修复</h3>
                <div class="success step">
                    <h4>✅ 已修复的问题</h4>
                    <ul>
                        <li>后端返回relationships，前端期望relations - 已在客户端转换</li>
                        <li>实体label/name字段不匹配 - 已优先使用label</li>
                        <li>关系source_entity/source_entity_id映射 - 已实现兼容</li>
                        <li>TypeScript类型定义不匹配 - 已添加可选字段</li>
                    </ul>
                </div>
            </div>

            <div class="tool-section">
                <h3>数据流验证</h3>
                <p>完整的数据流验证已通过：</p>
                <ol>
                    <li>后端API返回正确格式数据 ✅</li>
                    <li>客户端转换字段名称 ✅</li>
                    <li>数据转换器处理字段映射 ✅</li>
                    <li>React组件接收转换后数据 ✅</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        let basicScriptContent = '';
        let networkScriptContent = '';

        // 加载脚本内容
        fetch('/chrome_debug_script.js')
            .then(response => response.text())
            .then(content => {
                basicScriptContent = content;
                document.getElementById('basicScript').textContent = content;
            })
            .catch(() => {
                document.getElementById('basicScript').innerHTML = '<div style="color: #ff6b6b;">无法加载基础调试脚本</div>';
            });

        fetch('/chrome_network_debug.js')
            .then(response => response.text())
            .then(content => {
                networkScriptContent = content;
                document.getElementById('networkScript').textContent = content;
            })
            .catch(() => {
                document.getElementById('networkScript').innerHTML = '<div style="color: #ff6b6b;">无法加载网络调试脚本</div>';
            });

        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function copyScript(type) {
            let content = '';
            if (type === 'basic') {
                content = basicScriptContent;
            } else if (type === 'network') {
                content = networkScriptContent;
            }

            if (content) {
                navigator.clipboard.writeText(content).then(() => {
                    alert('脚本已复制到剪贴板！请在Chrome Console中粘贴运行。');
                }).catch(() => {
                    // 备用复制方法
                    const textArea = document.createElement('textarea');
                    textArea.value = content;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('脚本已复制到剪贴板！请在Chrome Console中粘贴运行。');
                });
            } else {
                alert('脚本还未加载完成，请稍后再试。');
            }
        }

        function copyQuickCommands() {
            const commands = `// 检查当前页面数据显示
console.log('当前显示:', {
    entities: document.body.textContent.match(/(\\d+)\\s+entities/)?.[1] || '0',
    relations: document.body.textContent.match(/(\\d+)\\s+relations/)?.[1] || '0',
    hasErrors: document.querySelectorAll('[class*="error"]').length > 0,
    isLoading: document.querySelectorAll('[class*="loading"]').length > 0
});

// 手动触发API请求
fetch('http://localhost:8000/v1/graph/memories?user_id=test_bob&limit=10')
    .then(res => res.json())
    .then(data => console.log('API数据:', data));

// 检查React错误边界
console.log('错误元素:', document.querySelectorAll('[class*="error"], .text-red'));`;

            navigator.clipboard.writeText(commands).then(() => {
                alert('快速命令已复制到剪贴板！');
            });
        }

        function refreshIframe() {
            document.getElementById('graphMemoryFrame').src = 'http://localhost:3000/graph-memory';
        }

        function openInNewTab() {
            window.open('http://localhost:3000/graph-memory', '_blank');
        }
    </script>
</body>
</html>