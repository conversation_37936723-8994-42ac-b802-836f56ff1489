#!/usr/bin/env python3
"""
Live test for Categories automatic classification functionality
"""
import requests
import json
import time

API_BASE = "http://localhost:8000"

def test_add_memory_with_categories():
    """Test adding memory and check if categories are automatically generated"""
    print("=== 测试Categories自动分类功能 ===")
    
    # Test case 1: Personal information
    test_messages = [
        {
            "message": "<PERSON>, my name is <PERSON>. I am a software engineer at Google.",
            "expected_categories": ["personal_details", "professional_details"]
        },
        {
            "message": "I love playing football on weekends and traveling to new countries.",
            "expected_categories": ["sports", "travel", "hobbies"]
        },
        {
            "message": "I have a wife named <PERSON> and two kids, <PERSON> and <PERSON>.",
            "expected_categories": ["family"]
        }
    ]
    
    for i, test_case in enumerate(test_messages, 1):
        print(f"\n--- 测试用例 {i} ---")
        print(f"输入: {test_case['message']}")
        
        # Add memory
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": test_case["message"]}],
                "user_id": f"test_user_{i}"
            },
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"API响应: {result}")
            
            # Handle different response formats
            if isinstance(result, list):
                memories = result
            elif isinstance(result, dict) and 'results' in result:
                memories = result['results']
            elif isinstance(result, dict) and 'id' in result:
                memories = [result]
            else:
                memories = []
            
            print(f"✓ 记忆创建成功: {len(memories)} 条记忆")
            
            # Check if memories have categories
            for memory in memories:
                memory_id = memory['id']
                memory_text = memory['memory']
                
                # Get detailed memory info
                detail_response = requests.get(f"{API_BASE}/v1/memories/{memory_id}")
                if detail_response.status_code == 200:
                    memory_detail = detail_response.json()
                    categories = memory_detail.get('categories', [])
                    
                    print(f"  记忆: {memory_text}")
                    print(f"  Categories: {categories}")
                    
                    if categories:
                        print(f"  ✓ 自动生成了Categories: {categories}")
                        # Check if expected categories are present
                        expected = test_case.get('expected_categories', [])
                        found_expected = [cat for cat in expected if cat in categories]
                        if found_expected:
                            print(f"  ✓ 发现预期Categories: {found_expected}")
                        else:
                            print(f"  ⚠ 未发现预期Categories: {expected}")
                    else:
                        print(f"  ✗ 未生成Categories")
                else:
                    print(f"  ✗ 获取记忆详情失败: {detail_response.status_code}")
        else:
            print(f"✗ 记忆创建失败: {response.status_code}")
            print(f"错误: {response.text}")
        
        time.sleep(1)  # Brief pause between tests

def test_custom_categories_priority():
    """Test that custom_categories take priority over auto-generated ones"""
    print(f"\n=== 测试custom_categories优先级 ===")
    
    response = requests.post(
        f"{API_BASE}/v1/memories/",
        json={
            "messages": [{"role": "user", "content": "I work as a data scientist"}],
            "user_id": "priority_test_user",
            "custom_categories": ["work", "career"]
        },
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['results']:
            memory_id = result['results'][0]['id']
            
            # Get detailed memory info
            detail_response = requests.get(f"{API_BASE}/v1/memories/{memory_id}")
            if detail_response.status_code == 200:
                memory_detail = detail_response.json()
                categories = memory_detail.get('categories', [])
                
                print(f"输入: I work as a data scientist")
                print(f"Custom categories: ['work', 'career']")
                print(f"实际categories: {categories}")
                
                if 'work' in categories and 'career' in categories:
                    print("✓ custom_categories优先级正确")
                else:
                    print("✗ custom_categories优先级可能有问题")
            else:
                print("✗ 获取记忆详情失败")
    else:
        print(f"✗ 记忆创建失败: {response.status_code}")

def main():
    """Run all tests"""
    print("Categories自动分类功能Live测试")
    print("="*50)
    
    # Test basic functionality
    test_add_memory_with_categories()
    
    # Test priority system
    test_custom_categories_priority()
    
    print(f"\n=== 测试完成 ===")

if __name__ == "__main__":
    main()