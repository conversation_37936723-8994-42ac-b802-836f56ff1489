#!/bin/bash
set -e

# Mem0 增强版 Entrypoint - 智能权限处理
echo "🚀 Mem0 服务启动中..."

# 数据目录路径
DATA_DIR="/app/data"

# 日志函数
log_info() {
    echo "[$(date '+%H:%M:%S')] [INFO] $1"
}

log_warn() {
    echo "[$(date '+%H:%M:%S')] [WARN] $1"
}

# 智能权限修复函数
smart_fix_permissions() {
    log_info "智能权限修复开始..."
    
    # 确保数据目录存在
    mkdir -p "$DATA_DIR"/{.mem0,vector_store}
    
    # 获取当前运行用户ID
    CURRENT_UID=$(id -u)
    CURRENT_GID=$(id -g)
    
    log_info "当前运行用户: $CURRENT_UID:$CURRENT_GID"
    
    # 递归修复所有权限
    if [[ $CURRENT_UID -eq 0 ]]; then
        log_info "以root身份运行，设置完全权限..."
        
        # 修复所有数据目录权限
        chown -R root:root "$DATA_DIR" 2>/dev/null || true
        chmod -R 755 "$DATA_DIR" 2>/dev/null || true
        
        # 特别处理SQLite数据库文件
        find "$DATA_DIR" -name "*.db" -exec chmod 666 {} \; 2>/dev/null || true
        find "$DATA_DIR" -name "*.db" -exec chown root:root {} \; 2>/dev/null || true
        
    else
        log_info "以非root用户运行，设置用户权限..."
        
        # 尝试修复权限（可能失败但不影响启动）
        chown -R "$CURRENT_UID:$CURRENT_GID" "$DATA_DIR" 2>/dev/null || log_warn "无法修改文件所有权（预期行为）"
        chmod -R 755 "$DATA_DIR" 2>/dev/null || log_warn "无法修改文件权限（预期行为）"
    fi
    
    # 创建SQLite数据库并设置权限
    SQLITE_DB="$DATA_DIR/history.db"
    if [[ ! -f "$SQLITE_DB" ]]; then
        log_info "创建SQLite数据库文件..."
        touch "$SQLITE_DB"
        chmod 666 "$SQLITE_DB" 2>/dev/null || true
        chown "$CURRENT_UID:$CURRENT_GID" "$SQLITE_DB" 2>/dev/null || true
    else
        log_info "SQLite数据库已存在，检查权限..."
        chmod 666 "$SQLITE_DB" 2>/dev/null || true
        chown "$CURRENT_UID:$CURRENT_GID" "$SQLITE_DB" 2>/dev/null || true
    fi
    
    log_info "✓ 权限修复完成"
}

# 健康检查函数
pre_flight_check() {
    log_info "执行启动前检查..."
    
    # 检查必要目录
    for dir in "$DATA_DIR" "$DATA_DIR/.mem0" "$DATA_DIR/vector_store"; do
        if [[ ! -d "$dir" ]]; then
            log_warn "目录 $dir 不存在，正在创建..."
            mkdir -p "$dir" || log_warn "无法创建目录 $dir"
        fi
    done
    
    # 检查SQLite数据库权限
    SQLITE_DB="$DATA_DIR/history.db"
    if [[ -f "$SQLITE_DB" ]]; then
        if [[ -w "$SQLITE_DB" ]]; then
            log_info "✓ SQLite数据库可写"
        else
            log_warn "SQLite数据库不可写，尝试修复..."
            chmod 666 "$SQLITE_DB" 2>/dev/null || log_warn "无法修改SQLite权限"
        fi
    fi
    
    # 检查磁盘空间
    local available_space=$(df "$DATA_DIR" | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 1048576 ]]; then  # 1GB = 1048576 KB
        log_warn "可用磁盘空间不足 1GB，可能影响服务运行"
    fi
    
    log_info "✓ 启动前检查完成"
}

# 等待依赖服务
wait_for_dependencies() {
    log_info "等待依赖服务就绪..."
    
    # 等待 Qdrant
    local qdrant_host="${QDRANT_HOST:-qdrant}"
    local qdrant_port="${QDRANT_PORT:-6333}"
    
    for i in {1..30}; do
        if nc -z "$qdrant_host" "$qdrant_port" 2>/dev/null; then
            log_info "✓ Qdrant 连接成功"
            break
        elif [[ $i -eq 30 ]]; then
            log_warn "Qdrant 连接超时，服务将继续启动"
        else
            echo -n "."
            sleep 2
        fi
    done
    
    # 等待 Neo4j
    local neo4j_host="${NEO4J_HOST:-neo4j}"
    local neo4j_port="${NEO4J_BOLT_PORT:-7687}"
    
    for i in {1..30}; do
        if nc -z "$neo4j_host" "$neo4j_port" 2>/dev/null; then
            log_info "✓ Neo4j 连接成功"
            break
        elif [[ $i -eq 30 ]]; then
            log_warn "Neo4j 连接超时，服务将继续启动"
        else
            echo -n "."
            sleep 2
        fi
    done
}

# 主要功能
main() {
    # 智能权限处理
    smart_fix_permissions
    
    # 启动前检查
    pre_flight_check
    
    # 等待依赖服务（可选，可通过环境变量控制）
    if [[ "${WAIT_FOR_DEPENDENCIES:-true}" == "true" ]]; then
        wait_for_dependencies
    fi
    
    CURRENT_UID=$(id -u)
    if [[ $CURRENT_UID -eq 0 ]]; then
        log_info "以root用户直接启动应用..."
        log_info "执行命令: $*"
        exec "$@"
    else
        log_info "切换到当前用户并启动应用..."
        log_info "执行命令: $*"
        exec "$@"
    fi
}

# 执行主函数
main "$@"