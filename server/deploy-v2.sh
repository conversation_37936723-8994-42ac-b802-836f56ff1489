#!/bin/bash
set -e

# Mem0 自动化部署脚本 v2.0 - 完全自动化权限管理
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
PROJECT_ROOT=$(dirname "$SCRIPT_DIR")
DATA_DIR="$PROJECT_ROOT/data"

# 颜色输出
RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[1;33m'; BLUE='\033[0;34m'; NC='\033[0m'
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 🔧 权限自动处理
auto_handle_permissions() {
    local force_permissions=${1:-false}
    log_step "🔒 自动权限管理..."
    
    mkdir -p "$DATA_DIR"/{mem0,qdrant,neo4j/{data,logs,import,plugins}} 2>/dev/null || true
    
    local marker_file="$DATA_DIR/.permissions_initialized"
    if [[ -f "$marker_file" && "$force_permissions" != "true" ]]; then
        log_info "✅ 权限已初始化"
        return 0
    fi
    
    log_info "设置服务权限..."
    chown -R 1000:1000 "$DATA_DIR/mem0" 2>/dev/null || true
    chown -R 1000:1000 "$DATA_DIR/qdrant" 2>/dev/null || true
    chown -R 7474:7474 "$DATA_DIR/neo4j" 2>/dev/null || true
    chmod -R 755 "$DATA_DIR" 2>/dev/null || true
    
    echo "$(date): Mem0 权限自动初始化完成" > "$marker_file" 2>/dev/null || true
    log_info "✅ 权限配置完成"
}

# 🚀 启动服务
start_services() {
    local skip_permissions=${1:-false}
    local force_permissions=${2:-false}
    
    log_step "🚀 启动 Mem0 服务栈..."
    
    if [[ "$skip_permissions" != "true" ]]; then
        auto_handle_permissions "$force_permissions"
    fi
    
    cd "$SCRIPT_DIR"
    
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 未运行，请启动 Docker 服务"
        return 1
    fi
    
    log_info "启动容器..."
    if docker compose up -d --build; then
        log_info "✅ 服务启动成功"
        log_info "等待服务就绪..."
        
        for i in {1..12}; do
            sleep 5
            if check_health_quiet; then
                log_info "🎉 所有服务运行正常！"
                show_service_info
                return 0
            fi
            echo -n "."
        done
        
        log_warn "服务启动完成，但部分服务可能仍在初始化中"
        check_health
    else
        log_error "❌ 服务启动失败"
        return 1
    fi
}

# 健康检查
check_health_quiet() {
    local healthy_count=0
    local services=("8000" "6333" "7474")
    
    for port in "${services[@]}"; do
        if curl -f -s "http://localhost:$port/health" >/dev/null 2>&1 || \
           curl -f -s "http://localhost:$port" >/dev/null 2>&1; then
            ((healthy_count++))
        fi
    done
    
    [[ $healthy_count -eq 3 ]]
}

check_health() {
    log_step "🏥 检查服务健康状态..."
    local services=("mem0-api:8000:/health" "mem0-qdrant:6333:" "mem0-neo4j:7474:")
    local all_healthy=true
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port path <<< "$service"
        if curl -f -s "http://localhost:$port$path" >/dev/null 2>&1; then
            log_info "✅ $name 健康"
        else
            log_warn "❌ $name 不健康 (端口 $port)"
            all_healthy=false
        fi
    done
    
    if [[ "$all_healthy" == "true" ]]; then
        show_service_info
    fi
}

show_service_info() {
    echo -e "\n🌐 服务访问地址："
    echo "  • Mem0 API:      http://localhost:8000"
    echo "  • API 文档:      http://localhost:8000/docs"
    echo "  • Qdrant:        http://localhost:6333"
    echo "  • Neo4j 浏览器:  http://localhost:7474 (neo4j/mem0graph)"
}

# 🛑 停止服务
stop_services() {
    log_step "🛑 停止 Mem0 服务..."
    cd "$SCRIPT_DIR"
    docker compose down && log_info "✅ 服务停止成功"
}

# 📊 显示状态
show_status() {
    log_step "📊 服务状态概览..."
    cd "$SCRIPT_DIR"
    docker compose ps
}

# 📝 显示日志
show_logs() {
    cd "$SCRIPT_DIR"
    if [[ -n "$1" ]]; then
        docker compose logs -f --tail=100 "$1"
    else
        docker compose logs -f --tail=50
    fi
}

show_help() {
    cat << EOF
🚀 Mem0 自动化部署脚本 v2.0

特性：
  ✅ 全自动权限管理    ✅ 智能依赖检查
  ✅ 健康状态监控      ✅ 错误自动恢复

用法: $0 [command] [options]

命令:
  start          🚀 启动所有服务（自动权限处理）
  stop           🛑 停止所有服务
  restart        🔄 重启所有服务
  status         📊 显示服务状态
  logs [service] 📝 显示服务日志
  health         🏥 检查服务健康状态

选项:
  --force-permissions  强制重新初始化权限
  --no-permissions     跳过权限检查

一键启动: sudo $0 start
EOF
}

# 主函数
main() {
    local command=${1:-start}
    local force_permissions=false
    local skip_permissions=false
    
    # 解析选项
    shift || true
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force-permissions) force_permissions=true; shift ;;
            --no-permissions) skip_permissions=true; shift ;;
            --help) show_help; exit 0 ;;
            *) break ;;
        esac
    done
    
    # 权限检查
    if [[ $EUID -ne 0 && "$skip_permissions" != "true" ]]; then
        log_error "需要 sudo 权限来管理文件权限"
        echo "使用: sudo $0 $command"
        echo "或者: $0 $command --no-permissions (不推荐)"
        exit 1
    fi
    
    # 执行命令
    case $command in
        start) start_services "$skip_permissions" "$force_permissions" ;;
        stop) stop_services ;;
        restart) stop_services; start_services "$skip_permissions" "$force_permissions" ;;
        status) show_status ;;
        logs) show_logs "$1" ;;
        health) check_health ;;
        help|--help) show_help ;;
        *)
            log_error "未知命令: $command"
            echo "运行 '$0 --help' 查看帮助"
            exit 1
            ;;
    esac
}

main "$@"