#!/usr/bin/env python3
"""
Mem0 API端点综合测试脚本
测试所有可用的API端点和功能
"""

import requests
import json
import time
import uuid
import base64
from io import BytesIO
from PIL import Image

BASE_URL = "http://localhost:8000"

class Mem0APITester:
    def __init__(self):
        self.test_user = f"api_test_{str(uuid.uuid4())[:8]}"
        self.test_agent = f"agent_{str(uuid.uuid4())[:8]}"
        self.test_run = f"run_{str(uuid.uuid4())[:8]}"
        self.created_memory_ids = []
        self.results = {}
        
    def log_result(self, endpoint, status, details=""):
        """记录测试结果"""
        self.results[endpoint] = {"status": status, "details": details}
        status_icon = "✅" if status == "SUCCESS" else "❌" if status == "FAILED" else "⚠️"
        print(f"{status_icon} {endpoint}: {status}")
        if details:
            print(f"    {details}")
    
    def test_health_endpoint(self):
        """测试健康检查端点"""
        print("\n=== 1. 健康检查端点 ===")
        try:
            response = requests.get(f"{BASE_URL}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                self.log_result("GET /health", "SUCCESS", f"Status: {health_data.get('status', 'unknown')}")
            else:
                self.log_result("GET /health", "FAILED", f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("GET /health", "FAILED", str(e))
    
    def test_memory_creation(self):
        """测试记忆创建端点"""
        print("\n=== 2. 记忆创建端点 ===")
        
        # 测试基本记忆创建
        try:
            response = requests.post(
                f"{BASE_URL}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": "I am a software engineer at Google"}],
                    "user_id": self.test_user
                },
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                if result:
                    self.created_memory_ids.extend([mem.get('id') for mem in result if mem.get('id')])
                self.log_result("POST /v1/memories/", "SUCCESS", f"Created {len(result)} memories")
            else:
                self.log_result("POST /v1/memories/", "FAILED", f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("POST /v1/memories/", "FAILED", str(e))
        
        # 测试带Categories的记忆创建
        try:
            response = requests.post(
                f"{BASE_URL}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": "I love playing tennis on weekends"}],
                    "user_id": self.test_user,
                    "custom_categories": [{"hobby": "leisure activities and sports"}]
                },
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                if result:
                    self.created_memory_ids.extend([mem.get('id') for mem in result if mem.get('id')])
                self.log_result("POST /v1/memories/ (with categories)", "SUCCESS", f"Created {len(result)} memories")
            else:
                self.log_result("POST /v1/memories/ (with categories)", "FAILED", f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("POST /v1/memories/ (with categories)", "FAILED", str(e))
        
        # 测试V2上下文添加
        try:
            response = requests.post(
                f"{BASE_URL}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": "I got promoted to Senior Software Engineer"}],
                    "user_id": self.test_user,
                    "version": "v2"
                },
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                self.log_result("POST /v1/memories/ (v2 contextual)", "SUCCESS", f"Processed {len(result)} memory operations")
            else:
                self.log_result("POST /v1/memories/ (v2 contextual)", "FAILED", f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("POST /v1/memories/ (v2 contextual)", "FAILED", str(e))
        
        time.sleep(3)  # 等待记忆处理完成
    
    def test_memory_search(self):
        """测试记忆搜索端点"""
        print("\n=== 3. 记忆搜索端点 ===")
        
        # 测试基本搜索
        try:
            response = requests.post(
                f"{BASE_URL}/v1/memories/search/",
                json={
                    "query": "software engineer",
                    "user_id": self.test_user,
                    "limit": 10
                },
                timeout=15
            )
            
            if response.status_code == 200:
                results = response.json()
                self.log_result("POST /v1/memories/search/", "SUCCESS", f"Found {len(results)} results")
            else:
                self.log_result("POST /v1/memories/search/", "FAILED", f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("POST /v1/memories/search/", "FAILED", str(e))
        
        # 测试带阈值的搜索
        try:
            response = requests.post(
                f"{BASE_URL}/v1/memories/search/",
                json={
                    "query": "tennis",
                    "user_id": self.test_user,
                    "threshold": 0.3,
                    "limit": 5
                },
                timeout=15
            )
            
            if response.status_code == 200:
                results = response.json()
                self.log_result("POST /v1/memories/search/ (with threshold)", "SUCCESS", f"Found {len(results)} results")
            else:
                self.log_result("POST /v1/memories/search/ (with threshold)", "FAILED", f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("POST /v1/memories/search/ (with threshold)", "FAILED", str(e))
        
        # 测试高级检索参数
        try:
            response = requests.post(
                f"{BASE_URL}/v1/memories/search/",
                json={
                    "query": "work job",
                    "user_id": self.test_user,
                    "keyword_search": True,
                    "rerank": True,
                    "limit": 5
                },
                timeout=15
            )
            
            if response.status_code == 200:
                results = response.json()
                self.log_result("POST /v1/memories/search/ (advanced)", "SUCCESS", f"Found {len(results)} results")
            else:
                self.log_result("POST /v1/memories/search/ (advanced)", "FAILED", f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("POST /v1/memories/search/ (advanced)", "FAILED", str(e))
    
    def test_memory_retrieval(self):
        """测试记忆获取端点"""
        print("\n=== 4. 记忆获取端点 ===")
        
        # 测试获取所有记忆
        try:
            response = requests.get(
                f"{BASE_URL}/v1/memories/",
                params={"user_id": self.test_user, "limit": 20},
                timeout=15
            )
            
            if response.status_code == 200:
                memories = response.json()
                if isinstance(memories, dict) and "results" in memories:
                    memories = memories["results"]
                self.log_result("GET /v1/memories/", "SUCCESS", f"Retrieved {len(memories)} memories")
            else:
                self.log_result("GET /v1/memories/", "FAILED", f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("GET /v1/memories/", "FAILED", str(e))
        
        # 测试获取特定记忆
        if self.created_memory_ids:
            try:
                memory_id = self.created_memory_ids[0]
                response = requests.get(f"{BASE_URL}/v1/memories/{memory_id}/", timeout=15)
                
                if response.status_code == 200:
                    memory = response.json()
                    self.log_result("GET /v1/memories/{id}/", "SUCCESS", f"Retrieved memory: {memory.get('memory', '')[:50]}...")
                else:
                    self.log_result("GET /v1/memories/{id}/", "FAILED", f"Status: {response.status_code}")
            except Exception as e:
                self.log_result("GET /v1/memories/{id}/", "FAILED", str(e))
        else:
            self.log_result("GET /v1/memories/{id}/", "SKIPPED", "No memory IDs available")
    
    def test_memory_update(self):
        """测试记忆更新端点"""
        print("\n=== 5. 记忆更新端点 ===")
        
        if self.created_memory_ids:
            try:
                memory_id = self.created_memory_ids[0]
                response = requests.put(
                    f"{BASE_URL}/v1/memories/{memory_id}/",
                    json={
                        "text": "I am a Senior Software Engineer at Google with 5 years of experience",
                        "metadata": {"category": "professional", "updated": True}
                    },
                    timeout=15
                )
                
                if response.status_code == 200:
                    result = response.json()
                    self.log_result("PUT /v1/memories/{id}/", "SUCCESS", "Memory updated successfully")
                else:
                    self.log_result("PUT /v1/memories/{id}/", "FAILED", f"Status: {response.status_code}")
            except Exception as e:
                self.log_result("PUT /v1/memories/{id}/", "FAILED", str(e))
        else:
            self.log_result("PUT /v1/memories/{id}/", "SKIPPED", "No memory IDs available")
    
    def test_memory_history(self):
        """测试记忆历史端点"""
        print("\n=== 6. 记忆历史端点 ===")
        
        if self.created_memory_ids:
            try:
                memory_id = self.created_memory_ids[0]
                response = requests.get(f"{BASE_URL}/v1/memories/{memory_id}/history/", timeout=15)
                
                if response.status_code == 200:
                    history = response.json()
                    self.log_result("GET /v1/memories/{id}/history/", "SUCCESS", f"Retrieved {len(history)} history entries")
                else:
                    self.log_result("GET /v1/memories/{id}/history/", "FAILED", f"Status: {response.status_code}")
            except Exception as e:
                self.log_result("GET /v1/memories/{id}/history/", "FAILED", str(e))
        else:
            self.log_result("GET /v1/memories/{id}/history/", "SKIPPED", "No memory IDs available")
    
    def test_v2_search_endpoint(self):
        """测试V2搜索端点"""
        print("\n=== 7. V2搜索端点 ===")
        
        try:
            response = requests.post(
                f"{BASE_URL}/v2/memories/search/",
                json={
                    "query": "engineering work",
                    "user_id": self.test_user,
                    "limit": 5,
                    "filters": {"category": "professional"}
                },
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                results = result.get("results", [])
                self.log_result("POST /v2/memories/search/", "SUCCESS", f"Found {len(results)} results")
            else:
                self.log_result("POST /v2/memories/search/", "FAILED", f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("POST /v2/memories/search/", "FAILED", str(e))
    
    def test_graph_endpoints(self):
        """测试图记忆端点"""
        print("\n=== 8. 图记忆端点 ===")
        
        # 测试图搜索
        try:
            response = requests.post(
                f"{BASE_URL}/v1/graph/search",
                json={
                    "query": "software engineer",
                    "user_id": self.test_user,
                    "search_type": "semantic",
                    "limit": 10
                },
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                entities = result.get("entities", [])
                self.log_result("POST /v1/graph/search", "SUCCESS", f"Found {len(entities)} entities")
            else:
                self.log_result("POST /v1/graph/search", "FAILED", f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("POST /v1/graph/search", "FAILED", str(e))
    
    def test_feedback_endpoint(self):
        """测试反馈端点"""
        print("\n=== 9. 反馈端点 ===")
        
        if self.created_memory_ids:
            try:
                memory_id = self.created_memory_ids[0]
                response = requests.post(
                    f"{BASE_URL}/v1/feedback/",
                    json={
                        "memory_id": memory_id,
                        "feedback": "POSITIVE",
                        "feedback_reason": "This memory is accurate and helpful"
                    },
                    timeout=15
                )
                
                if response.status_code == 200:
                    result = response.json()
                    self.log_result("POST /v1/feedback/", "SUCCESS", "Feedback submitted successfully")
                else:
                    self.log_result("POST /v1/feedback/", "FAILED", f"Status: {response.status_code}")
            except Exception as e:
                self.log_result("POST /v1/feedback/", "FAILED", str(e))
        else:
            self.log_result("POST /v1/feedback/", "SKIPPED", "No memory IDs available")
    
    def test_memory_deletion(self):
        """测试记忆删除端点（最后执行）"""
        print("\n=== 10. 记忆删除端点 ===")
        
        if self.created_memory_ids:
            try:
                memory_id = self.created_memory_ids[-1]  # 删除最后一个记忆
                response = requests.delete(f"{BASE_URL}/v1/memories/{memory_id}/", timeout=15)
                
                if response.status_code == 200:
                    self.log_result("DELETE /v1/memories/{id}/", "SUCCESS", "Memory deleted successfully")
                else:
                    self.log_result("DELETE /v1/memories/{id}/", "FAILED", f"Status: {response.status_code}")
            except Exception as e:
                self.log_result("DELETE /v1/memories/{id}/", "FAILED", str(e))
        else:
            self.log_result("DELETE /v1/memories/{id}/", "SKIPPED", "No memory IDs available")
    
    def test_multimodal_endpoint(self):
        """测试多模态端点"""
        print("\n=== 11. 多模态端点 ===")
        
        try:
            # 创建简单的测试图像
            img = Image.new('RGB', (100, 100), color='blue')
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            img_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            response = requests.post(
                f"{BASE_URL}/v1/memories/",
                json={
                    "messages": [
                        {
                            "role": "user", 
                            "content": "Here is a blue square image",
                            "images": [f"data:image/png;base64,{img_base64}"]
                        }
                    ],
                    "user_id": self.test_user
                },
                timeout=20
            )
            
            if response.status_code == 200:
                result = response.json()
                self.log_result("POST /v1/memories/ (multimodal)", "SUCCESS", f"Created {len(result)} multimodal memories")
            else:
                self.log_result("POST /v1/memories/ (multimodal)", "FAILED", f"Status: {response.status_code}")
        except Exception as e:
            self.log_result("POST /v1/memories/ (multimodal)", "FAILED", str(e))
    
    def print_summary(self):
        """打印测试结果总结"""
        print("\n" + "=" * 60)
        print("🎯 API端点测试总结")
        print("=" * 60)
        
        success_count = sum(1 for result in self.results.values() if result["status"] == "SUCCESS")
        failed_count = sum(1 for result in self.results.values() if result["status"] == "FAILED") 
        skipped_count = sum(1 for result in self.results.values() if result["status"] == "SKIPPED")
        total_count = len(self.results)
        
        print(f"总端点数: {total_count}")
        print(f"✅ 成功: {success_count}")
        print(f"❌ 失败: {failed_count}")
        print(f"⚠️ 跳过: {skipped_count}")
        print(f"成功率: {(success_count / (total_count - skipped_count) * 100):.1f}%" if total_count > skipped_count else "N/A")
        
        print(f"\n测试用户ID: {self.test_user}")
        print(f"创建的记忆数: {len(self.created_memory_ids)}")
        
        if failed_count > 0:
            print(f"\n❌ 失败的端点:")
            for endpoint, result in self.results.items():
                if result["status"] == "FAILED":
                    print(f"  - {endpoint}: {result['details']}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始Mem0 API端点综合测试")
        print("=" * 60)
        print(f"测试用户: {self.test_user}")
        
        # 按顺序执行所有测试
        self.test_health_endpoint()
        self.test_memory_creation()
        self.test_memory_search()
        self.test_memory_retrieval()
        self.test_memory_update()
        self.test_memory_history()
        self.test_v2_search_endpoint()
        self.test_graph_endpoints()
        self.test_feedback_endpoint()
        self.test_multimodal_endpoint()
        self.test_memory_deletion()  # 最后执行删除
        
        self.print_summary()

def main():
    tester = Mem0APITester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()