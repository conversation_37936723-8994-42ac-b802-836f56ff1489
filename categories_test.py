#!/usr/bin/env python3
"""
带Categories的测试记忆生成器
使用正确的custom_categories格式
"""
import requests
import json
import time

API_BASE = "http://localhost:8000"

# 预定义Categories
CATEGORIES = {
    "个人信息": "个人基本信息、联系方式、身份信息等",
    "技能专长": "专业技能、技术能力、工作经验等", 
    "兴趣爱好": "个人兴趣、娱乐活动、爱好等",
    "工作项目": "工作相关的项目、任务、计划等",
    "团队信息": "团队成员、组织结构、协作关系等",
    "学习笔记": "学习内容、知识点、学习心得等",
    "生活记录": "日常生活、购物、用餐等记录",
    "娱乐活动": "电影、音乐、游戏等娱乐内容",
    "人际关系": "朋友、同事、家人等人际关系信息",
    "技术文档": "技术相关的文档、最佳实践等",
    "项目管理": "项目计划、进度、管理相关内容",
    "健康信息": "健康状况、体检、医疗相关信息"
}

# 测试记忆数据 - 使用正确的custom_categories格式
memories = [
    # 个人信息类
    {
        "user_id": "alice", 
        "messages": [{"role": "user", "content": "我叫Alice，是一名前端开发工程师，住在上海浦东新区"}],
        "custom_categories": [{"个人信息": CATEGORIES["个人信息"]}],
        "metadata": {"priority": "high"}
    },
    {
        "user_id": "alice", 
        "messages": [{"role": "user", "content": "我擅长React、Vue.js、TypeScript和Node.js，有5年前端开发经验"}],
        "custom_categories": [{"技能专长": CATEGORIES["技能专长"]}],
        "metadata": {"experience_years": 5}
    },
    {
        "user_id": "alice", 
        "messages": [{"role": "user", "content": "我喜欢看科幻电影、听古典音乐、周末去咖啡厅，最近在学习摄影"}],
        "custom_categories": [{"兴趣爱好": CATEGORIES["兴趣爱好"]}],
        "metadata": {"learning": "摄影"}
    },
    
    # 工作项目类
    {
        "user_id": "bob", 
        "agent_id": "work_assistant",
        "messages": [{"role": "user", "content": "正在开发一个电商前端项目，使用Next.js、Tailwind CSS和Redux Toolkit"}],
        "custom_categories": [{"工作项目": CATEGORIES["工作项目"]}],
        "metadata": {"project_type": "电商", "tech_stack": ["Next.js", "Tailwind CSS", "Redux"]}
    },
    {
        "user_id": "bob", 
        "agent_id": "work_assistant",
        "messages": [{"role": "user", "content": "团队有6个人：我和小李做前端，小王和小赵做后端，小刘做UI设计，小张做测试"}],
        "custom_categories": [{"团队信息": CATEGORIES["团队信息"]}],
        "enable_graph": True,
        "metadata": {"team_size": 6, "roles": ["前端", "后端", "UI", "测试"]}
    },
    {
        "user_id": "bob", 
        "agent_id": "work_assistant",
        "messages": [{"role": "user", "content": "项目预计3月15日上线，目前完成度70%，还需要完成支付模块和用户中心"}],
        "custom_categories": [{"工作项目": CATEGORIES["工作项目"]}],
        "metadata": {"deadline": "2024-03-15", "progress": 70, "remaining": ["支付模块", "用户中心"]}
    },
    
    # 学习记录类
    {
        "user_id": "charlie", 
        "agent_id": "study_assistant",
        "messages": [{"role": "user", "content": "学习了Python装饰器：@property可以将方法转为属性访问，@staticmethod定义静态方法"}],
        "custom_categories": [{"学习笔记": CATEGORIES["学习笔记"]}],
        "metadata": {"topic": "Python装饰器", "difficulty": "中级"}
    },
    {
        "user_id": "charlie", 
        "agent_id": "study_assistant",
        "messages": [{"role": "user", "content": "Docker容器化学习：镜像是只读模板，容器是镜像的运行实例，可以启动停止删除"}],
        "custom_categories": [{"学习笔记": CATEGORIES["学习笔记"]}],
        "metadata": {"topic": "Docker", "difficulty": "初级"}
    },
    {
        "user_id": "charlie", 
        "agent_id": "study_assistant",
        "messages": [{"role": "user", "content": "微服务架构设计原则：单一职责、独立部署、技术栈无关、通过API通信"}],
        "custom_categories": [{"技术文档": CATEGORIES["技术文档"]}],
        "metadata": {"topic": "微服务", "difficulty": "高级"}
    },
    
    # 生活记录类
    {
        "user_id": "diana", 
        "messages": [{"role": "user", "content": "今天去了新开的日料店，环境很棒，三文鱼刺身很新鲜，人均消费280元"}],
        "custom_categories": [{"生活记录": CATEGORIES["生活记录"]}],
        "metadata": {"restaurant": "日料", "cost": 280, "rating": "很好"}
    },
    {
        "user_id": "diana", 
        "messages": [{"role": "user", "content": "看了《沙丘2》IMAX版本，视觉效果震撼，音效出色，故事情节紧凑，评分9/10"}],
        "custom_categories": [{"娱乐活动": CATEGORIES["娱乐活动"]}],
        "metadata": {"movie": "沙丘2", "format": "IMAX", "rating": 9}
    },
    {
        "user_id": "diana", 
        "messages": [{"role": "user", "content": "这周末计划：周六上午健身房锻炼，下午购物买春装，周日和朋友聚餐看展览"}],
        "custom_categories": [{"生活记录": CATEGORIES["生活记录"]}],
        "metadata": {"type": "周末计划", "activities": ["健身", "购物", "聚餐", "展览"]}
    },
    
    # 关系网络类 - 启用图形记忆
    {
        "user_id": "eve", 
        "enable_graph": True,
        "messages": [{"role": "user", "content": "我的同事Tom是后端工程师，擅长Java和Spring框架，我们经常一起讨论架构设计"}],
        "custom_categories": [{"人际关系": CATEGORIES["人际关系"]}],
        "metadata": {"relation": "同事", "name": "Tom", "skills": ["Java", "Spring"]}
    },
    {
        "user_id": "eve", 
        "enable_graph": True,
        "messages": [{"role": "user", "content": "朋友Lisa在腾讯做产品经理，负责微信小程序业务，月薪3万，我们大学室友"}],
        "custom_categories": [{"人际关系": CATEGORIES["人际关系"]}],
        "metadata": {"relation": "朋友", "name": "Lisa", "company": "腾讯", "position": "产品经理"}
    },
    {
        "user_id": "eve", 
        "enable_graph": True,
        "messages": [{"role": "user", "content": "老板Zhang总技术背景出身，创业12年，公司现在120人规模，对技术要求很高"}],
        "custom_categories": [{"人际关系": CATEGORIES["人际关系"]}],
        "metadata": {"relation": "老板", "name": "Zhang", "company_size": 120, "experience": 12}
    },
    
    # 健康信息类
    {
        "user_id": "frank", 
        "messages": [{"role": "user", "content": "体检报告显示：血压130/85偏高，体重75kg，BMI 24.5，建议多运动控制饮食"}],
        "custom_categories": [{"健康信息": CATEGORIES["健康信息"]}],
        "metadata": {"blood_pressure": "130/85", "weight": 75, "bmi": 24.5, "advice": "运动+饮食"}
    },
    
    # 复杂对话类 - 多分类
    {
        "user_id": "grace", 
        "agent_id": "consultant",
        "messages": [
            {"role": "user", "content": "我想学习人工智能，应该从哪里开始？需要什么数学基础？"},
            {"role": "assistant", "content": "建议先学Python基础，然后学习线性代数、概率统计、微积分，再学机器学习算法"},
            {"role": "user", "content": "有什么好的在线课程推荐吗？"},
            {"role": "assistant", "content": "推荐Andrew Ng的机器学习课程，李宏毅的深度学习课程，还有fast.ai的实践课程"}
        ],
        "custom_categories": [
            {"学习笔记": CATEGORIES["学习笔记"]},
            {"技能专长": CATEGORIES["技能专长"]}
        ],
        "metadata": {"topic": "AI学习规划", "courses": ["Andrew Ng", "李宏毅", "fast.ai"]}
    },
    
    # 项目管理类
    {
        "user_id": "henry", 
        "agent_id": "pm_assistant",
        "messages": [{"role": "user", "content": "Sprint 3计划：本周完成用户登录注册模块，下周开始支付功能开发，预计需要10个工作日"}],
        "custom_categories": [{"项目管理": CATEGORIES["项目管理"]}],
        "metadata": {"sprint": 3, "current_week": "登录注册", "next_week": "支付", "duration": 10}
    },
    {
        "user_id": "henry", 
        "agent_id": "pm_assistant",
        "messages": [{"role": "user", "content": "Bug跟踪统计：P0级严重bug 1个（登录失败），P1级重要bug 3个，P2级一般bug 8个"}],
        "custom_categories": [{"项目管理": CATEGORIES["项目管理"]}],
        "metadata": {"bugs": {"P0": 1, "P1": 3, "P2": 8}, "critical_issue": "登录失败"}
    }
]

def add_memory(data):
    """添加单条记忆"""
    try:
        response = requests.post(f"{API_BASE}/v1/memories/", json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            return True, "成功", result
        else:
            return False, f"状态码: {response.status_code}, 响应: {response.text[:100]}", None
    except Exception as e:
        return False, f"异常: {str(e)[:100]}", None

print("🧠 开始添加带Categories的测试记忆数据...")
print(f"📊 计划添加 {len(memories)} 条记忆")
print(f"🏷️ 可用分类: {len(CATEGORIES)} 个")
print("-" * 60)

success_count = 0
for i, memory in enumerate(memories, 1):
    content_preview = memory["messages"][0]["content"][:40] + "..."
    categories = [list(cat.keys())[0] for cat in memory.get("custom_categories", [])]
    category_str = ", ".join(categories) if categories else "无分类"
    
    print(f"[{i:2d}/{len(memories)}] {content_preview:<45} 分类: {category_str}")
    
    success, message, result = add_memory(memory)
    if success:
        print(f"{'':>50} ✅ 成功")
        success_count += 1
    else:
        print(f"{'':>50} ❌ {message}")
    
    time.sleep(0.3)  # 避免请求过快

print("-" * 60)
print(f"🎉 完成！成功添加 {success_count}/{len(memories)} 条记忆")
print(f"📈 成功率: {success_count/len(memories)*100:.1f}%")

# 统计信息
category_stats = {}
users = set()
agents = set()
for memory in memories:
    for cat_dict in memory.get("custom_categories", []):
        for cat_name in cat_dict.keys():
            category_stats[cat_name] = category_stats.get(cat_name, 0) + 1
    users.add(memory["user_id"])
    if memory.get("agent_id"):
        agents.add(memory["agent_id"])

print(f"\n📋 数据统计:")
print(f"  👥 用户数: {len(users)}")
print(f"  🤖 代理数: {len(agents)}")
print(f"  🏷️ 分类数: {len(category_stats)}")
print(f"  🔗 图形记忆: {sum(1 for m in memories if m.get('enable_graph', False))} 条")

print(f"\n📊 分类分布:")
for category, count in sorted(category_stats.items()):
    print(f"  • {category}: {count} 条")

print(f"\n🎯 带Categories的测试数据已准备就绪！")
print(f"📱 现在可以在前端UI中查看这些分类记忆了")
print(f"🔍 API查询示例: /v1/memories/?user_id=alice")