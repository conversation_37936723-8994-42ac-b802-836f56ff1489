#!/usr/bin/env python3
"""
快速测试修复后的阈值过滤功能
"""

import requests
import json
import time
import uuid

BASE_URL = "http://localhost:8000"

def test_threshold_fix():
    """测试修复后的阈值过滤功能"""
    print("🔧 测试修复后的阈值过滤功能")
    print("=" * 50)
    
    test_user = f"threshold_test_{str(uuid.uuid4())[:8]}"
    
    # 创建一些测试记忆
    print("创建测试记忆...")
    test_memories = [
        "I love programming in Python",
        "I enjoy hiking in the mountains", 
        "My favorite food is pizza",
        "I work as a software engineer",
        "I have a pet dog named <PERSON>"
    ]
    
    for memory_text in test_memories:
        try:
            response = requests.post(
                f"{BASE_URL}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": memory_text}],
                    "user_id": test_user
                },
                timeout=10
            )
            time.sleep(0.5)
        except Exception as e:
            print(f"创建记忆失败: {e}")
    
    print("等待记忆处理完成...")
    time.sleep(3)
    
    # 测试不同阈值
    query = "programming"
    thresholds = [0.1, 0.3, 0.5, 0.7, 0.9]
    
    print(f"\n搜索查询: '{query}'\n")
    
    for threshold in thresholds:
        try:
            response = requests.post(
                f"{BASE_URL}/v1/memories/search/",
                json={
                    "query": query,
                    "user_id": test_user,
                    "threshold": threshold,
                    "limit": 10
                },
                timeout=15
            )
            
            if response.status_code == 200:
                results = response.json()
                
                # 检查结果是否满足阈值要求
                above_threshold = [r for r in results if r.get('score', 0) >= threshold]
                below_threshold = [r for r in results if r.get('score', 0) < threshold]
                
                print(f"阈值 {threshold:.1f}:")
                print(f"  总结果: {len(results)} 条")
                print(f"  >= 阈值: {len(above_threshold)} 条")
                print(f"  < 阈值: {len(below_threshold)} 条")
                
                if len(below_threshold) == 0:
                    print(f"  ✅ 阈值 {threshold:.1f} 过滤正确")
                else:
                    print(f"  ❌ 阈值 {threshold:.1f} 过滤失败")
                    # 显示不符合阈值的结果
                    for result in below_threshold[:3]:
                        print(f"    - 分数 {result.get('score', 0):.3f}: {result.get('memory', '')[:50]}...")
                
                # 显示最高分数的记忆
                if results:
                    best_result = max(results, key=lambda x: x.get('score', 0))
                    print(f"  最高分数: {best_result.get('score', 0):.3f}")
                
                print()
                
            else:
                print(f"阈值 {threshold:.1f} 测试失败: {response.status_code}")
                
        except Exception as e:
            print(f"阈值 {threshold:.1f} 测试异常: {e}")
    
    print("=" * 50)
    print("🎯 阈值过滤修复测试完成!")

if __name__ == "__main__":
    test_threshold_fix()