/**
 * 错误处理工具函数
 * 
 * 提供通用的错误处理工具和辅助函数：
 * - 错误类型检查和转换
 * - 错误消息格式化
 * - 重试机制
 * - 错误恢复策略
 */

import { ErrorCategory, ErrorSeverity } from '@/lib/errorReporting';

export interface ParsedError {
  message: string;
  code?: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  isRetryable: boolean;
  suggestedAction?: string;
}

/**
 * 解析和分类错误
 */
export function parseError(error: unknown): ParsedError {
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    
    // 网络错误
    if (isNetworkError(error)) {
      return {
        message: error.message,
        category: 'network',
        severity: 'medium',
        isRetryable: true,
        suggestedAction: '检查网络连接后重试'
      };
    }
    
    // API错误
    if (isApiError(error)) {
      const severity = getApiErrorSeverity(error);
      return {
        message: error.message,
        category: 'api',
        severity,
        isRetryable: severity !== 'critical',
        suggestedAction: severity === 'critical' ? '联系技术支持' : '稍后重试'
      };
    }
    
    // 验证错误
    if (isValidationError(error)) {
      return {
        message: error.message,
        category: 'validation',
        severity: 'low',
        isRetryable: false,
        suggestedAction: '检查输入数据格式'
      };
    }
    
    // 权限错误
    if (isAuthError(error)) {
      return {
        message: error.message,
        category: 'auth',
        severity: 'high',
        isRetryable: false,
        suggestedAction: '重新登录或联系管理员'
      };
    }
    
    // 默认应用错误
    return {
      message: error.message,
      category: 'application',
      severity: 'medium',
      isRetryable: true,
      suggestedAction: '刷新页面或稍后重试'
    };
  }
  
  // 非Error对象
  return {
    message: String(error),
    category: 'unknown',
    severity: 'medium',
    isRetryable: true,
    suggestedAction: '刷新页面或稍后重试'
  };
}

/**
 * 检查是否为网络错误
 */
export function isNetworkError(error: Error): boolean {
  const message = error.message.toLowerCase();
  return message.includes('network') ||
         message.includes('fetch') ||
         message.includes('connection') ||
         message.includes('timeout') ||
         message.includes('offline') ||
         error.name === 'NetworkError' ||
         error.name === 'TypeError' && message.includes('failed to fetch');
}

/**
 * 检查是否为API错误
 */
export function isApiError(error: Error): boolean {
  const message = error.message.toLowerCase();
  return message.includes('api') ||
         message.includes('server') ||
         message.includes('400') ||
         message.includes('401') ||
         message.includes('403') ||
         message.includes('404') ||
         message.includes('500') ||
         message.includes('502') ||
         message.includes('503') ||
         message.includes('504');
}

/**
 * 检查是否为验证错误
 */
export function isValidationError(error: Error): boolean {
  const message = error.message.toLowerCase();
  return message.includes('validation') ||
         message.includes('invalid') ||
         message.includes('required') ||
         message.includes('format') ||
         message.includes('schema') ||
         message.includes('constraint');
}

/**
 * 检查是否为权限错误
 */
export function isAuthError(error: Error): boolean {
  const message = error.message.toLowerCase();
  return message.includes('unauthorized') ||
         message.includes('forbidden') ||
         message.includes('401') ||
         message.includes('403') ||
         message.includes('authentication') ||
         message.includes('permission');
}

/**
 * 获取API错误的严重性
 */
export function getApiErrorSeverity(error: Error): ErrorSeverity {
  const message = error.message.toLowerCase();
  
  if (message.includes('500') || message.includes('502') || 
      message.includes('503') || message.includes('504')) {
    return 'critical';
  }
  
  if (message.includes('401') || message.includes('403')) {
    return 'high';
  }
  
  if (message.includes('400') || message.includes('404')) {
    return 'medium';
  }
  
  return 'medium';
}

/**
 * 格式化错误消息为用户友好的文本
 */
export function formatErrorMessage(error: unknown): string {
  const parsed = parseError(error);
  
  const baseMessages: Record<ErrorCategory, string> = {
    network: '网络连接出现问题',
    api: '服务器响应异常',
    validation: '输入数据格式不正确',
    auth: '权限验证失败',
    application: '应用程序出现错误',
    unknown: '发生未知错误'
  };
  
  const baseMessage = baseMessages[parsed.category];
  const suggestion = parsed.suggestedAction ? `，${parsed.suggestedAction}` : '';
  
  return `${baseMessage}${suggestion}`;
}

/**
 * 重试机制配置
 */
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryCondition?: (error: unknown) => boolean;
}

/**
 * 默认重试配置
 */
export const defaultRetryConfig: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryCondition: (error) => parseError(error).isRetryable
};

/**
 * 带重试机制的异步操作执行器
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {}
): Promise<T> {
  const finalConfig = { ...defaultRetryConfig, ...config };
  let lastError: unknown;
  
  for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // 检查是否应该重试
      if (attempt === finalConfig.maxAttempts || 
          (finalConfig.retryCondition && !finalConfig.retryCondition(error))) {
        throw error;
      }
      
      // 计算延迟时间（指数退避）
      const delay = Math.min(
        finalConfig.baseDelay * Math.pow(finalConfig.backoffFactor, attempt - 1),
        finalConfig.maxDelay
      );
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * 安全的异步操作包装器
 */
export async function safeAsync<T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<T | undefined> {
  try {
    return await operation();
  } catch (error) {
    console.warn('Safe async operation failed:', error);
    return fallback;
  }
}

/**
 * 错误边界辅助函数
 */
export function createErrorBoundaryInfo(error: Error, errorInfo: any) {
  return {
    message: error.message,
    stack: error.stack,
    componentStack: errorInfo.componentStack,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href
  };
}

/**
 * 检查错误是否需要立即报告
 */
export function shouldReportImmediately(error: unknown): boolean {
  const parsed = parseError(error);
  return parsed.severity === 'critical' || parsed.severity === 'high';
}

/**
 * 生成错误摘要
 */
export function generateErrorSummary(errors: ParsedError[]): string {
  if (errors.length === 0) return '无错误';
  
  const categoryCounts = errors.reduce((acc, error) => {
    acc[error.category] = (acc[error.category] || 0) + 1;
    return acc;
  }, {} as Record<ErrorCategory, number>);
  
  const summaryParts = Object.entries(categoryCounts)
    .map(([category, count]) => `${category}: ${count}`)
    .join(', ');
  
  return `总计 ${errors.length} 个错误 (${summaryParts})`;
}
