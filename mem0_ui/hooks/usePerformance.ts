/**
 * 性能监控和优化Hook
 * 
 * 提供组件性能监控和优化功能：
 * - 渲染时间监控
 * - 内存使用监控
 * - 性能优化建议
 * - 自动性能调优
 */

import { useEffect, useRef, useCallback, useState } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  componentMounts: number;
  componentUpdates: number;
  lastRenderTime: number;
}

interface PerformanceConfig {
  enableMonitoring: boolean;
  sampleRate: number;
  maxSamples: number;
  warningThresholds: {
    renderTime: number;
    memoryUsage: number;
  };
}

const defaultConfig: PerformanceConfig = {
  enableMonitoring: false, // 临时禁用所有性能监控
  sampleRate: 0.0, // 设置为0禁用采样
  maxSamples: 100,
  warningThresholds: {
    renderTime: 16, // 60fps = 16.67ms per frame
    memoryUsage: 50 * 1024 * 1024 // 50MB
  }
};

/**
 * 性能监控Hook - 临时禁用版本
 */
export function usePerformanceMonitor(
  componentName: string,
  config: Partial<PerformanceConfig> = {}
) {
  // 完全禁用性能监控，返回静态值和空函数
  const staticMetrics = {
    renderTime: 0,
    memoryUsage: 0,
    componentMounts: 0,
    componentUpdates: 0,
    lastRenderTime: Date.now()
  };

  const noOp = () => {};
  const staticGetStats = () => null;

  return {
    metrics: staticMetrics,
    startRender: noOp,
    endRender: noOp,
    getStats: staticGetStats,
    updateComponentUpdateCount: noOp
  };
}

/**
 * 渲染性能Hook
 */
export function useRenderPerformance(componentName: string) {
  // 完全禁用性能监控，返回静态值避免无限循环
  const staticMetrics = {
    renderTime: 0,
    memoryUsage: 0,
    componentUpdates: 0,
    lastUpdate: Date.now()
  };

  const staticGetStats = () => null;

  return { metrics: staticMetrics, getStats: staticGetStats };
}

/**
 * 内存监控Hook
 */
export function useMemoryMonitor(interval: number = 5000) {
  const [memoryInfo, setMemoryInfo] = useState<{
    used: number;
    total: number;
    limit: number;
  } | null>(null);

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ((performance as any).memory) {
        const memory = (performance as any).memory;
        setMemoryInfo({
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        });
      }
    };

    updateMemoryInfo();
    const timer = setInterval(updateMemoryInfo, interval);

    return () => clearInterval(timer);
  }, [interval]);

  return memoryInfo;
}

/**
 * 性能优化建议Hook
 */
export function usePerformanceOptimization(metrics: PerformanceMetrics) {
  const suggestions = useCallback(() => {
    const suggestions: string[] = [];

    if (metrics.renderTime > 16) {
      suggestions.push('Consider using React.memo() to prevent unnecessary re-renders');
      suggestions.push('Use useMemo() and useCallback() for expensive calculations');
    }

    if (metrics.renderTime > 50) {
      suggestions.push('Consider code splitting with React.lazy()');
      suggestions.push('Implement virtualization for large lists');
    }

    if (metrics.componentUpdates > metrics.componentMounts * 10) {
      suggestions.push('Too many re-renders detected - check dependencies in useEffect');
    }

    if (metrics.memoryUsage > 100 * 1024 * 1024) {
      suggestions.push('High memory usage - check for memory leaks');
      suggestions.push('Consider implementing data pagination');
    }

    return suggestions;
  }, [metrics]);

  return suggestions();
}

/**
 * 自动性能调优Hook
 */
export function useAutoPerformanceTuning(componentName: string) {
  const [optimizationLevel, setOptimizationLevel] = useState<'low' | 'medium' | 'high'>('medium');
  const { metrics } = usePerformanceMonitor(componentName);

  useEffect(() => {
    // 根据性能指标自动调整优化级别
    if (metrics.renderTime > 50 || metrics.memoryUsage > 100 * 1024 * 1024) {
      setOptimizationLevel('high');
    } else if (metrics.renderTime > 16 || metrics.memoryUsage > 50 * 1024 * 1024) {
      setOptimizationLevel('medium');
    } else {
      setOptimizationLevel('low');
    }
  }, [metrics]);

  const getOptimizationConfig = useCallback(() => {
    switch (optimizationLevel) {
      case 'high':
        return {
          enableVirtualization: true,
          maxItemsPerPage: 20,
          enableLazyLoading: true,
          debounceDelay: 500
        };
      case 'medium':
        return {
          enableVirtualization: false,
          maxItemsPerPage: 50,
          enableLazyLoading: true,
          debounceDelay: 300
        };
      case 'low':
      default:
        return {
          enableVirtualization: false,
          maxItemsPerPage: 100,
          enableLazyLoading: false,
          debounceDelay: 100
        };
    }
  }, [optimizationLevel]);

  return {
    optimizationLevel,
    config: getOptimizationConfig(),
    metrics
  };
}

/**
 * 性能分析器Hook
 */
export function usePerformanceProfiler(enabled: boolean = false) {
  const profileRef = useRef<{
    startTime: number;
    marks: Array<{ name: string; time: number }>;
  }>({ startTime: 0, marks: [] });

  const startProfiling = useCallback((name: string = 'profile') => {
    if (!enabled) return;
    
    profileRef.current.startTime = performance.now();
    profileRef.current.marks = [];
    performance.mark(`${name}-start`);
  }, [enabled]);

  const mark = useCallback((name: string) => {
    if (!enabled) return;
    
    const time = performance.now();
    profileRef.current.marks.push({ name, time });
    performance.mark(name);
  }, [enabled]);

  const endProfiling = useCallback((name: string = 'profile') => {
    if (!enabled) return;
    
    const endTime = performance.now();
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    
    const totalTime = endTime - profileRef.current.startTime;
    const marks = profileRef.current.marks.map(mark => ({
      ...mark,
      relativeTime: mark.time - profileRef.current.startTime
    }));

    return {
      totalTime,
      marks,
      startTime: profileRef.current.startTime,
      endTime
    };
  }, [enabled]);

  return {
    startProfiling,
    mark,
    endProfiling
  };
}
