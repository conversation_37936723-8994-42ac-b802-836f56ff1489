import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '@/store/store';
import { setApps, setTotalApps } from '@/store/profileSlice';
import { setTotalMemories } from '@/store/profileSlice';
import { mem0Client } from '@/lib/mem0-client';
import { UIStatsResponse, Mem0ApiError } from '@/types/mem0-api';

interface UseStatsReturn {
  fetchStats: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
  stats: UIStatsResponse | null;
}

export const useStats = (): UseStatsReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<UIStatsResponse | null>(null);
  const dispatch = useDispatch<AppDispatch>();
  const user_id = useSelector((state: RootState) => state.profile.userId);

  const fetchStats = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // 使用新的UI统计API端点以确保一致性
      const statsData = await mem0Client.getStats(user_id);
      setStats(statsData);

      // 更新Redux store以保持兼容性
      dispatch(setTotalMemories(statsData.total_memories));
      dispatch(setTotalApps(0)); // UI统计API暂不包含top_apps

      // 由于新API结构不同，暂时设置空数组
      dispatch(setApps([]));

      setIsLoading(false);
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to fetch stats';
      setError(errorMessage);
      setIsLoading(false);
      throw new Error(errorMessage);
    }
  };

  return { fetchStats, isLoading, error, stats };
};