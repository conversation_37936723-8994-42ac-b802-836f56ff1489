/**
 * 缓存管理Hook
 * 
 * 提供智能缓存功能：
 * - 内存缓存
 * - 本地存储缓存
 * - TTL支持
 * - LRU淘汰策略
 * - 缓存预热
 */

import { useState, useEffect, useCallback, useRef } from 'react';

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

interface CacheConfig {
  maxSize: number;
  defaultTTL: number;
  enablePersistence: boolean;
  storageKey: string;
}

const defaultConfig: CacheConfig = {
  maxSize: 100,
  defaultTTL: 5 * 60 * 1000, // 5分钟
  enablePersistence: true,
  storageKey: 'mem0_cache'
};

/**
 * 内存缓存Hook
 */
export function useMemoryCache<T>(config: Partial<CacheConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config };
  const cacheRef = useRef<Map<string, CacheItem<T>>>(new Map());
  const [cacheStats, setCacheStats] = useState({
    hits: 0,
    misses: 0,
    size: 0
  });

  // 清理过期缓存
  const cleanupExpired = useCallback(() => {
    const now = Date.now();
    const cache = cacheRef.current;
    const keysToDelete: string[] = [];

    cache.forEach((item, key) => {
      if (now - item.timestamp > item.ttl) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => cache.delete(key));
    
    setCacheStats(prev => ({
      ...prev,
      size: cache.size
    }));
  }, []);

  // LRU淘汰策略
  const evictLRU = useCallback(() => {
    const cache = cacheRef.current;
    if (cache.size <= finalConfig.maxSize) return;

    // 找到最少使用的项目
    let lruKey = '';
    let lruTime = Date.now();

    cache.forEach((item, key) => {
      if (item.lastAccessed < lruTime) {
        lruTime = item.lastAccessed;
        lruKey = key;
      }
    });

    if (lruKey) {
      cache.delete(lruKey);
    }
  }, [finalConfig.maxSize]);

  // 设置缓存
  const set = useCallback((key: string, data: T, ttl?: number) => {
    const cache = cacheRef.current;
    const now = Date.now();
    
    const item: CacheItem<T> = {
      data,
      timestamp: now,
      ttl: ttl || finalConfig.defaultTTL,
      accessCount: 0,
      lastAccessed: now
    };

    cache.set(key, item);
    evictLRU();
    
    setCacheStats(prev => ({
      ...prev,
      size: cache.size
    }));
  }, [finalConfig.defaultTTL, evictLRU]);

  // 获取缓存
  const get = useCallback((key: string): T | null => {
    const cache = cacheRef.current;
    const item = cache.get(key);
    const now = Date.now();

    if (!item) {
      setCacheStats(prev => ({
        ...prev,
        misses: prev.misses + 1
      }));
      return null;
    }

    // 检查是否过期
    if (now - item.timestamp > item.ttl) {
      cache.delete(key);
      setCacheStats(prev => ({
        ...prev,
        misses: prev.misses + 1,
        size: cache.size
      }));
      return null;
    }

    // 更新访问信息
    item.accessCount++;
    item.lastAccessed = now;

    setCacheStats(prev => ({
      ...prev,
      hits: prev.hits + 1
    }));

    return item.data;
  }, []);

  // 删除缓存
  const remove = useCallback((key: string) => {
    const cache = cacheRef.current;
    const deleted = cache.delete(key);
    
    if (deleted) {
      setCacheStats(prev => ({
        ...prev,
        size: cache.size
      }));
    }
    
    return deleted;
  }, []);

  // 清空缓存
  const clear = useCallback(() => {
    cacheRef.current.clear();
    setCacheStats({
      hits: 0,
      misses: 0,
      size: 0
    });
  }, []);

  // 检查是否存在
  const has = useCallback((key: string): boolean => {
    const item = cacheRef.current.get(key);
    if (!item) return false;
    
    const now = Date.now();
    if (now - item.timestamp > item.ttl) {
      cacheRef.current.delete(key);
      return false;
    }
    
    return true;
  }, []);

  // 定期清理
  useEffect(() => {
    const interval = setInterval(cleanupExpired, 60000); // 每分钟清理一次
    return () => clearInterval(interval);
  }, [cleanupExpired]);

  return {
    set,
    get,
    remove,
    clear,
    has,
    stats: cacheStats
  };
}

/**
 * 持久化缓存Hook
 */
export function usePersistentCache<T>(config: Partial<CacheConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config };
  const memoryCache = useMemoryCache<T>(config);

  // 从localStorage加载缓存
  const loadFromStorage = useCallback(() => {
    if (!finalConfig.enablePersistence || typeof window === 'undefined') {
      return;
    }

    try {
      const stored = localStorage.getItem(finalConfig.storageKey);
      if (stored) {
        const data = JSON.parse(stored);
        Object.entries(data).forEach(([key, item]: [string, any]) => {
          memoryCache.set(key, item.data, item.ttl);
        });
      }
    } catch (error) {
      console.warn('Failed to load cache from storage:', error);
    }
  }, [finalConfig.enablePersistence, finalConfig.storageKey, memoryCache]);

  // 保存到localStorage
  const saveToStorage = useCallback(() => {
    if (!finalConfig.enablePersistence || typeof window === 'undefined') {
      return;
    }

    try {
      const cacheData: Record<string, any> = {};
      // 这里需要访问内部的cache，但由于封装问题，我们简化处理
      localStorage.setItem(finalConfig.storageKey, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to save cache to storage:', error);
    }
  }, [finalConfig.enablePersistence, finalConfig.storageKey]);

  // 初始化时加载缓存
  useEffect(() => {
    loadFromStorage();
  }, [loadFromStorage]);

  // 定期保存缓存
  useEffect(() => {
    if (!finalConfig.enablePersistence) return;
    
    const interval = setInterval(saveToStorage, 30000); // 每30秒保存一次
    return () => clearInterval(interval);
  }, [finalConfig.enablePersistence, saveToStorage]);

  return memoryCache;
}

/**
 * API缓存Hook
 */
export function useApiCache<T>(
  apiFunction: (...args: any[]) => Promise<T>,
  config: Partial<CacheConfig> = {}
) {
  const cache = usePersistentCache<T>(config);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const cachedApiCall = useCallback(async (...args: any[]): Promise<T> => {
    const cacheKey = JSON.stringify(args);
    
    // 尝试从缓存获取
    const cached = cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    // 缓存未命中，调用API
    setLoading(true);
    setError(null);
    
    try {
      const result = await apiFunction(...args);
      cache.set(cacheKey, result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'API call failed';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiFunction, cache]);

  const invalidate = useCallback((...args: any[]) => {
    const cacheKey = JSON.stringify(args);
    cache.remove(cacheKey);
  }, [cache]);

  const invalidateAll = useCallback(() => {
    cache.clear();
  }, [cache]);

  return {
    call: cachedApiCall,
    invalidate,
    invalidateAll,
    loading,
    error,
    stats: cache.stats
  };
}

/**
 * 查询缓存Hook
 */
export function useQueryCache<T>(
  queryKey: string,
  queryFn: () => Promise<T>,
  options: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
    refetchOnWindowFocus?: boolean;
  } = {}
) {
  const {
    enabled = true,
    staleTime = 0,
    cacheTime = 5 * 60 * 1000,
    refetchOnWindowFocus = false
  } = options;

  const cache = usePersistentCache<T>({ defaultTTL: cacheTime });
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const lastFetchRef = useRef<number>(0);

  const fetchData = useCallback(async (force = false) => {
    if (!enabled) return;

    const now = Date.now();
    const cached = cache.get(queryKey);
    
    // 检查是否需要重新获取
    if (!force && cached && (now - lastFetchRef.current) < staleTime) {
      setData(cached);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await queryFn();
      cache.set(queryKey, result);
      setData(result);
      lastFetchRef.current = now;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Query failed';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [enabled, queryKey, queryFn, cache, staleTime]);

  // 初始化获取数据
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 窗口焦点重新获取
  useEffect(() => {
    if (!refetchOnWindowFocus) return;

    const handleFocus = () => fetchData();
    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, fetchData]);

  const refetch = useCallback(() => fetchData(true), [fetchData]);
  const invalidate = useCallback(() => {
    cache.remove(queryKey);
    setData(null);
  }, [cache, queryKey]);

  return {
    data,
    loading,
    error,
    refetch,
    invalidate
  };
}
