'use client';

import { useState, useEffect } from 'react';

import { responsive } from '@/lib/utils';

/**
 * 检测是否为移动设备的自定义Hook
 * @param breakpoint 断点像素值，默认768px
 * @returns boolean 是否为移动设备
 */
export function useIsMobile(breakpoint: number = 768): boolean {
  const [isMobile, setIsMobile] = useState<boolean>(false);

  useEffect(() => {
    // 初始检测
    const checkIsMobile = () => {
      if (typeof window !== 'undefined') {
        setIsMobile(window.innerWidth < breakpoint);
      }
    };

    // 初始化
    checkIsMobile();

    // 监听窗口大小变化
    const handleResize = () => {
      checkIsMobile();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [breakpoint]);

  return isMobile;
}

/**
 * 获取当前设备断点的Hook
 * @returns 'mobile' | 'tablet' | 'desktop' | 'wide'
 */
export function useBreakpoint() {
  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop' | 'wide'>('desktop');

  useEffect(() => {
    const updateBreakpoint = () => {
      setBreakpoint(responsive.getCurrentBreakpoint());
    };

    // 初始化
    updateBreakpoint();

    // 监听窗口大小变化
    window.addEventListener('resize', updateBreakpoint);

    return () => {
      window.removeEventListener('resize', updateBreakpoint);
    };
  }, []);

  return breakpoint;
}

/**
 * 检测是否为触摸设备的Hook
 * @returns boolean 是否为触摸设备
 */
export function useIsTouchDevice(): boolean {
  const [isTouchDevice, setIsTouchDevice] = useState<boolean>(false);

  useEffect(() => {
    setIsTouchDevice(responsive.isTouchDevice());
  }, []);

  return isTouchDevice;
}

/**
 * 获取设备性能等级的Hook
 * @returns 'low' | 'medium' | 'high'
 */
export function useDevicePerformance() {
  const [performanceLevel, setPerformanceLevel] = useState<'low' | 'medium' | 'high'>('medium');

  useEffect(() => {
    setPerformanceLevel((performance as any).getDevicePerformanceLevel?.() || 'medium');
  }, []);

  return performanceLevel;
}

/**
 * 检测用户偏好设置的Hook
 * @returns 用户偏好设置对象
 */
export function useUserPreferences() {
  const [preferences, setPreferences] = useState({
    prefersReducedMotion: false,
    prefersHighContrast: false,
    prefersDarkMode: true
  });

  useEffect(() => {
    const updatePreferences = () => {
      setPreferences({
        prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        prefersHighContrast: window.matchMedia('(prefers-contrast: high)').matches,
        prefersDarkMode: window.matchMedia('(prefers-color-scheme: dark)').matches
      });
    };

    // 初始化
    updatePreferences();

    // 监听偏好设置变化
    const mediaQueries = [
      window.matchMedia('(prefers-reduced-motion: reduce)'),
      window.matchMedia('(prefers-contrast: high)'),
      window.matchMedia('(prefers-color-scheme: dark)')
    ];

    mediaQueries.forEach(mq => {
      mq.addEventListener('change', updatePreferences);
    });

    return () => {
      mediaQueries.forEach(mq => {
        mq.removeEventListener('change', updatePreferences);
      });
    };
  }, []);

  return preferences;
}
