import { useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { mem0Client } from '@/lib/mem0-client';
import { Mem0ApiError } from '@/types/mem0-api';
import { RootState } from '@/store/store';
import {
  InstructionTemplate,
  InstructionCategory,
  addInstructionTemplate,
  updateInstructionTemplate,
  deleteInstructionTemplate,
  addInstructionCategory,
  updateInstructionCategory,
  deleteInstructionCategory,
} from '@/store/configSlice';

/**
 * 配置Hook - 包含Mem0核心功能和指令管理
 */
interface UseConfigApiReturn {
  testConnection: () => Promise<boolean>;
  isLoading: boolean;
  error: string | null;
  // 指令模板管理
  instructionTemplates: InstructionTemplate[];
  instructionCategories: InstructionCategory[];
  createInstructionTemplate: (template: Omit<InstructionTemplate, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateInstructionTemplateById: (id: string, updates: Partial<InstructionTemplate>) => void;
  deleteInstructionTemplateById: (id: string) => void;
  // 指令分类管理
  createInstructionCategory: (category: Omit<InstructionCategory, 'id'>) => void;
  updateInstructionCategoryById: (id: string, updates: Partial<InstructionCategory>) => void;
  deleteInstructionCategoryById: (id: string) => void;
  // 指令预览和测试
  previewInstruction: (template: string, variables?: Record<string, string>) => string;
}

export const useConfig = (): UseConfigApiReturn => {
  const dispatch = useDispatch();
  const configState = useSelector((state: RootState) => state.config);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const instructionTemplates = configState.openmemory.instruction_templates || [];
  const instructionCategories = configState.openmemory.instruction_categories || [];

  const testConnection = async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const healthStatus = await mem0Client.healthCheck();
      setIsLoading(false);
      return healthStatus.status === 'ok' || healthStatus.status === 'healthy';
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to connect to Mem0 server';
      setError(errorMessage);
      setIsLoading(false);
      return false;
    }
  };

  // 指令模板管理方法
  const createInstructionTemplate = useCallback((template: Omit<InstructionTemplate, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newTemplate: InstructionTemplate = {
      ...template,
      id: `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    dispatch(addInstructionTemplate(newTemplate));
  }, [dispatch]);

  const updateInstructionTemplateById = useCallback((id: string, updates: Partial<InstructionTemplate>) => {
    const existingTemplate = instructionTemplates.find(t => t.id === id);
    if (existingTemplate) {
      const updatedTemplate: InstructionTemplate = {
        ...existingTemplate,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      dispatch(updateInstructionTemplate(updatedTemplate));
    }
  }, [dispatch, instructionTemplates]);

  const deleteInstructionTemplateById = useCallback((id: string) => {
    dispatch(deleteInstructionTemplate(id));
  }, [dispatch]);

  // 指令分类管理方法
  const createInstructionCategory = useCallback((category: Omit<InstructionCategory, 'id'>) => {
    const newCategory: InstructionCategory = {
      ...category,
      id: `category_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
    dispatch(addInstructionCategory(newCategory));
  }, [dispatch]);

  const updateInstructionCategoryById = useCallback((id: string, updates: Partial<InstructionCategory>) => {
    const existingCategory = instructionCategories.find(c => c.id === id);
    if (existingCategory) {
      const updatedCategory: InstructionCategory = {
        ...existingCategory,
        ...updates,
      };
      dispatch(updateInstructionCategory(updatedCategory));
    }
  }, [dispatch, instructionCategories]);

  const deleteInstructionCategoryById = useCallback((id: string) => {
    dispatch(deleteInstructionCategory(id));
  }, [dispatch]);

  // 指令预览和测试
  const previewInstruction = useCallback((template: string, variables: Record<string, string> = {}) => {
    let result = template;

    // 替换变量占位符 {{variable}}
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
      result = result.replace(regex, value);
    });

    return result;
  }, []);

  return {
    testConnection,
    isLoading,
    error,
    // 指令模板管理
    instructionTemplates,
    instructionCategories,
    createInstructionTemplate,
    updateInstructionTemplateById,
    deleteInstructionTemplateById,
    // 指令分类管理
    createInstructionCategory,
    updateInstructionCategoryById,
    deleteInstructionCategoryById,
    // 指令预览和测试
    previewInstruction,
  };
};