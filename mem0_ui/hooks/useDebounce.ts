/**
 * 防抖Hook
 * 
 * 用于优化频繁触发的操作，如搜索、API调用等：
 * - 延迟执行函数调用
 * - 减少不必要的API请求
 * - 提升用户体验和性能
 */

import { useCallback, useEffect, useRef } from 'react';

/**
 * 防抖Hook - 延迟执行函数
 */
export function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const callbackRef = useRef(callback);

  // 更新回调函数引用
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return useCallback(
    ((...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callbackRef.current(...args);
      }, delay);
    }) as T,
    [delay]
  );
}

/**
 * 防抖值Hook - 延迟更新值
 */
export function useDebouncedValue<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * 防抖状态Hook - 防抖的状态更新
 */
export function useDebouncedState<T>(
  initialValue: T,
  delay: number
): [T, T, (value: T) => void] {
  const [immediateValue, setImmediateValue] = useState(initialValue);
  const [debouncedValue, setDebouncedValue] = useState(initialValue);

  const debouncedSetValue = useDebounce((value: T) => {
    setDebouncedValue(value);
  }, delay);

  const setValue = useCallback((value: T) => {
    setImmediateValue(value);
    debouncedSetValue(value);
  }, [debouncedSetValue]);

  return [immediateValue, debouncedValue, setValue];
}

/**
 * 防抖搜索Hook - 专门用于搜索场景
 */
export function useDebouncedSearch(
  searchFunction: (query: string) => void | Promise<void>,
  delay: number = 300
) {
  const debouncedSearch = useDebounce(searchFunction, delay);

  return useCallback((query: string) => {
    if (query.trim() === '') {
      // 空查询立即执行
      searchFunction('');
    } else {
      // 非空查询使用防抖
      debouncedSearch(query);
    }
  }, [searchFunction, debouncedSearch]);
}

/**
 * 防抖API调用Hook - 专门用于API调用场景
 */
export function useDebouncedApiCall<T extends (...args: any[]) => Promise<any>>(
  apiFunction: T,
  delay: number = 500
): [T, boolean] {
  const [isLoading, setIsLoading] = useState(false);
  const loadingRef = useRef(false);

  const debouncedApiCall = useDebounce(async (...args: Parameters<T>) => {
    if (loadingRef.current) {
      return; // 如果已经在加载中，忽略新的调用
    }

    loadingRef.current = true;
    setIsLoading(true);

    try {
      const result = await apiFunction(...args);
      return result;
    } finally {
      loadingRef.current = false;
      setIsLoading(false);
    }
  }, delay);

  return [debouncedApiCall as T, isLoading];
}

/**
 * 防抖回调Hook - 防抖的事件处理
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  dependencies: any[],
  delay: number
): T {
  const debouncedCallback = useDebounce(callback, delay);

  return useCallback(debouncedCallback, [debouncedCallback, ...dependencies]);
}

// 导入useState
import { useState } from 'react';
