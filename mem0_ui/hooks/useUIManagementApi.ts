/**
 * UI管理API Hooks
 * 支持P0优先级的统计数据、活动日志和管理面板API
 */

import React, { useState, useCallback } from 'react';
import { useSelector } from 'react-redux';

import { RootState } from '@/store/store';
import { realMem0Client } from '@/lib/mem0-client/realClient';
import { 
  UIStatsResponse, 
  UIActivitiesResponse, 
  UIDashboardResponse,
  UIStatsQueryParams,
  UIActivitiesQueryParams,
  Mem0ApiError 
} from '@/types/mem0-api';

// ============================================================================
// 统计数据Hook
// ============================================================================

interface UseUIStatsReturn {
  stats: UIStatsResponse | null;
  isLoading: boolean;
  error: string | null;
  fetchStats: (params?: UIStatsQueryParams) => Promise<void>;
  refetch: () => Promise<void>;
}

export const useUIStats = (autoFetch?: boolean): UseUIStatsReturn => {
  const [stats, setStats] = useState<UIStatsResponse | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [lastParams, setLastParams] = useState<UIStatsQueryParams | undefined>(undefined);
  
  const user_id = useSelector((state: RootState) => state.profile.userId);

  const fetchStats = useCallback(async (params?: UIStatsQueryParams) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const queryParams = {
        user_id: params?.user_id || user_id,
        time_range: params?.time_range || '24h'
      };
      
      const response = await realMem0Client.getUIStats(queryParams);
      setStats(response);
      setLastParams(queryParams);
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to fetch UI stats';
      setError(errorMessage);
      console.error('Error fetching UI stats:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user_id]);

  const refetch = useCallback(async () => {
    if (lastParams !== undefined) {
      await fetchStats(lastParams);
    } else {
      await fetchStats();
    }
  }, [fetchStats, lastParams]);

  // 自动获取数据
  React.useEffect(() => {
    if (autoFetch) {
      fetchStats();
    }
  }, [autoFetch, fetchStats]);

  return {
    stats,
    isLoading,
    error,
    fetchStats,
    refetch
  };
};

// ============================================================================
// 活动日志Hook
// ============================================================================

interface UseUIActivitiesReturn {
  activities: UIActivitiesResponse | null;
  isLoading: boolean;
  error: string | null;
  fetchActivities: (params?: UIActivitiesQueryParams) => Promise<void>;
  refetch: () => Promise<void>;
  hasMore: boolean;
  loadMore: () => Promise<void>;
}

export const useUIActivities = (autoFetch?: boolean): UseUIActivitiesReturn => {
  const [activities, setActivities] = useState<UIActivitiesResponse | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [lastParams, setLastParams] = useState<UIActivitiesQueryParams | undefined>(undefined);
  
  const user_id = useSelector((state: RootState) => state.profile.userId);

  const fetchActivities = useCallback(async (params?: UIActivitiesQueryParams) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const queryParams = {
        user_id: params?.user_id || user_id,
        limit: params?.limit || 50,
        offset: params?.offset || 0,
        operation_type: params?.operation_type,
        start_time: params?.start_time,
        end_time: params?.end_time
      };
      
      const response = await realMem0Client.getUIActivities(queryParams);
      
      // 如果是第一页或者重新获取，直接设置数据
      if (!params?.offset || params.offset === 0) {
        setActivities(response);
      } else {
        // 如果是加载更多，追加数据
        setActivities(prev => prev ? {
          ...response,
          activities: [...prev.activities, ...response.activities]
        } : response);
      }
      
      setLastParams(queryParams);
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to fetch UI activities';
      setError(errorMessage);
      console.error('Error fetching UI activities:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user_id]);

  const refetch = useCallback(async () => {
    if (lastParams !== undefined) {
      await fetchActivities({ ...lastParams, offset: 0 });
    } else {
      await fetchActivities();
    }
  }, [fetchActivities, lastParams]);

  const loadMore = useCallback(async () => {
    if (activities && lastParams) {
      const currentOffset = lastParams.offset || 0;
      const currentLimit = lastParams.limit || 50;
      
      await fetchActivities({
        ...lastParams,
        offset: currentOffset + currentLimit
      });
    }
  }, [activities, lastParams, fetchActivities]);

  const hasMore = activities ? activities.has_more : false;

  // 自动获取数据
  React.useEffect(() => {
    if (autoFetch) {
      fetchActivities();
    }
  }, [autoFetch, fetchActivities]);

  return {
    activities,
    isLoading,
    error,
    fetchActivities,
    refetch,
    hasMore,
    loadMore
  };
};

// ============================================================================
// 管理面板Hook
// ============================================================================

interface UseUIDashboardReturn {
  dashboard: UIDashboardResponse | null;
  isLoading: boolean;
  error: string | null;
  fetchDashboard: (params?: { user_id?: string; time_range?: '1h' | '24h' | '7d' | '30d' }) => Promise<void>;
  refetch: () => Promise<void>;
}

export const useUIDashboard = (autoFetch?: boolean): UseUIDashboardReturn => {
  const [dashboard, setDashboard] = useState<UIDashboardResponse | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [lastParams, setLastParams] = useState<{ user_id?: string; time_range?: '1h' | '24h' | '7d' | '30d' } | undefined>(undefined);
  
  const user_id = useSelector((state: RootState) => state.profile.userId);

  const fetchDashboard = useCallback(async (params?: { user_id?: string; time_range?: '1h' | '24h' | '7d' | '30d' }) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const queryParams = {
        user_id: params?.user_id || user_id,
        time_range: params?.time_range || '24h'
      };
      
      const response = await realMem0Client.getUIDashboard(queryParams);
      setDashboard(response);
      setLastParams(queryParams);
    } catch (err: any) {
      const errorMessage = (err as Mem0ApiError).message || err.message || 'Failed to fetch dashboard data';
      setError(errorMessage);
      console.error('Error fetching dashboard data:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user_id]);

  const refetch = useCallback(async () => {
    if (lastParams !== undefined) {
      await fetchDashboard(lastParams);
    } else {
      await fetchDashboard();
    }
  }, [fetchDashboard, lastParams]);

  // 自动获取数据
  React.useEffect(() => {
    if (autoFetch) {
      fetchDashboard();
    }
  }, [autoFetch, fetchDashboard]);

  return {
    dashboard,
    isLoading,
    error,
    fetchDashboard,
    refetch
  };
};

// ============================================================================
// 组合Hook - 一次性获取所有UI管理数据
// ============================================================================

interface UseUIManagementReturn {
  stats: UIStatsResponse | null;
  activities: UIActivitiesResponse | null;
  dashboard: UIDashboardResponse | null;
  isLoading: boolean;
  error: string | null;
  refetchAll: () => Promise<void>;
  refetchStats: () => Promise<void>;
  refetchActivities: () => Promise<void>;
  refetchDashboard: () => Promise<void>;
}

export const useUIManagement = (): UseUIManagementReturn => {
  const statsHook = useUIStats();
  const activitiesHook = useUIActivities();
  const dashboardHook = useUIDashboard();

  const isLoading = statsHook.isLoading || activitiesHook.isLoading || dashboardHook.isLoading;
  const error = statsHook.error || activitiesHook.error || dashboardHook.error;

  const refetchAll = useCallback(async () => {
    await Promise.all([
      statsHook.refetch(),
      activitiesHook.refetch(),
      dashboardHook.refetch()
    ]);
  }, [statsHook.refetch, activitiesHook.refetch, dashboardHook.refetch]);

  return {
    stats: statsHook.stats,
    activities: activitiesHook.activities,
    dashboard: dashboardHook.dashboard,
    isLoading,
    error,
    refetchAll,
    refetchStats: statsHook.refetch,
    refetchActivities: activitiesHook.refetch,
    refetchDashboard: dashboardHook.refetch
  };
};