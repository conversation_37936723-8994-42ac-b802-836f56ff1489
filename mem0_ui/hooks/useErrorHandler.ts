/**
 * 统一错误处理Hook
 * 
 * 提供一致的错误处理机制：
 * - 统一的错误分类和处理
 * - 用户友好的错误提示
 * - 错误日志记录
 * - 错误恢复机制
 */

import { useCallback, useState } from 'react';
import { toast } from 'sonner';

import { logError, ErrorSeverity, ErrorCategory } from '@/lib/errorReporting';

export interface ErrorInfo {
  message: string;
  code?: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  context?: Record<string, any>;
  stack?: string;
}

export interface UseErrorHandlerReturn {
  error: string | null;
  isError: boolean;
  handleError: (error: unknown, context?: Record<string, any>) => void;
  handleApiError: (error: unknown, operation: string) => void;
  handleNetworkError: (error: unknown) => void;
  handleValidationError: (error: unknown, field?: string) => void;
  clearError: () => void;
  retryOperation: (operation: () => Promise<void>) => Promise<void>;
}

export function useErrorHandler(): UseErrorHandlerReturn {
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const categorizeError = useCallback((error: unknown): ErrorInfo => {
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      
      // 网络错误
      if (message.includes('network') || message.includes('fetch') || 
          message.includes('connection') || message.includes('timeout')) {
        return {
          message: error.message,
          category: 'network',
          severity: 'medium',
          stack: error.stack
        };
      }
      
      // API错误
      if (message.includes('api') || message.includes('server') || 
          message.includes('400') || message.includes('500')) {
        return {
          message: error.message,
          category: 'api',
          severity: 'high',
          stack: error.stack
        };
      }
      
      // 验证错误
      if (message.includes('validation') || message.includes('invalid') || 
          message.includes('required') || message.includes('format')) {
        return {
          message: error.message,
          category: 'validation',
          severity: 'low',
          stack: error.stack
        };
      }
      
      // 权限错误
      if (message.includes('unauthorized') || message.includes('forbidden') || 
          message.includes('401') || message.includes('403')) {
        return {
          message: error.message,
          category: 'auth',
          severity: 'high',
          stack: error.stack
        };
      }
      
      // 默认为应用错误
      return {
        message: error.message,
        category: 'application',
        severity: 'medium',
        stack: error.stack
      };
    }
    
    // 非Error对象
    return {
      message: String(error),
      category: 'unknown',
      severity: 'medium'
    };
  }, []);

  const getUserFriendlyMessage = useCallback((errorInfo: ErrorInfo): string => {
    switch (errorInfo.category) {
      case 'network':
        return '网络连接出现问题，请检查您的网络连接后重试';
      case 'api':
        return '服务器暂时无法响应，请稍后重试';
      case 'validation':
        return '输入的数据格式不正确，请检查后重新提交';
      case 'auth':
        return '您没有权限执行此操作，请重新登录';
      case 'application':
        return '应用出现了一个问题，我们正在努力修复';
      default:
        return '发生了未知错误，请稍后重试';
    }
  }, []);

  const showToast = useCallback((errorInfo: ErrorInfo, userMessage: string) => {
    switch (errorInfo.severity) {
      case 'low':
        toast.warning(userMessage);
        break;
      case 'medium':
        toast.error(userMessage);
        break;
      case 'high':
        toast.error(userMessage, {
          duration: 10000, // 高严重性错误显示更长时间
          action: {
            label: '报告问题',
            onClick: () => {
              // 可以打开问题报告对话框
              console.log('Report issue:', errorInfo);
            }
          }
        });
        break;
    }
  }, []);

  const handleError = useCallback((error: unknown, context?: Record<string, any>) => {
    const errorInfo = categorizeError(error);
    errorInfo.context = context;
    
    // 记录错误
    logError(errorInfo);
    
    // 获取用户友好的错误消息
    const userMessage = getUserFriendlyMessage(errorInfo);
    
    // 设置错误状态
    setError(userMessage);
    
    // 显示Toast通知
    showToast(errorInfo, userMessage);
    
    // 在开发环境中打印详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Handler');
      console.error('Original Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Context:', context);
      console.groupEnd();
    }
  }, [categorizeError, getUserFriendlyMessage, showToast]);

  const handleApiError = useCallback((error: unknown, operation: string) => {
    handleError(error, { operation, type: 'api_call' });
  }, [handleError]);

  const handleNetworkError = useCallback((error: unknown) => {
    handleError(error, { type: 'network_error' });
  }, [handleError]);

  const handleValidationError = useCallback((error: unknown, field?: string) => {
    handleError(error, { field, type: 'validation_error' });
  }, [handleError]);

  const retryOperation = useCallback(async (operation: () => Promise<void>) => {
    try {
      clearError();
      await operation();
    } catch (error) {
      handleError(error, { type: 'retry_failed' });
      throw error;
    }
  }, [clearError, handleError]);

  return {
    error,
    isError: error !== null,
    handleError,
    handleApiError,
    handleNetworkError,
    handleValidationError,
    clearError,
    retryOperation
  };
}
