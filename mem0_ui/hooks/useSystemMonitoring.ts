/**
 * 系统监控Hook
 * 
 * 提供系统监控相关的状态管理和操作方法
 */

import { useState, useEffect, useCallback, useRef } from 'react';

import SystemMonitor, { 
  SystemHealth, 
  APIMetrics, 
  ResourceUsage, 
  AlertEvent 
} from '@/lib/monitoring/SystemMonitor';

interface UseSystemMonitoringReturn {
  // 系统健康状态
  systemHealth: SystemHealth | null;
  isHealthLoading: boolean;
  
  // API性能指标
  apiMetrics: {
    averageResponseTime: number;
    errorRate: number;
    requestCount: number;
    slowestEndpoints: Array<{ endpoint: string; avgTime: number }>;
  } | null;
  
  // 资源使用情况
  resourceUsage: ResourceUsage | null;
  
  // 告警事件
  alertEvents: AlertEvent[];
  unresolvedAlertCount: number;
  
  // 操作方法
  refreshSystemHealth: () => Promise<void>;
  resolveAlert: (alertId: string) => void;
  clearAllAlerts: () => void;
  
  // 监控控制
  startMonitoring: () => void;
  stopMonitoring: () => void;
  isMonitoring: boolean;
}

export const useSystemMonitoring = (): UseSystemMonitoringReturn => {
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [isHealthLoading, setIsHealthLoading] = useState(false);
  const [apiMetrics, setApiMetrics] = useState<{
    averageResponseTime: number;
    errorRate: number;
    requestCount: number;
    slowestEndpoints: Array<{ endpoint: string; avgTime: number }>;
  } | null>(null);
  const [resourceUsage, setResourceUsage] = useState<ResourceUsage | null>(null);
  const [alertEvents, setAlertEvents] = useState<AlertEvent[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  
  const systemMonitor = useRef<SystemMonitor>(SystemMonitor.getInstance());
  const updateInterval = useRef<NodeJS.Timeout | null>(null);

  // 刷新系统健康状态
  const refreshSystemHealth = useCallback(async () => {
    setIsHealthLoading(true);
    try {
      const health = await systemMonitor.current.getSystemHealth();
      setSystemHealth(health);
    } catch (error) {
      console.error('Failed to refresh system health:', error);
    } finally {
      setIsHealthLoading(false);
    }
  }, []);

  // 更新所有监控数据
  const updateMonitoringData = useCallback(() => {
    // 更新API指标
    const metrics = systemMonitor.current.getAPIMetrics();
    setApiMetrics(metrics);

    // 更新资源使用情况
    const usage = systemMonitor.current.getResourceUsage();
    setResourceUsage(usage);

    // 更新告警事件
    const alerts = systemMonitor.current.getAlertEvents();
    setAlertEvents(alerts);
  }, []);

  // 解决告警
  const resolveAlert = useCallback((alertId: string) => {
    systemMonitor.current.resolveAlert(alertId);
    updateMonitoringData();
  }, [updateMonitoringData]);

  // 清除所有告警
  const clearAllAlerts = useCallback(() => {
    alertEvents.forEach(alert => {
      if (!alert.resolved) {
        systemMonitor.current.resolveAlert(alert.id);
      }
    });
    updateMonitoringData();
  }, [alertEvents, updateMonitoringData]);

  // 开始监控
  const startMonitoring = useCallback(() => {
    if (isMonitoring) return;

    systemMonitor.current.startMonitoring();
    setIsMonitoring(true);

    // 定期更新数据
    updateInterval.current = setInterval(() => {
      updateMonitoringData();
      refreshSystemHealth();
    }, 10000); // 每10秒更新一次

    // 立即更新一次
    updateMonitoringData();
    refreshSystemHealth();
  }, [isMonitoring, updateMonitoringData, refreshSystemHealth]);

  // 停止监控
  const stopMonitoring = useCallback(() => {
    if (!isMonitoring) return;

    systemMonitor.current.stopMonitoring();
    setIsMonitoring(false);

    if (updateInterval.current) {
      clearInterval(updateInterval.current);
      updateInterval.current = null;
    }
  }, [isMonitoring]);

  // 计算未解决的告警数量
  const unresolvedAlertCount = alertEvents.filter(alert => !alert.resolved).length;

  // 组件挂载时自动开始监控
  useEffect(() => {
    startMonitoring();

    // 组件卸载时清理
    return () => {
      stopMonitoring();
    };
  }, [startMonitoring, stopMonitoring]);

  // 监听API调用，记录性能指标
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // 拦截fetch请求来记录API性能
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const startTime = Date.now();
      const url = typeof args[0] === 'string' ? args[0] : (args[0] as Request).url;
      const method = args[1]?.method || 'GET';
      
      try {
        const response = await originalFetch(...args);
        const responseTime = Date.now() - startTime;
        
        // 只记录API调用（排除静态资源）
        if (url.includes('/api/') || url.includes('localhost:8765')) {
          systemMonitor.current.recordAPICall(
            url,
            method,
            responseTime,
            response.status
          );
        }
        
        return response;
      } catch (error) {
        const responseTime = Date.now() - startTime;
        
        if (url.includes('/api/') || url.includes('localhost:8765')) {
          systemMonitor.current.recordAPICall(
            url,
            method,
            responseTime,
            0 // 网络错误
          );
        }
        
        throw error;
      }
    };

    // 清理函数
    return () => {
      window.fetch = originalFetch;
    };
  }, []);

  return {
    systemHealth,
    isHealthLoading,
    apiMetrics,
    resourceUsage,
    alertEvents,
    unresolvedAlertCount,
    refreshSystemHealth,
    resolveAlert,
    clearAllAlerts,
    startMonitoring,
    stopMonitoring,
    isMonitoring
  };
};
