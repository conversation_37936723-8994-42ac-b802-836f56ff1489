/**
 * 真实Mem0 API格式的记忆管理handlers
 * 基于http://localhost:8000/openapi.json规范
 */

import { http, HttpResponse } from 'msw';

import { 
  MemoryCreateRequest, 
  UpdateMemoryRequest, 
  SearchRequest,
  BatchUpdateRequest,
  BatchDeleteRequest,
  Mem0Memory 
} from '@/types/mem0-api';

import { 
  mockMemories, 
  generateMockMemories, 
  searchMemories, 
  filterMemories, 
  paginateData,
  generateHealthResponse,
  generateBatchResponse,
  mockUserIds,
  mockAgentIds
} from '../data/realMockData';

// API基础URL列表
const API_BASES = [
  'http://localhost:8000',
  'http://localhost:8765',
  process.env.NEXT_PUBLIC_API_URL,
  process.env.NEXT_PUBLIC_MEM0_API_URL
].filter((url): url is string => Boolean(url));

// 内存中的记忆数据存储
let memoryStore = [...mockMemories];

// 生成handlers
export const realMemoriesHandlers = API_BASES.flatMap(baseUrl => [
  // 健康检查
  http.get(`${baseUrl}/health`, () => {
    return HttpResponse.json(generateHealthResponse());
  }),

  // 获取所有记忆 - GET /v1/memories/
  http.get(`${baseUrl}/v1/memories/`, ({ request }) => {
    const url = new URL(request.url);
    const user_id = url.searchParams.get('user_id');
    const agent_id = url.searchParams.get('agent_id');
    const run_id = url.searchParams.get('run_id');
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const enable_graph = url.searchParams.get('enable_graph') === 'true';
    const output_format = url.searchParams.get('output_format');

    let filteredMemories = memoryStore;

    // 应用过滤器
    const filters: Record<string, any> = {};
    if (user_id) filters.user_id = user_id;
    if (agent_id) filters.agent_id = agent_id;
    if (run_id) filters.run_id = run_id;

    if (Object.keys(filters).length > 0) {
      filteredMemories = filterMemories(memoryStore, filters);
    }

    // 分页
    const result = paginateData(filteredMemories, limit, 0);

    return HttpResponse.json({
      memories: result.items,
      total: result.total,
      limit: result.limit,
      offset: result.offset
    });
  }),

  // 创建记忆 - POST /v1/memories/
  http.post(`${baseUrl}/v1/memories/`, async ({ request }) => {
    try {
      const body = await request.json() as MemoryCreateRequest;
      
      if (!body.messages || !Array.isArray(body.messages) || body.messages.length === 0) {
        return HttpResponse.json(
          { detail: [{ loc: ['body', 'messages'], msg: 'Messages are required', type: 'value_error' }] },
          { status: 422 }
        );
      }

      // 从messages中提取记忆内容
      const memoryContent = body.messages
        .filter(msg => msg.role === 'user')
        .map(msg => typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content))
        .join(' ');

      const newMemory: Mem0Memory = {
        id: `mem_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        memory: memoryContent,
        hash: `hash_${Math.random().toString(36).substring(2, 15)}`,
        user_id: body.user_id,
        agent_id: body.agent_id,
        run_id: body.run_id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        metadata: body.metadata || {},
        categories: body.custom_categories?.map(cat => Object.keys(cat)[0]) || [],
        score: Math.random() * 0.3 + 0.7
      };

      memoryStore.push(newMemory);

      return HttpResponse.json({
        message: 'Memory created successfully',
        memory: newMemory
      }, { status: 201 });
    } catch (error) {
      return HttpResponse.json(
        { detail: [{ loc: ['body'], msg: 'Invalid request body', type: 'value_error' }] },
        { status: 422 }
      );
    }
  }),

  // 获取单个记忆 - GET /v1/memories/{memory_id}/
  http.get(`${baseUrl}/v1/memories/:memory_id/`, ({ params }) => {
    const { memory_id } = params;
    const memory = memoryStore.find(m => m.id === memory_id);
    
    if (!memory) {
      return HttpResponse.json(
        { detail: 'Memory not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json(memory);
  }),

  // 更新记忆 - PUT /v1/memories/{memory_id}/
  http.put(`${baseUrl}/v1/memories/:memory_id/`, async ({ params, request }) => {
    const { memory_id } = params;
    const memoryIndex = memoryStore.findIndex(m => m.id === memory_id);
    
    if (memoryIndex === -1) {
      return HttpResponse.json(
        { detail: 'Memory not found' },
        { status: 404 }
      );
    }

    try {
      const body = await request.json() as UpdateMemoryRequest;
      
      if (!body.text) {
        return HttpResponse.json(
          { detail: [{ loc: ['body', 'text'], msg: 'Text is required', type: 'value_error' }] },
          { status: 422 }
        );
      }

      const updatedMemory = {
        ...memoryStore[memoryIndex],
        memory: body.text,
        metadata: { ...memoryStore[memoryIndex].metadata, ...body.metadata },
        updated_at: new Date().toISOString()
      };

      memoryStore[memoryIndex] = updatedMemory;

      return HttpResponse.json({
        message: 'Memory updated successfully',
        memory: updatedMemory
      });
    } catch (error) {
      return HttpResponse.json(
        { detail: [{ loc: ['body'], msg: 'Invalid request body', type: 'value_error' }] },
        { status: 422 }
      );
    }
  }),

  // 删除记忆 - DELETE /v1/memories/{memory_id}/
  http.delete(`${baseUrl}/v1/memories/:memory_id/`, ({ params }) => {
    const { memory_id } = params;
    const memoryIndex = memoryStore.findIndex(m => m.id === memory_id);
    
    if (memoryIndex === -1) {
      return HttpResponse.json(
        { detail: 'Memory not found' },
        { status: 404 }
      );
    }

    memoryStore.splice(memoryIndex, 1);

    return HttpResponse.json({
      message: 'Memory deleted successfully'
    });
  }),

  // 搜索记忆 - POST /v1/memories/search/
  http.post(`${baseUrl}/v1/memories/search/`, async ({ request }) => {
    try {
      const body = await request.json() as SearchRequest;
      
      if (!body.query) {
        return HttpResponse.json(
          { detail: [{ loc: ['body', 'query'], msg: 'Query is required', type: 'value_error' }] },
          { status: 422 }
        );
      }

      let filteredMemories = memoryStore;

      // 应用过滤器
      const filters: Record<string, any> = {};
      if (body.user_id) filters.user_id = body.user_id;
      if (body.agent_id) filters.agent_id = body.agent_id;
      if (body.run_id) filters.run_id = body.run_id;
      if (body.filters) Object.assign(filters, body.filters);

      if (Object.keys(filters).length > 0) {
        filteredMemories = filterMemories(memoryStore, filters);
      }

      // 执行搜索
      const searchResults = searchMemories(body.query, filteredMemories);

      return HttpResponse.json({
        memories: searchResults,
        total: searchResults.length,
        query: body.query
      });
    } catch (error) {
      return HttpResponse.json(
        { detail: [{ loc: ['body'], msg: 'Invalid request body', type: 'value_error' }] },
        { status: 422 }
      );
    }
  }),

  // 删除所有记忆 - DELETE /v1/memories/
  http.delete(`${baseUrl}/v1/memories/`, ({ request }) => {
    const url = new URL(request.url);
    const user_id = url.searchParams.get('user_id');
    const agent_id = url.searchParams.get('agent_id');
    const run_id = url.searchParams.get('run_id');

    let deletedCount = 0;

    if (user_id || agent_id || run_id) {
      // 删除特定条件的记忆
      const initialLength = memoryStore.length;
      memoryStore = memoryStore.filter(memory => {
        const shouldDelete = 
          (!user_id || memory.user_id === user_id) &&
          (!agent_id || memory.agent_id === agent_id) &&
          (!run_id || memory.run_id === run_id);
        return !shouldDelete;
      });
      deletedCount = initialLength - memoryStore.length;
    } else {
      // 删除所有记忆
      deletedCount = memoryStore.length;
      memoryStore = [];
    }

    return HttpResponse.json({
      message: `${deletedCount} memories deleted successfully`,
      deleted_count: deletedCount
    });
  }),

  // 批量更新记忆 - PUT /v1/batch/
  http.put(`${baseUrl}/v1/batch/`, async ({ request }) => {
    try {
      const body = await request.json() as BatchUpdateRequest;

      if (!body.memories || !Array.isArray(body.memories) || body.memories.length === 0) {
        return HttpResponse.json(
          { detail: [{ loc: ['body', 'memories'], msg: 'Memories array is required', type: 'value_error' }] },
          { status: 422 }
        );
      }

      if (body.memories.length > 1000) {
        return HttpResponse.json(
          { detail: [{ loc: ['body', 'memories'], msg: 'Maximum 1000 memories per request', type: 'value_error' }] },
          { status: 422 }
        );
      }

      let successCount = 0;
      let failedCount = 0;
      const errors: Array<{ memory_id: string; error: string }> = [];

      body.memories.forEach(memoryUpdate => {
        const memoryIndex = memoryStore.findIndex(m => m.id === memoryUpdate.memory_id);

        if (memoryIndex === -1) {
          failedCount++;
          errors.push({
            memory_id: memoryUpdate.memory_id,
            error: 'Memory not found'
          });
        } else if (!memoryUpdate.text) {
          failedCount++;
          errors.push({
            memory_id: memoryUpdate.memory_id,
            error: 'Text is required'
          });
        } else {
          memoryStore[memoryIndex] = {
            ...memoryStore[memoryIndex],
            memory: memoryUpdate.text,
            updated_at: new Date().toISOString()
          };
          successCount++;
        }
      });

      return HttpResponse.json(generateBatchResponse(successCount, failedCount));
    } catch (error) {
      return HttpResponse.json(
        { detail: [{ loc: ['body'], msg: 'Invalid request body', type: 'value_error' }] },
        { status: 422 }
      );
    }
  }),

  // 批量删除记忆 - DELETE /v1/batch/
  http.delete(`${baseUrl}/v1/batch/`, async ({ request }) => {
    try {
      const body = await request.json() as BatchDeleteRequest;

      if (!body.memories || !Array.isArray(body.memories) || body.memories.length === 0) {
        return HttpResponse.json(
          { detail: [{ loc: ['body', 'memories'], msg: 'Memories array is required', type: 'value_error' }] },
          { status: 422 }
        );
      }

      if (body.memories.length > 1000) {
        return HttpResponse.json(
          { detail: [{ loc: ['body', 'memories'], msg: 'Maximum 1000 memories per request', type: 'value_error' }] },
          { status: 422 }
        );
      }

      let successCount = 0;
      let failedCount = 0;
      const errors: Array<{ memory_id: string; error: string }> = [];

      body.memories.forEach(memoryDelete => {
        const memoryIndex = memoryStore.findIndex(m => m.id === memoryDelete.memory_id);

        if (memoryIndex === -1) {
          failedCount++;
          errors.push({
            memory_id: memoryDelete.memory_id,
            error: 'Memory not found'
          });
        } else {
          memoryStore.splice(memoryIndex, 1);
          successCount++;
        }
      });

      return HttpResponse.json(generateBatchResponse(successCount, failedCount));
    } catch (error) {
      return HttpResponse.json(
        { detail: [{ loc: ['body'], msg: 'Invalid request body', type: 'value_error' }] },
        { status: 422 }
      );
    }
  }),

  // 重置所有记忆 - POST /reset
  http.post(`${baseUrl}/reset`, () => {
    memoryStore = [...mockMemories];
    return HttpResponse.json({
      message: 'All memories have been reset',
      total_memories: memoryStore.length
    });
  }),

  // 注意：错误格式的stats端点已删除，使用正确的 /v1/stats 端点

  // 活动日志API - 后续Mem0 Server会支持
  http.get(`${baseUrl}/activities`, ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    const activities = Array.from({ length: Math.min(limit, 10) }, (_, i) => ({
      id: `activity_${Date.now()}_${i}`,
      user_id: userId || 'default',
      action: ['create', 'update', 'delete', 'search'][Math.floor(Math.random() * 4)],
      resource_type: 'memory',
      resource_id: `mem_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString(),
      metadata: {
        response_time: Math.floor(Math.random() * 200) + 50,
        success: Math.random() > 0.1
      }
    }));

    return HttpResponse.json({
      activities,
      total: 50,
      limit,
      offset
    });
  }),

  // 用户管理API - 后续Mem0 Server会支持
  http.get(`${baseUrl}/users/:userId`, ({ params }) => {
    const { userId } = params;

    return HttpResponse.json({
      id: userId,
      user_id: userId,
      name: `User ${userId}`,
      email: `${userId}@example.com`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_active: true,
      metadata: {}
    });
  }),

  http.post(`${baseUrl}/users`, async ({ request }) => {
    const userData = await request.json() as any;

    return HttpResponse.json({
      id: `user_${Date.now()}`,
      user_id: userData.user_id || `user_${Date.now()}`,
      name: userData.name || 'New User',
      email: userData.email || '<EMAIL>',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_active: true,
      metadata: userData.metadata || {}
    }, { status: 201 });
  }),

  // 注意：Apps API 在实际Mem0 Server中不存在，已删除相关handler

  // Memory Categories API - 修正为正确的端点格式
  http.get(`${baseUrl}/v1/memories/categories`, ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id');

    // 从现有记忆中提取分类
    const userMemories = userId
      ? memoryStore.filter(m => m.user_id === userId)
      : memoryStore;

    const categories = Array.from(new Set(
      userMemories.flatMap(m => m.categories || [])
    ));

    // 如果没有分类，返回一些默认分类
    const defaultCategories = categories.length > 0
      ? categories
      : ['work', 'personal', 'learning', 'projects', 'ideas', 'meetings'];

    return HttpResponse.json({
      categories: defaultCategories,
      total: defaultCategories.length
    });
  }),

  // Stats API - 添加统计信息端点
  http.get(`${baseUrl}/v1/stats`, ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id');

    const userMemories = userId
      ? memoryStore.filter(m => m.user_id === userId)
      : memoryStore;

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayMemories = userMemories.filter(m => {
      if (!m.created_at) return false;
      const memoryDate = new Date(m.created_at);
      return memoryDate >= today;
    });

    return HttpResponse.json({
      total_memories: userMemories.length,
      today_operations: todayMemories.length + Math.floor(Math.random() * 10),
      avg_response_time: Math.floor(Math.random() * 200) + 50,
      active_users: new Set(userMemories.map(m => m.user_id).filter(Boolean)).size || 1,
      top_apps: []
    });
  }),

  // 获取记忆历史 - GET /v1/memories/{memory_id}/history/
  http.get(`${baseUrl}/v1/memories/:memory_id/history/`, ({ params }) => {
    const { memory_id } = params;
    const memory = memoryStore.find(m => m.id === memory_id);

    if (!memory) {
      return HttpResponse.json(
        { detail: 'Memory not found' },
        { status: 404 }
      );
    }

    // 生成模拟历史数据
    const history = [
      {
        id: `hist_${memory_id}_1`,
        memory_id: memory_id,
        operation: 'create',
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        user_id: memory.user_id,
        run_id: memory.run_id,
        agent_id: memory.agent_id,
        custom_categories: memory.custom_categories || [],
        changes: { created: true }
      },
      {
        id: `hist_${memory_id}_2`,
        memory_id: memory_id,
        operation: 'update',
        timestamp: new Date(Date.now() - 43200000).toISOString(),
        user_id: memory.user_id,
        run_id: memory.run_id,
        agent_id: memory.agent_id,
        custom_categories: memory.custom_categories || [],
        changes: { memory: 'Updated content' },
        previous_value: 'Old content',
        new_value: memory.memory
      }
    ];

    return HttpResponse.json(history);
  })
]);
