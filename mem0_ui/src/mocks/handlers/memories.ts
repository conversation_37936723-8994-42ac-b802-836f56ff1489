import { http, HttpResponse } from 'msw';

import { Mem0Memory, MemoryCreateRequest, UpdateMemoryRequest } from '@/types/mem0-api';

import {
  mockMemories,
  paginateData,
  searchMemories,
  filterMemories
} from '../data/mockData';

// 支持多个API base URL
const API_BASES = [
  'http://localhost:8765/api/v1',
  'http://localhost:8000/v1',
  process.env.NEXT_PUBLIC_API_URL ? process.env.NEXT_PUBLIC_API_URL + '/v1' : null,
  process.env.NEXT_PUBLIC_MEM0_API_URL
].filter((url): url is string => Boolean(url));

// 创建所有base URL的handlers
const createHandlersForBase = (baseUrl: string) => [
  // Get memories with pagination and filtering
  http.get(`${baseUrl}/v1/memories/`, ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    if (!userId) {
      return HttpResponse.json(
        { error: 'Missing user_id parameter', message: 'user_id is required' },
        { status: 400 }
      );
    }

    // 过滤用户的记忆
    const userMemories = mockMemories.filter(m => m.user_id === userId);
    const result = paginateData(userMemories, limit, offset);

    return HttpResponse.json({
      memories: result.items,
      total: result.total,
      limit: result.limit,
      offset: result.offset
    });
  }),

  // Search memories
  http.post(`${baseUrl}/v1/memories/search/`, async ({ request }) => {
    const body = await request.json() as any;
    const { user_id, query, limit = 10, offset = 0, filters } = body;

    if (!user_id) {
      return HttpResponse.json(
        { error: 'Missing user_id', message: 'user_id is required' },
        { status: 400 }
      );
    }

    // 过滤用户记忆
    let userMemories = mockMemories.filter(m => m.user_id === user_id);

    // 应用搜索查询
    if (query) {
      userMemories = searchMemories(query, userMemories);
    }

    // 应用过滤器
    if (filters) {
      userMemories = filterMemories(userMemories, filters);
    }

    const result = paginateData(userMemories, limit, offset);

    return HttpResponse.json({
      memories: result.items,
      total: result.total,
      limit: result.limit,
      offset: result.offset
    });
  }),

  // Get single memory
  http.get(`${baseUrl}/memories/:id`, ({ params, request }) => {
    const { id } = params;
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id');

    if (!userId) {
      return HttpResponse.json(
        { error: 'Missing user_id parameter', message: 'user_id is required' },
        { status: 400 }
      );
    }

    const memory = mockMemories.find(m => m.id === id && m.user_id === userId);

    if (!memory) {
      return HttpResponse.json(
        { error: 'Memory not found', message: `Memory with id ${id} not found` },
        { status: 404 }
      );
    }

    return HttpResponse.json(memory);
  }),

  // Create memory
  http.post(`${baseUrl}/v1/memories/`, async ({ request }) => {
    const body = await request.json() as MemoryCreateRequest;

    if (!body.text || !body.user_id) {
      return HttpResponse.json(
        { error: 'Missing required fields', message: 'text and user_id are required' },
        { status: 400 }
      );
    }

    const newMemory: Mem0Memory = {
      id: `mem_${Date.now()}`,
      memory: body.text || (body.messages && typeof body.messages[0]?.content === 'string' ? body.messages[0].content : '') || '',
      text: body.text || (body.messages && typeof body.messages[0]?.content === 'string' ? body.messages[0].content : '') || '',
      user_id: body.user_id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      metadata: body.metadata || {},
      app_name: body.app_name || 'mem0-ui',
      state: 'active'
    };

    // 模拟添加到数据中
    mockMemories.unshift(newMemory);

    return HttpResponse.json(newMemory, { status: 201 });
  }),

  // Update memory
  http.put(`${baseUrl}/memories/:id`, async ({ params, request }) => {
    const { id } = params;
    const body = await request.json() as UpdateMemoryRequest;

    const memoryIndex = mockMemories.findIndex(m => m.id === id);

    if (memoryIndex === -1) {
      return HttpResponse.json(
        { error: 'Memory not found', message: `Memory with id ${id} not found` },
        { status: 404 }
      );
    }

    // 更新记忆
    const updatedMemory = {
      ...mockMemories[memoryIndex],
      ...(body.text && { text: body.text }),
      ...(body.metadata && { metadata: { ...mockMemories[memoryIndex].metadata, ...body.metadata } }),
      ...(body.state && { state: body.state }),
      updated_at: new Date().toISOString()
    };

    mockMemories[memoryIndex] = updatedMemory;

    return HttpResponse.json(updatedMemory);
  }),

  // Delete memory
  http.delete(`${baseUrl}/memories/:id`, ({ params, request }) => {
    const { id } = params;
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id');

    if (!userId) {
      return HttpResponse.json(
        { error: 'Missing user_id parameter', message: 'user_id is required' },
        { status: 400 }
      );
    }

    const memoryIndex = mockMemories.findIndex(m => m.id === id && m.user_id === userId);

    if (memoryIndex === -1) {
      return HttpResponse.json(
        { error: 'Memory not found', message: `Memory with id ${id} not found` },
        { status: 404 }
      );
    }

    // 删除记忆
    mockMemories.splice(memoryIndex, 1);

    return HttpResponse.json({ message: 'Memory deleted successfully' });
  }),

  // Batch operations
  http.post(`${baseUrl}/memories/batch`, async ({ request }) => {
    const body = await request.json() as any;
    const { operation, memory_ids, data } = body;

    if (!operation || !memory_ids || !Array.isArray(memory_ids)) {
      return HttpResponse.json(
        { error: 'Invalid request', message: 'operation and memory_ids array are required' },
        { status: 400 }
      );
    }

    let successCount = 0;
    let failedCount = 0;
    const errors: Array<{ memory_id: string; error: string }> = [];

    for (const memoryId of memory_ids) {
      const memoryIndex = mockMemories.findIndex(m => m.id === memoryId);

      if (memoryIndex === -1) {
        failedCount++;
        errors.push({ memory_id: memoryId, error: 'Memory not found' });
        continue;
      }

      try {
        if (operation === 'delete') {
          mockMemories.splice(memoryIndex, 1);
        } else if (operation === 'update' && data) {
          mockMemories[memoryIndex] = {
            ...mockMemories[memoryIndex],
            ...data,
            updated_at: new Date().toISOString()
          };
        }
        successCount++;
      } catch (error) {
        failedCount++;
        errors.push({ memory_id: memoryId, error: 'Operation failed' });
      }
    }

    return HttpResponse.json({
      success_count: successCount,
      failed_count: failedCount,
      ...(errors.length > 0 && { errors })
    });
  })
];

// 为所有API base创建handlers
export const memoriesHandlers = API_BASES.flatMap(createHandlersForBase);
