/**
 * Graph Memory API Mock Handlers
 * 基于http://localhost:8000/openapi.json规范
 */

import { http, HttpResponse } from 'msw';

// API基础URL列表
const API_BASES = [
  'http://localhost:8000',
  'http://localhost:8765',
  process.env.NEXT_PUBLIC_API_URL,
  process.env.NEXT_PUBLIC_MEM0_API_URL
].filter((url): url is string => Boolean(url));

// Mock数据
const mockEntities = [
  {
    id: 'entity_1',
    name: '<PERSON>',
    type: 'Person',
    properties: {
      age: 30,
      occupation: 'Software Engineer',
      location: 'San Francisco'
    },
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-15T10:30:00Z'
  },
  {
    id: 'entity_2',
    name: 'OpenAI',
    type: 'Organization',
    properties: {
      industry: 'AI Research',
      founded: 2015,
      location: 'San Francisco'
    },
    created_at: '2024-01-15T11:00:00Z',
    updated_at: '2024-01-15T11:00:00Z'
  },
  {
    id: 'entity_3',
    name: 'Machine Learning',
    type: 'Concept',
    properties: {
      category: 'Technology',
      complexity: 'High'
    },
    created_at: '2024-01-15T11:30:00Z',
    updated_at: '2024-01-15T11:30:00Z'
  }
];

const mockRelationships = [
  {
    id: 'rel_1',
    source: 'entity_1',
    target: 'entity_2',
    type: 'WORKS_AT',
    properties: {
      since: '2022-01-01',
      role: 'Senior Engineer'
    },
    weight: 0.9,
    created_at: '2024-01-15T12:00:00Z',
    updated_at: '2024-01-15T12:00:00Z'
  },
  {
    id: 'rel_2',
    source: 'entity_1',
    target: 'entity_3',
    type: 'INTERESTED_IN',
    properties: {
      level: 'Expert',
      years_experience: 8
    },
    weight: 0.8,
    created_at: '2024-01-15T12:30:00Z',
    updated_at: '2024-01-15T12:30:00Z'
  },
  {
    id: 'rel_3',
    source: 'entity_2',
    target: 'entity_3',
    type: 'RESEARCHES',
    properties: {
      focus_area: 'Large Language Models',
      investment: 'High'
    },
    weight: 0.95,
    created_at: '2024-01-15T13:00:00Z',
    updated_at: '2024-01-15T13:00:00Z'
  }
];

// 生成handlers
export const graphMemoryHandlers = API_BASES.flatMap(baseUrl => [
  // 获取图实体 - GET /v1/graph/entities
  http.get(`${baseUrl}/v1/graph/entities`, ({ request }) => {
    const url = new URL(request.url);
    const user_id = url.searchParams.get('user_id');
    const entity_type = url.searchParams.get('entity_type');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '100'), 500);
    const offset = parseInt(url.searchParams.get('offset') || '0');

    // 验证必需参数
    if (!user_id) {
      return HttpResponse.json(
        { 
          detail: [{ 
            loc: ['query', 'user_id'], 
            msg: 'field required', 
            type: 'value_error.missing' 
          }] 
        },
        { status: 400 }
      );
    }

    // 验证limit参数
    if (limit > 500) {
      return HttpResponse.json(
        { 
          detail: [{ 
            loc: ['query', 'limit'], 
            msg: 'ensure this value is less than or equal to 500', 
            type: 'value_error.number.not_le',
            ctx: { limit_value: 500 }
          }] 
        },
        { status: 422 }
      );
    }

    let filteredEntities = [...mockEntities];

    // 应用类型过滤
    if (entity_type) {
      filteredEntities = filteredEntities.filter(e => e.type === entity_type);
    }

    // 应用分页
    const paginatedEntities = filteredEntities.slice(offset, offset + limit);

    return HttpResponse.json({
      entities: paginatedEntities,
      total: filteredEntities.length,
      limit,
      offset
    });
  }),

  // 获取图关系 - GET /v1/graph/relationships
  http.get(`${baseUrl}/v1/graph/relationships`, ({ request }) => {
    const url = new URL(request.url);
    const user_id = url.searchParams.get('user_id');
    const source_entity = url.searchParams.get('source_entity');
    const target_entity = url.searchParams.get('target_entity');
    const relation_type = url.searchParams.get('relation_type');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '100'), 500);
    const offset = parseInt(url.searchParams.get('offset') || '0');

    // 验证必需参数
    if (!user_id) {
      return HttpResponse.json(
        { 
          detail: [{ 
            loc: ['query', 'user_id'], 
            msg: 'field required', 
            type: 'value_error.missing' 
          }] 
        },
        { status: 400 }
      );
    }

    // 验证limit参数
    if (limit > 500) {
      return HttpResponse.json(
        { 
          detail: [{ 
            loc: ['query', 'limit'], 
            msg: 'ensure this value is less than or equal to 500', 
            type: 'value_error.number.not_le',
            ctx: { limit_value: 500 }
          }] 
        },
        { status: 422 }
      );
    }

    let filteredRelationships = [...mockRelationships];

    // 应用过滤器
    if (source_entity) {
      filteredRelationships = filteredRelationships.filter(r => r.source === source_entity);
    }
    if (target_entity) {
      filteredRelationships = filteredRelationships.filter(r => r.target === target_entity);
    }
    if (relation_type) {
      filteredRelationships = filteredRelationships.filter(r => r.type === relation_type);
    }

    // 应用分页
    const paginatedRelationships = filteredRelationships.slice(offset, offset + limit);

    return HttpResponse.json({
      relationships: paginatedRelationships,
      total: filteredRelationships.length,
      limit,
      offset
    });
  }),

  // OPTIONS请求支持（CORS预检）
  http.options(`${baseUrl}/v1/graph/entities`, () => {
    return new HttpResponse(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }),

  http.options(`${baseUrl}/v1/graph/relationships`, () => {
    return new HttpResponse(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  })
]);
