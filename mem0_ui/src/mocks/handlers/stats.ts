import { http, HttpResponse } from 'msw';

import { mockStats, mockMemories, mockUsers } from '../data/mockData';

// 支持多个API base URL
const API_BASES = [
  'http://localhost:8765/api/v1',
  'http://localhost:8000/v1',
  process.env.NEXT_PUBLIC_API_URL ? process.env.NEXT_PUBLIC_API_URL + '/v1' : null,
  process.env.NEXT_PUBLIC_MEM0_API_URL
].filter((url): url is string => Boolean(url));

// 创建所有base URL的handlers
const createHandlersForBase = (baseUrl: string) => [
  // Get global statistics
  http.get(`${baseUrl}/v1/stats`, ({ request }) => {
    const url = new URL(request.url);
    const userId = url.searchParams.get('user_id');

    // 如果指定了user_id，返回用户特定的统计
    if (userId) {
      const userMemories = mockMemories.filter(m => m.user_id === userId);
      const activeMemories = userMemories.filter(m => m.state === 'active').length;
      const archivedMemories = userMemories.filter(m => m.state === 'archived').length;

      // 计算用户的应用统计
      const appStats = userMemories.reduce((acc, memory) => {
        const appName = memory.app_name || 'unknown';
        acc[appName] = (acc[appName] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const topApps = Object.entries(appStats)
        .map(([app_name, memory_count]) => ({ app_name, memory_count }))
        .sort((a, b) => b.memory_count - a.memory_count)
        .slice(0, 5);

      return HttpResponse.json({
        total_memories: userMemories.length,
        active_memories: activeMemories,
        archived_memories: archivedMemories,
        total_users: 1, // 当前用户
        memory_operations_today: Math.floor(Math.random() * 20) + 5,
        avg_response_time: Math.floor(Math.random() * 50) + 80,
        top_apps: topApps
      });
    }

    // 返回全局统计
    return HttpResponse.json(mockStats);
  }),

  // Get detailed analytics (扩展功能)
  http.get(`${baseUrl}/v1/stats/analytics`, ({ request }) => {
    const url = new URL(request.url);
    const period = url.searchParams.get('period') || '7d'; // 7d, 30d, 90d
    const userId = url.searchParams.get('user_id');

    // 生成时间序列数据
    const generateTimeSeriesData = (days: number) => {
      const data = [];
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        data.push({
          date: date.toISOString().split('T')[0],
          memories_created: Math.floor(Math.random() * 10) + 1,
          memories_updated: Math.floor(Math.random() * 5),
          memories_deleted: Math.floor(Math.random() * 2),
          avg_response_time: Math.floor(Math.random() * 50) + 80
        });
      }
      return data;
    };

    const days = period === '30d' ? 30 : period === '90d' ? 90 : 7;
    const timeSeriesData = generateTimeSeriesData(days);

    return HttpResponse.json({
      period,
      user_id: userId,
      time_series: timeSeriesData,
      summary: {
        total_operations: timeSeriesData.reduce((sum, day) =>
          sum + day.memories_created + day.memories_updated + day.memories_deleted, 0),
        avg_daily_operations: Math.round(
          timeSeriesData.reduce((sum, day) =>
            sum + day.memories_created + day.memories_updated + day.memories_deleted, 0) / days
        ),
        peak_day: timeSeriesData.reduce((peak, day) => {
          const dayTotal = day.memories_created + day.memories_updated + day.memories_deleted;
          const peakTotal = peak.memories_created + peak.memories_updated + peak.memories_deleted;
          return dayTotal > peakTotal ? day : peak;
        })
      }
    });
  }),

  // Health check endpoint
  http.get(`${baseUrl}/health`, () => {
    return HttpResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: Math.floor(Math.random() * 86400), // 随机运行时间（秒）
      memory_usage: {
        used: Math.floor(Math.random() * 500) + 100, // MB
        total: 1024
      }
    });
  })
];

// 为所有API base创建handlers
export const statsHandlers = API_BASES.flatMap(createHandlersForBase);
