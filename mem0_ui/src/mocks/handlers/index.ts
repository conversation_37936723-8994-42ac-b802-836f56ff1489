/**
 * MSW Handlers 索引文件
 * 基于真实Mem0 Server API格式
 */

import { realMemoriesHandlers } from './realMemories';
import { graphMemoryHandlers } from './graphMemory';
import { dashboardHandlers } from './dashboard';

// 导出所有handlers
export const handlers = [
  ...realMemoriesHandlers,
  ...graphMemoryHandlers,
  ...dashboardHandlers
];

// 导出单独的handler组
export { realMemoriesHandlers, graphMemoryHandlers, dashboardHandlers };

// 打印可用的API端点信息
export const printAvailableEndpoints = () => {
  console.log('🚀 MSW Mock API 已启动，可用端点：');
  console.log('');
  console.log('📋 记忆管理 API:');
  console.log('  GET    /health                     - 健康检查');
  console.log('  GET    /v1/memories/               - 获取记忆列表');
  console.log('  POST   /v1/memories/               - 创建记忆');
  console.log('  GET    /v1/memories/{id}/          - 获取单个记忆');
  console.log('  PUT    /v1/memories/{id}/          - 更新记忆');
  console.log('  DELETE /v1/memories/{id}/          - 删除记忆');
  console.log('  POST   /v1/memories/search/        - 搜索记忆');
  console.log('  DELETE /v1/memories/               - 删除所有记忆');
  console.log('  GET    /v1/memories/categories     - 获取记忆分类');
  console.log('  PUT    /v1/batch/                  - 批量更新记忆');
  console.log('  DELETE /v1/batch/                  - 批量删除记忆');
  console.log('  GET    /v1/stats                   - 获取统计信息');
  console.log('  POST   /reset                      - 重置所有数据');
  console.log('');
  console.log('🧠 图记忆管理 API:');
  console.log('  GET    /v1/graph/entities          - 获取图实体列表');
  console.log('  GET    /v1/graph/relationships     - 获取图关系列表');
  console.log('');
  console.log('📊 Dashboard API:');
  console.log('  GET    /v1/stats                   - 获取系统统计数据');
  console.log('  GET    /v1/activities              - 获取活动日志');
  console.log('  GET    /v1/admin/dashboard         - 获取管理面板数据');
  console.log('');
  console.log('🌐 支持的API基础URL:');
  console.log('  - http://localhost:8000');
  console.log('  - http://localhost:8765');
  console.log('  - NEXT_PUBLIC_API_URL (如果设置)');
  console.log('  - NEXT_PUBLIC_MEM0_API_URL (如果设置)');
  console.log('');
  console.log('📖 API格式基于: http://localhost:8000/openapi.json');
};
