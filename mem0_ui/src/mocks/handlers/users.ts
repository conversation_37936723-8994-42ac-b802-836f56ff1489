import { http, HttpResponse } from 'msw';

import { UserInfo } from '@/types/mem0-api';

import { mockUsers } from '../data/mockData';

// 支持多个API base URL
const API_BASES = [
  'http://localhost:8765/api/v1',
  'http://localhost:8000/v1',
  process.env.NEXT_PUBLIC_API_URL ? process.env.NEXT_PUBLIC_API_URL + '/v1' : null,
  process.env.NEXT_PUBLIC_MEM0_API_URL
].filter((url): url is string => Boolean(url));

// 创建所有base URL的handlers
const createHandlersForBase = (baseUrl: string) => [
  // Get user by ID
  http.get(`${baseUrl}/users/:id`, ({ params }) => {
    const { id } = params;
    const user = mockUsers.find(u => u.id === id);
    
    if (!user) {
      return HttpResponse.json(
        { error: 'User not found', message: `User with id ${id} not found` },
        { status: 404 }
      );
    }
    
    return HttpResponse.json(user);
  }),

  // Create user
  http.post(`${baseUrl}/users`, async ({ request }) => {
    const body = await request.json() as Partial<UserInfo>;
    
    if (!body.id) {
      return HttpResponse.json(
        { error: 'Missing user id', message: 'User id is required' },
        { status: 400 }
      );
    }

    // 检查用户是否已存在
    const existingUser = mockUsers.find(u => u.id === body.id);
    if (existingUser) {
      return HttpResponse.json(
        { error: 'User already exists', message: `User with id ${body.id} already exists` },
        { status: 409 }
      );
    }

    const newUser: UserInfo = {
      id: body.id,
      name: body.name || '',
      memory_count: 0,
      last_activity: new Date().toISOString()
    };
    
    // 模拟添加到数据中
    mockUsers.push(newUser);
    
    return HttpResponse.json(newUser, { status: 201 });
  }),

  // Update user
  http.put(`${baseUrl}/users/:id`, async ({ params, request }) => {
    const { id } = params;
    const body = await request.json() as Partial<UserInfo>;
    
    const userIndex = mockUsers.findIndex(u => u.id === id);
    
    if (userIndex === -1) {
      return HttpResponse.json(
        { error: 'User not found', message: `User with id ${id} not found` },
        { status: 404 }
      );
    }

    // 更新用户
    const updatedUser = {
      ...mockUsers[userIndex],
      ...(body.name && { name: body.name }),
      ...(body.email && { email: body.email }),
      ...(body.metadata && { metadata: { ...mockUsers[userIndex].metadata, ...body.metadata } })
    };
    
    mockUsers[userIndex] = updatedUser;
    
    return HttpResponse.json(updatedUser);
  }),

  // Delete user
  http.delete(`${baseUrl}/users/:id`, ({ params }) => {
    const { id } = params;
    const userIndex = mockUsers.findIndex(u => u.id === id);
    
    if (userIndex === -1) {
      return HttpResponse.json(
        { error: 'User not found', message: `User with id ${id} not found` },
        { status: 404 }
      );
    }

    // 删除用户
    mockUsers.splice(userIndex, 1);
    
    return HttpResponse.json({ message: 'User deleted successfully' });
  }),

  // List users (管理功能)
  http.get(`${baseUrl}/users`, ({ request }) => {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    
    const start = offset;
    const end = start + limit;
    const users = mockUsers.slice(start, end);
    
    return HttpResponse.json({
      users,
      total: mockUsers.length,
      limit,
      offset
    });
  })
];

// 为所有API base创建handlers
export const usersHandlers = API_BASES.flatMap(createHandlersForBase);
