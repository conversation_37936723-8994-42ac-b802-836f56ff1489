/**
 * Mock数据生成器
 * 基于实际Mem0 Server API格式的测试数据
 */

import { Mem0Memory, Message, Mem0User, Mem0Stats, Mem0Activity } from '@/types/mem0-api';

// 模拟用户数据
export const mockUsers: Mem0User[] = [
  {
    id: 'user_1',
    name: '<PERSON>',
    email: '<EMAIL>',
    created_at: '2024-01-15T10:30:00Z',
    metadata: { role: 'developer', team: 'frontend' }
  },
  {
    id: 'user_2', 
    name: '<PERSON>',
    email: '<EMAIL>',
    created_at: '2024-01-20T14:15:00Z',
    metadata: { role: 'designer', team: 'product' }
  },
  {
    id: 'user_3',
    name: '<PERSON>',
    email: '<EMAIL>', 
    created_at: '2024-02-01T09:45:00Z',
    metadata: { role: 'manager', team: 'engineering' }
  }
];

// 模拟记忆数据生成器
export const generateMockMemories = (count: number = 20): Mem0Memory[] => {
  const categories = ['personal', 'work', 'health', 'finance', 'travel', 'education', 'preferences', 'relationships'];
  const appNames = ['mem0-ui', 'chatgpt-plugin', 'slack-bot', 'notion-sync', 'chrome-extension'];
  const states: Array<'active' | 'paused' | 'archived' | 'deleted'> = ['active', 'active', 'active', 'paused', 'archived'];
  
  const memoryTexts = [
    'I prefer dark mode interfaces for better eye comfort during long coding sessions',
    'My favorite programming language is TypeScript because of its type safety',
    'I usually take breaks every 45 minutes when working to maintain focus',
    'I like to have my morning coffee at 9 AM sharp every day',
    'My preferred IDE is VS Code with the Material Theme Dark',
    'I work best in quiet environments with minimal distractions',
    'I prefer to review code in the morning when my mind is fresh',
    'My go-to debugging approach is to use console.log statements first',
    'I like to organize my files using a feature-based folder structure',
    'I prefer to write unit tests before implementing new features',
    'My favorite keyboard shortcuts are Ctrl+Shift+P for command palette',
    'I usually commit code with descriptive messages following conventional commits',
    'I prefer to use Git rebase instead of merge for cleaner history',
    'My preferred database is PostgreSQL for its reliability and features',
    'I like to use Docker for consistent development environments',
    'I prefer to deploy applications using CI/CD pipelines',
    'My favorite CSS framework is Tailwind CSS for its utility-first approach',
    'I like to use React hooks for state management in functional components',
    'I prefer to write documentation using Markdown format',
    'My preferred testing framework is Jest with React Testing Library'
  ];

  return Array.from({ length: count }, (_, index) => ({
    id: `mem_${String(index + 1).padStart(3, '0')}`,
    memory: memoryTexts[index % memoryTexts.length],
    text: memoryTexts[index % memoryTexts.length],
    user_id: mockUsers[index % mockUsers.length].id,
    created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    metadata: {
      source: ['api', 'web', 'mobile'][Math.floor(Math.random() * 3)],
      confidence: Math.random() * 0.3 + 0.7, // 0.7-1.0
      tags: categories.slice(0, Math.floor(Math.random() * 3) + 1)
    },
    categories: [categories[Math.floor(Math.random() * categories.length)]],
    app_name: appNames[Math.floor(Math.random() * appNames.length)],
    state: states[Math.floor(Math.random() * states.length)]
  }));
};

// 模拟统计数据
export const generateMockStats = (): Mem0Stats => {
  const totalMemories = 1247;
  const activeMemories = Math.floor(totalMemories * 0.85);
  const archivedMemories = totalMemories - activeMemories;
  
  return {
    total_memories: totalMemories,
    active_memories: activeMemories,
    archived_memories: archivedMemories,
    total_users: mockUsers.length,
    memory_operations_today: Math.floor(Math.random() * 50) + 20,
    avg_response_time: Math.floor(Math.random() * 100) + 80,
    top_apps: [
      { id: 'mem0-ui', name: 'mem0-ui', memory_count: 456 },
      { id: 'chatgpt-plugin', name: 'chatgpt-plugin', memory_count: 321 },
      { id: 'slack-bot', name: 'slack-bot', memory_count: 234 },
      { id: 'notion-sync', name: 'notion-sync', memory_count: 156 },
      { id: 'chrome-extension', name: 'chrome-extension', memory_count: 80 }
    ]
  };
};

// 模拟活动数据生成器
export const generateMockActivities = (count: number = 50): Mem0Activity[] => {
  const operations: Array<'create' | 'update' | 'delete' | 'search' | 'retrieve'> = 
    ['create', 'update', 'delete', 'search', 'retrieve'];
  const appNames = ['mem0-ui', 'chatgpt-plugin', 'slack-bot', 'notion-sync', 'chrome-extension'];
  
  return Array.from({ length: count }, (_, index) => ({
    id: `activity_${String(index + 1).padStart(3, '0')}`,
    operation: operations[Math.floor(Math.random() * operations.length)],
    memory_id: `mem_${String(Math.floor(Math.random() * 100) + 1).padStart(3, '0')}`,
    user_id: mockUsers[Math.floor(Math.random() * mockUsers.length)].id,
    app_name: appNames[Math.floor(Math.random() * appNames.length)],
    timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    response_time: Math.floor(Math.random() * 200) + 50,
    metadata: {
      ip_address: `192.168.1.${Math.floor(Math.random() * 255)}`,
      user_agent: 'Mozilla/5.0 (compatible; Mem0Client/1.0)',
      success: Math.random() > 0.1 // 90% success rate
    }
  })).sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
};

// 全局mock数据实例
export const mockMemories = generateMockMemories(50);
export const mockStats = generateMockStats();
export const mockActivities = generateMockActivities(100);

// 分页辅助函数
export const paginateData = <T>(data: T[], limit: number, offset: number) => {
  const start = offset;
  const end = start + limit;
  return {
    items: data.slice(start, end),
    total: data.length,
    limit,
    offset,
    hasMore: end < data.length
  };
};

// 搜索辅助函数
export const searchMemories = (query: string, memories: Mem0Memory[] = mockMemories) => {
  if (!query) return memories;
  
  const lowerQuery = query.toLowerCase();
  return memories.filter(memory =>
    (memory.text || memory.memory || '').toLowerCase().includes(lowerQuery) ||
    memory.categories?.some(cat => cat.toLowerCase().includes(lowerQuery)) ||
    memory.app_name?.toLowerCase().includes(lowerQuery)
  );
};

// 过滤辅助函数
export const filterMemories = (
  memories: Mem0Memory[],
  filters: {
    app_name?: string[];
    categories?: string[];
    state?: string[];
  }
) => {
  return memories.filter(memory => {
    if (filters.app_name && !filters.app_name.includes(memory.app_name || '')) {
      return false;
    }
    if (filters.categories && !memory.categories?.some(cat => filters.categories!.includes(cat))) {
      return false;
    }
    if (filters.state && !filters.state.includes(memory.state || 'active')) {
      return false;
    }
    return true;
  });
};
