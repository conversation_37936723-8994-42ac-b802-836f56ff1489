// ============================================================================
// 组件Props和状态类型定义
// ============================================================================

import { ReactNode } from 'react';

/**
 * 基础组件Props接口
 */
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
}

/**
 * 加载状态接口
 */
export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

/**
 * 分页组件Props
 */
export interface PaginationProps extends BaseComponentProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showSizeChanger?: boolean;
  pageSize?: number;
  onPageSizeChange?: (size: number) => void;
}

/**
 * 搜索组件Props
 */
export interface SearchProps extends BaseComponentProps {
  value: string;
  onChange: (value: string) => void;
  onSearch?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

/**
 * 筛选器组件Props
 */
export interface FilterProps extends BaseComponentProps {
  filters: Record<string, any>;
  onFiltersChange: (filters: Record<string, any>) => void;
  availableFilters?: FilterOption[];
}

/**
 * 筛选器选项接口
 */
export interface FilterOption {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'boolean';
  options?: Array<{ label: string; value: any }>;
}

/**
 * 表格列定义接口
 */
export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex?: keyof T;
  render?: (value: any, record: T, index: number) => ReactNode;
  sortable?: boolean;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
}

/**
 * 表格组件Props
 */
export interface TableProps<T = any> extends BaseComponentProps {
  columns: TableColumn<T>[];
  dataSource: T[];
  loading?: boolean;
  pagination?: PaginationProps | false;
  rowKey?: keyof T | ((record: T) => string);
  onRowClick?: (record: T, index: number) => void;
  selectedRowKeys?: string[];
  onSelectionChange?: (selectedKeys: string[], selectedRows: T[]) => void;
}

/**
 * 模态框组件Props
 */
export interface ModalProps extends BaseComponentProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  width?: number | string;
  footer?: ReactNode;
  closable?: boolean;
  maskClosable?: boolean;
}

/**
 * 表单字段接口
 */
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date' | 'number';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
  validation?: {
    min?: number;
    max?: number;
    pattern?: RegExp;
    message?: string;
  };
}

/**
 * 表单组件Props
 */
export interface FormProps extends BaseComponentProps {
  fields: FormField[];
  initialValues?: Record<string, any>;
  onSubmit: (values: Record<string, any>) => void;
  loading?: boolean;
  submitText?: string;
  resetText?: string;
}

/**
 * 卡片组件Props
 */
export interface CardProps extends BaseComponentProps {
  title?: string;
  extra?: ReactNode;
  bordered?: boolean;
  hoverable?: boolean;
  loading?: boolean;
  size?: 'small' | 'default' | 'large';
}

/**
 * 标签页组件Props
 */
export interface TabsProps extends BaseComponentProps {
  activeKey: string;
  onChange: (key: string) => void;
  items: TabItem[];
  type?: 'line' | 'card';
  size?: 'small' | 'default' | 'large';
}

/**
 * 标签页项接口
 */
export interface TabItem {
  key: string;
  label: string;
  children: ReactNode;
  disabled?: boolean;
  closable?: boolean;
}

/**
 * 通知组件Props
 */
export interface NotificationProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  onClose?: () => void;
}

/**
 * 确认对话框Props
 */
export interface ConfirmDialogProps extends BaseComponentProps {
  visible: boolean;
  title: string;
  content: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
  type?: 'info' | 'warning' | 'error';
}

/**
 * 图表组件基础Props
 */
export interface ChartProps extends BaseComponentProps {
  data: any[];
  width?: number;
  height?: number;
  loading?: boolean;
  title?: string;
}

/**
 * 统计卡片Props
 */
export interface StatCardProps extends BaseComponentProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  loading?: boolean;
}

/**
 * 面包屑导航Props
 */
export interface BreadcrumbProps extends BaseComponentProps {
  items: BreadcrumbItem[];
  separator?: string;
}

/**
 * 面包屑项接口
 */
export interface BreadcrumbItem {
  title: string;
  href?: string;
  onClick?: () => void;
}

/**
 * 侧边栏菜单Props
 */
export interface SidebarProps extends BaseComponentProps {
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
  menuItems: MenuItem[];
  selectedKey?: string;
  onMenuSelect: (key: string) => void;
}

/**
 * 菜单项接口
 */
export interface MenuItem {
  key: string;
  label: string;
  icon?: ReactNode;
  children?: MenuItem[];
  disabled?: boolean;
  href?: string;
}

/**
 * 头部组件Props
 */
export interface HeaderProps extends BaseComponentProps {
  title?: string;
  user?: {
    name: string;
    avatar?: string;
  };
  onUserMenuClick?: (key: string) => void;
  actions?: ReactNode;
}

/**
 * 布局组件Props
 */
export interface LayoutProps extends BaseComponentProps {
  sidebar?: ReactNode;
  header?: ReactNode;
  footer?: ReactNode;
  sidebarCollapsed?: boolean;
  onSidebarCollapse?: (collapsed: boolean) => void;
}
