// ============================================================================
// Redux状态和Action类型定义
// ============================================================================

import { AppInfo } from './api';

/**
 * 异步状态枚举
 */
export type AsyncStatus = 'idle' | 'loading' | 'succeeded' | 'failed';

/**
 * 基础异步状态接口
 */
export interface BaseAsyncState {
  status: AsyncStatus;
  error: string | null;
}

/**
 * 记忆相关状态接口
 */
export interface MemoriesState extends BaseAsyncState {
  memories: Memory[];
  selectedMemory: SimpleMemory | null;
  accessLogs: AccessLogEntry[];
  relatedMemories: Memory[];
  selectedMemoryIds: string[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
}

/**
 * 简化的记忆接口
 */
export interface SimpleMemory {
  id: string;
  memory: string;
  user_id?: string;
  agent_id?: string;
  run_id?: string;
  created_at?: string;
  metadata?: Record<string, any>;
}

/**
 * 完整的记忆接口
 */
export interface Memory extends SimpleMemory {
  categories: Category[];
  client: Client;
  app_name: string;
  state: 'active' | 'paused' | 'archived' | 'deleted';
  updated_at?: string;
  score?: number;
}

/**
 * 访问日志条目接口
 */
export interface AccessLogEntry {
  id: string;
  app_name: string;
  accessed_at: string;
  user_id?: string;
  memory_id?: string;
  operation?: string;
}

/**
 * 分类类型
 */
export type Category = 
  | "personal" 
  | "work" 
  | "health" 
  | "finance" 
  | "travel" 
  | "education" 
  | "preferences" 
  | "relationships";

/**
 * 客户端类型
 */
export type Client = 
  | "chrome" 
  | "chatgpt" 
  | "cursor" 
  | "windsurf" 
  | "terminal" 
  | "api";

/**
 * 用户配置状态接口
 */
export interface ProfileState extends BaseAsyncState {
  userId: string;
  viewMode: 'system' | 'user';
  availableUsers: string[];
  totalMemories: number;
  totalApps: number;
  apps: AppInfo[];
}



/**
 * 应用状态接口
 */
export interface AppsState extends BaseAsyncState {
  apps: AppInfo[];
  selectedApp: AppInfo | null;
  filters: AppFilters;
  sortBy: AppSortBy;
  sortOrder: 'asc' | 'desc';
  totalCount: number;
  currentPage: number;
  pageSize: number;
}

/**
 * 应用筛选器接口
 */
export interface AppFilters {
  search?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  minMemoryCount?: number;
  maxMemoryCount?: number;
}

/**
 * 应用排序字段
 */
export type AppSortBy = 'name' | 'memory_count' | 'last_activity' | 'created_at';

/**
 * UI状态接口
 */
export interface UIState {
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: Notification[];
  modals: ModalState[];
  loading: UILoadingState[];
}

/**
 * 通知接口
 */
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  timestamp: number;
}

/**
 * 模态框状态接口
 */
export interface ModalState {
  id: string;
  visible: boolean;
  title?: string;
  content?: any;
  props?: Record<string, any>;
}

/**
 * UI加载状态接口
 */
export interface UILoadingState {
  id: string;
  isLoading: boolean;
  message?: string;
}

/**
 * 筛选器状态接口
 */
export interface FiltersState {
  memories: MemoryFilters;
  apps: AppFilters;
  activities: ActivityFilters;
}

/**
 * 记忆筛选器接口
 */
export interface MemoryFilters {
  search?: string;
  categories?: Category[];
  clients?: Client[];
  apps?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  showArchived?: boolean;
  userId?: string;
  agentId?: string;
  runId?: string;
}

/**
 * 活动筛选器接口
 */
export interface ActivityFilters {
  userId?: string;
  agentId?: string;
  runId?: string;
  operation?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

/**
 * 配置状态接口
 */
export interface ConfigState extends BaseAsyncState {
  apiEndpoint: string;
  apiKey?: string;
  defaultUserId: string;
  enableGraph: boolean;
  pageSize: number;
  theme: 'light' | 'dark' | 'system';
  language: string;
  autoRefresh: boolean;
  refreshInterval: number;
}

/**
 * 图形记忆状态接口
 */
export interface GraphMemoryState extends BaseAsyncState {
  nodes: GraphNode[];
  edges: GraphEdge[];
  selectedNodeId?: string;
  selectedEdgeId?: string;
  filters: GraphFilters;
  layout: GraphLayout;
  viewport: GraphViewport;
}

/**
 * 图形节点接口
 */
export interface GraphNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    label: string;
    memory?: Memory;
    entity?: any;
    [key: string]: any;
  };
  style?: Record<string, any>;
}

/**
 * 图形边接口
 */
export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
  data?: {
    label?: string;
    relationship?: any;
    [key: string]: any;
  };
  style?: Record<string, any>;
}

/**
 * 图形筛选器接口
 */
export interface GraphFilters {
  nodeTypes?: string[];
  edgeTypes?: string[];
  search?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}

/**
 * 图形布局类型
 */
export type GraphLayout = 'force' | 'hierarchical' | 'circular' | 'grid';

/**
 * 图形视口接口
 */
export interface GraphViewport {
  x: number;
  y: number;
  zoom: number;
}

/**
 * 根状态接口
 */
export interface RootState {
  memories: MemoriesState;
  profile: ProfileState;
  apps: AppsState;
  ui: UIState;
  filters: FiltersState;
  config: ConfigState;
  graphMemory: GraphMemoryState;
}

/**
 * 通用Action接口
 */
export interface BaseAction<T = any> {
  type: string;
  payload?: T;
}

/**
 * 异步Action状态
 */
export interface AsyncAction<T = any> extends BaseAction<T> {
  meta?: {
    requestId: string;
    requestStatus: 'pending' | 'fulfilled' | 'rejected';
  };
}
