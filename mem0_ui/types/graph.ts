// ============================================================================
// 图形可视化相关类型定义
// ============================================================================

import { Memory } from './redux';

// React Flow 基础类型定义（避免直接依赖reactflow包）
interface ReactFlowNode {
  id: string;
  type?: string;
  position: { x: number; y: number };
  data: any;
  style?: Record<string, any>;
}

interface ReactFlowEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
  data?: any;
  style?: Record<string, any>;
}

interface ReactFlowViewport {
  x: number;
  y: number;
  zoom: number;
}

/**
 * 图形记忆实体接口
 */
export interface GraphEntity {
  id: string;
  name: string;
  type: string;
  description?: string;
  properties?: Record<string, any>;
  metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

/**
 * 图形记忆关系接口
 */
export interface GraphRelationship {
  id: string;
  source_id: string;
  target_id: string;
  type: string;
  description?: string;
  properties?: Record<string, any>;
  metadata?: Record<string, any>;
  strength?: number;
  created_at?: string;
  updated_at?: string;
}

/**
 * 图形记忆API响应接口
 */
export interface GraphMemoryResponse {
  entities: GraphEntity[];
  relationships: GraphRelationship[];
  metadata?: {
    total_entities: number;
    total_relationships: number;
    query_time: number;
  };
}

/**
 * React Flow 节点数据接口
 */
export interface GraphNodeData {
  label: string;
  type: string;
  entity?: GraphEntity;
  memory?: Memory;
  isSelected?: boolean;
  isHighlighted?: boolean;
  color?: string;
  size?: number;
  [key: string]: any;
}

/**
 * React Flow 边数据接口
 */
export interface GraphEdgeData {
  label?: string;
  type: string;
  relationship?: GraphRelationship;
  isSelected?: boolean;
  isHighlighted?: boolean;
  color?: string;
  width?: number;
  [key: string]: any;
}

/**
 * 扩展的React Flow节点接口
 */
export interface GraphNode extends ReactFlowNode {
  data: GraphNodeData;
}

/**
 * 扩展的React Flow边接口
 */
export interface GraphEdge extends ReactFlowEdge {
  data: GraphEdgeData;
}

/**
 * 图形布局配置接口
 */
export interface GraphLayoutConfig {
  type: GraphLayoutType;
  options?: {
    // Force layout options
    strength?: number;
    distance?: number;
    iterations?: number;
    
    // Hierarchical layout options
    direction?: 'TB' | 'BT' | 'LR' | 'RL';
    levelSeparation?: number;
    nodeSeparation?: number;
    
    // Circular layout options
    radius?: number;
    startAngle?: number;
    
    // Grid layout options
    rows?: number;
    columns?: number;
    spacing?: number;
  };
}

/**
 * 图形布局类型
 */
export type GraphLayoutType = 'force' | 'hierarchical' | 'circular' | 'grid' | 'manual';

/**
 * 图形视图配置接口
 */
export interface GraphViewConfig {
  showMinimap?: boolean;
  showControls?: boolean;
  showBackground?: boolean;
  snapToGrid?: boolean;
  gridSize?: number;
  panOnDrag?: boolean;
  zoomOnScroll?: boolean;
  zoomOnPinch?: boolean;
  minZoom?: number;
  maxZoom?: number;
}

/**
 * 图形筛选配置接口
 */
export interface GraphFilterConfig {
  nodeTypes: Array<{
    type: string;
    label: string;
    color: string;
    enabled: boolean;
  }>;
  edgeTypes: Array<{
    type: string;
    label: string;
    color: string;
    enabled: boolean;
  }>;
  searchQuery?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  strengthRange?: {
    min: number;
    max: number;
  };
}

/**
 * 图形样式配置接口
 */
export interface GraphStyleConfig {
  nodes: {
    default: NodeStyle;
    selected: NodeStyle;
    highlighted: NodeStyle;
    [nodeType: string]: NodeStyle;
  };
  edges: {
    default: EdgeStyle;
    selected: EdgeStyle;
    highlighted: EdgeStyle;
    [edgeType: string]: EdgeStyle;
  };
  background: {
    color: string;
    pattern?: 'dots' | 'lines' | 'cross';
    patternColor?: string;
  };
}

/**
 * 节点样式接口
 */
export interface NodeStyle {
  backgroundColor: string;
  borderColor: string;
  borderWidth: number;
  borderRadius: number;
  color: string;
  fontSize: number;
  fontWeight: string;
  width: number;
  height: number;
  padding: number;
  shadow?: {
    color: string;
    blur: number;
    offsetX: number;
    offsetY: number;
  };
}

/**
 * 边样式接口
 */
export interface EdgeStyle {
  stroke: string;
  strokeWidth: number;
  strokeDasharray?: string;
  markerEnd?: {
    type: string;
    color: string;
    size: number;
  };
  labelBgColor?: string;
  labelColor?: string;
  labelFontSize?: number;
}

/**
 * 图形交互事件接口
 */
export interface GraphInteractionEvents {
  onNodeClick?: (event: React.MouseEvent, node: GraphNode) => void;
  onNodeDoubleClick?: (event: React.MouseEvent, node: GraphNode) => void;
  onNodeContextMenu?: (event: React.MouseEvent, node: GraphNode) => void;
  onNodeMouseEnter?: (event: React.MouseEvent, node: GraphNode) => void;
  onNodeMouseLeave?: (event: React.MouseEvent, node: GraphNode) => void;
  onNodeDragStart?: (event: React.MouseEvent, node: GraphNode) => void;
  onNodeDrag?: (event: React.MouseEvent, node: GraphNode) => void;
  onNodeDragStop?: (event: React.MouseEvent, node: GraphNode) => void;
  
  onEdgeClick?: (event: React.MouseEvent, edge: GraphEdge) => void;
  onEdgeDoubleClick?: (event: React.MouseEvent, edge: GraphEdge) => void;
  onEdgeContextMenu?: (event: React.MouseEvent, edge: GraphEdge) => void;
  onEdgeMouseEnter?: (event: React.MouseEvent, edge: GraphEdge) => void;
  onEdgeMouseLeave?: (event: React.MouseEvent, edge: GraphEdge) => void;
  
  onPaneClick?: (event: React.MouseEvent) => void;
  onPaneContextMenu?: (event: React.MouseEvent) => void;
  onSelectionChange?: (elements: Array<GraphNode | GraphEdge>) => void;
  onViewportChange?: (viewport: ReactFlowViewport) => void;
}

/**
 * 图形工具栏配置接口
 */
export interface GraphToolbarConfig {
  showZoomControls?: boolean;
  showLayoutControls?: boolean;
  showFilterControls?: boolean;
  showExportControls?: boolean;
  showFullscreenControl?: boolean;
  customActions?: Array<{
    id: string;
    label: string;
    icon: string;
    onClick: () => void;
  }>;
}

/**
 * 图形导出配置接口
 */
export interface GraphExportConfig {
  format: 'png' | 'jpg' | 'svg' | 'pdf';
  quality?: number;
  width?: number;
  height?: number;
  backgroundColor?: string;
  includeBackground?: boolean;
}

/**
 * 图形搜索结果接口
 */
export interface GraphSearchResult {
  nodes: GraphNode[];
  edges: GraphEdge[];
  query: string;
  totalResults: number;
  searchTime: number;
}

/**
 * 图形分析结果接口
 */
export interface GraphAnalysisResult {
  metrics: {
    nodeCount: number;
    edgeCount: number;
    density: number;
    averageDegree: number;
    clusteringCoefficient: number;
    diameter: number;
  };
  communities?: Array<{
    id: string;
    nodes: string[];
    size: number;
    modularity: number;
  }>;
  centralNodes?: Array<{
    nodeId: string;
    betweennessCentrality: number;
    closenessCentrality: number;
    degreeCentrality: number;
  }>;
}

/**
 * 图形历史记录接口
 */
export interface GraphHistoryState {
  nodes: GraphNode[];
  edges: GraphEdge[];
  viewport: ReactFlowViewport;
  timestamp: number;
  description?: string;
}

/**
 * 图形上下文菜单项接口
 */
export interface GraphContextMenuItem {
  id: string;
  label: string;
  icon?: string;
  disabled?: boolean;
  separator?: boolean;
  children?: GraphContextMenuItem[];
  onClick?: (target: GraphNode | GraphEdge | null) => void;
}
