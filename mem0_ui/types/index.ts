// ============================================================================
// 统一类型定义导出
// ============================================================================

// 明确导出以避免命名冲突
// API 相关类型
export type {
  APIResponse,
  PaginationParams,
  GetMemoriesParams,
  MemoriesResponse,
  Mem0Memory,
  GetActivitiesParams,
  StatsResponse,
  ErrorResponse,
  ActivityItem,
  ActivitiesResponse,
  UserInfo,
  AppInfo,
  CategoryInfo,
  CreateMemoryRequest,
  UpdateMemoryRequest,
  SearchMemoryRequest,
  BatchOperationRequest,
  BatchOperationResponse,
  UserDetailedStats,
} from './api';

// 组件相关类型
export type {
  BaseComponentProps,
  LoadingState,
  PaginationProps,
  SearchProps,
  FilterProps,
  FilterOption,
  TableColumn,
  TableProps,
  ModalProps,
  FormField,
  FormProps,
  CardProps,
  TabsProps,
  TabItem,
  NotificationProps,
  ConfirmDialogProps,
  ChartProps,
  StatCardProps,
  BreadcrumbProps,
  BreadcrumbItem,
  SidebarProps,
  MenuItem,
  HeaderProps,
  LayoutProps,
} from './components';

// Redux 相关类型
export type {
  AsyncStatus,
  BaseAsyncState,
  MemoriesState,
  SimpleMemory,
  Memory,
  AccessLogEntry,
  Category,
  Client,
  ProfileState,
  AppsState,
  AppFilters,
  AppSortBy,
  UIState,
  Notification,
  ModalState,
  UILoadingState,
  FiltersState,
  MemoryFilters,
  ActivityFilters,
  ConfigState,
  GraphMemoryState,
  GraphNode,
  GraphEdge,
  GraphFilters,
  GraphLayout,
  GraphViewport,
  RootState,
  BaseAction,
  AsyncAction,
} from './redux';

// 图形相关类型
export type {
  GraphEntity,
  GraphRelationship,
  GraphMemoryResponse,
  GraphNodeData,
  GraphEdgeData,
  GraphLayoutConfig,
  GraphLayoutType,
  GraphViewConfig,
  GraphFilterConfig,
  GraphStyleConfig,
  NodeStyle,
  EdgeStyle,
  GraphInteractionEvents,
  GraphToolbarConfig,
  GraphExportConfig,
  GraphSearchResult,
  GraphAnalysisResult,
  GraphHistoryState,
  GraphContextMenuItem,
} from './graph';

// 内存相关类型（重命名以避免冲突）
export type {
  MemoryOperation,
  MemoryState as MemoryStateType,
  MemoryPriority,
  MemoryVisibility,
  CreateMemoryRequest as DetailedCreateMemoryRequest,
  UpdateMemoryRequest as DetailedUpdateMemoryRequest,
  SearchMemoryRequest as DetailedSearchMemoryRequest,
  MemorySearchFilters,
  MemorySearchSort,
  MemorySearchResult,
  BatchMemoryRequest,
  BatchMemoryResult,
  MemoryStats,
  MemoryHistory,
  MemoryVersion,
  MemoryAssociation,
  MemoryTag,
  MemoryCategory,
  MemoryTemplate,
  ImportMemoryRequest,
  ExportMemoryRequest,
  MemoryImportExportResult,
  MemoryBackup,
  RestoreMemoryRequest,
} from './memory';



// 工具类型定义
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type Nullable<T> = T | null;
export type Maybe<T> = T | undefined;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

// 事件处理器类型
export type EventHandler<T = any> = (event: T) => void;
export type AsyncEventHandler<T = any> = (event: T) => Promise<void>;
export type ChangeHandler<T = any> = (value: T) => void;
export type AsyncChangeHandler<T = any> = (value: T) => Promise<void>;

// 通用回调类型
export type Callback<T = void> = () => T;
export type AsyncCallback<T = void> = () => Promise<T>;
export type CallbackWithParam<P, T = void> = (param: P) => T;
export type AsyncCallbackWithParam<P, T = void> = (param: P) => Promise<T>;

// 数据获取相关类型
export interface DataFetchOptions {
  page?: number;
  pageSize?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  filters?: Record<string, any>;
  search?: string;
}

export interface DataFetchResult<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

// 表单相关类型
export interface FormValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  validator?: (value: any) => boolean | string;
  message?: string;
}

export interface FormFieldConfig {
  name: string;
  label: string;
  type: string;
  rules?: FormValidationRule[];
  options?: Array<{ label: string; value: any }>;
  placeholder?: string;
  disabled?: boolean;
  hidden?: boolean;
}

export interface FormState {
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}

// 主题相关类型
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'system';
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  borderRadius: number;
  fontSize: {
    small: string;
    medium: string;
    large: string;
  };
  spacing: {
    small: string;
    medium: string;
    large: string;
  };
}

// 国际化相关类型
export interface I18nConfig {
  locale: string;
  fallbackLocale: string;
  messages: Record<string, Record<string, string>>;
}

// 路由相关类型
export interface RouteConfig {
  path: string;
  component: React.ComponentType<any>;
  exact?: boolean;
  title?: string;
  meta?: Record<string, any>;
  children?: RouteConfig[];
  guards?: Array<(route: RouteConfig) => boolean | Promise<boolean>>;
}

// 权限相关类型
export interface Permission {
  id: string;
  name: string;
  description?: string;
  resource: string;
  action: string;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
}

export interface User {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  roles: Role[];
  permissions: Permission[];
  metadata?: Record<string, any>;
}

// 配置相关类型
export interface AppConfig {
  apiEndpoint: string;
  apiKey?: string;
  theme: ThemeConfig;
  i18n: I18nConfig;
  features: Record<string, boolean>;
  limits: {
    maxMemories: number;
    maxFileSize: number;
    maxRequestSize: number;
  };
  cache: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
}

// 错误处理相关类型
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
  stack?: string;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: AppError;
}

// 性能监控相关类型
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  apiResponseTime: number;
  errorRate: number;
}

// WebSocket 相关类型
export interface WebSocketMessage<T = any> {
  type: string;
  payload: T;
  timestamp: number;
  id?: string;
}

export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

// 文件上传相关类型
export interface FileUploadConfig {
  maxSize: number;
  allowedTypes: string[];
  multiple?: boolean;
  directory?: boolean;
}

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: string;
  metadata?: Record<string, any>;
}
