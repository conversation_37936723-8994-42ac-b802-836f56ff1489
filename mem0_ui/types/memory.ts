// ============================================================================
// 内存相关类型定义
// ============================================================================

/**
 * 记忆操作类型
 */
export type MemoryOperation = 'create' | 'update' | 'delete' | 'search' | 'get' | 'archive' | 'restore';

/**
 * 记忆状态类型
 */
export type MemoryState = 'active' | 'paused' | 'archived' | 'deleted';

/**
 * 记忆优先级类型
 */
export type MemoryPriority = 'low' | 'normal' | 'high' | 'critical';

/**
 * 记忆可见性类型
 */
export type MemoryVisibility = 'private' | 'shared' | 'public';

/**
 * 记忆创建请求接口
 */
export interface CreateMemoryRequest {
  memory: string;
  user_id?: string;
  agent_id?: string;
  run_id?: string;
  metadata?: Record<string, any>;
  categories?: string[];
  priority?: MemoryPriority;
  visibility?: MemoryVisibility;
  tags?: string[];
  expires_at?: string;
}

/**
 * 记忆更新请求接口
 */
export interface UpdateMemoryRequest {
  memory?: string;
  metadata?: Record<string, any>;
  categories?: string[];
  priority?: MemoryPriority;
  visibility?: MemoryVisibility;
  tags?: string[];
  state?: MemoryState;
  expires_at?: string;
}

/**
 * 记忆搜索请求接口
 */
export interface SearchMemoryRequest {
  query: string;
  user_id?: string;
  agent_id?: string;
  run_id?: string;
  limit?: number;
  offset?: number;
  filters?: MemorySearchFilters;
  sort?: MemorySearchSort;
  include_archived?: boolean;
  include_deleted?: boolean;
}

/**
 * 记忆搜索筛选器接口
 */
export interface MemorySearchFilters {
  categories?: string[];
  tags?: string[];
  priority?: MemoryPriority[];
  visibility?: MemoryVisibility[];
  state?: MemoryState[];
  created_after?: string;
  created_before?: string;
  updated_after?: string;
  updated_before?: string;
  expires_after?: string;
  expires_before?: string;
  has_metadata?: boolean;
  metadata_keys?: string[];
  score_min?: number;
  score_max?: number;
}

/**
 * 记忆搜索排序接口
 */
export interface MemorySearchSort {
  field: 'created_at' | 'updated_at' | 'score' | 'priority' | 'memory';
  order: 'asc' | 'desc';
}

/**
 * 记忆搜索结果接口
 */
export interface MemorySearchResult {
  id: string;
  memory: string;
  score: number;
  user_id?: string;
  agent_id?: string;
  run_id?: string;
  metadata?: Record<string, any>;
  categories?: string[];
  tags?: string[];
  priority: MemoryPriority;
  visibility: MemoryVisibility;
  state: MemoryState;
  created_at: string;
  updated_at: string;
  expires_at?: string;
  highlights?: string[];
}

/**
 * 记忆批量操作请求接口
 */
export interface BatchMemoryRequest {
  operation: MemoryOperation;
  memory_ids: string[];
  data?: UpdateMemoryRequest;
}

/**
 * 记忆批量操作结果接口
 */
export interface BatchMemoryResult {
  success_count: number;
  failed_count: number;
  results: Array<{
    memory_id: string;
    success: boolean;
    error?: string;
  }>;
}

/**
 * 记忆统计信息接口
 */
export interface MemoryStats {
  total_memories: number;
  active_memories: number;
  archived_memories: number;
  deleted_memories: number;
  memories_by_priority: Record<MemoryPriority, number>;
  memories_by_visibility: Record<MemoryVisibility, number>;
  memories_by_category: Record<string, number>;
  memories_by_tag: Record<string, number>;
  memories_by_user: Record<string, number>;
  memories_by_agent: Record<string, number>;
  memories_created_today: number;
  memories_updated_today: number;
  average_score: number;
  total_size_bytes: number;
}

/**
 * 记忆历史记录接口
 */
export interface MemoryHistory {
  id: string;
  memory_id: string;
  operation: MemoryOperation;
  timestamp: string;
  user_id?: string;
  agent_id?: string;
  run_id?: string;
  changes?: {
    field: string;
    old_value: any;
    new_value: any;
  }[];
  metadata?: Record<string, any>;
}

/**
 * 记忆版本接口
 */
export interface MemoryVersion {
  version: number;
  memory: string;
  metadata?: Record<string, any>;
  categories?: string[];
  tags?: string[];
  priority: MemoryPriority;
  visibility: MemoryVisibility;
  state: MemoryState;
  created_at: string;
  created_by?: string;
  comment?: string;
}

/**
 * 记忆关联接口
 */
export interface MemoryAssociation {
  id: string;
  source_memory_id: string;
  target_memory_id: string;
  association_type: 'similar' | 'related' | 'contradicts' | 'supports' | 'custom';
  strength: number;
  metadata?: Record<string, any>;
  created_at: string;
  created_by?: string;
}

/**
 * 记忆标签接口
 */
export interface MemoryTag {
  id: string;
  name: string;
  color?: string;
  description?: string;
  usage_count: number;
  created_at: string;
  created_by?: string;
}

/**
 * 记忆分类接口
 */
export interface MemoryCategory {
  id: string;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  parent_id?: string;
  usage_count: number;
  created_at: string;
  created_by?: string;
}

/**
 * 记忆模板接口
 */
export interface MemoryTemplate {
  id: string;
  name: string;
  description?: string;
  template: string;
  variables: Array<{
    name: string;
    type: 'string' | 'number' | 'boolean' | 'date';
    required: boolean;
    default_value?: any;
    description?: string;
  }>;
  categories?: string[];
  tags?: string[];
  priority: MemoryPriority;
  visibility: MemoryVisibility;
  usage_count: number;
  created_at: string;
  created_by?: string;
}

/**
 * 记忆导入请求接口
 */
export interface ImportMemoryRequest {
  format: 'json' | 'csv' | 'txt';
  data: string | File;
  options?: {
    skip_duplicates?: boolean;
    update_existing?: boolean;
    default_user_id?: string;
    default_agent_id?: string;
    default_categories?: string[];
    default_tags?: string[];
    default_priority?: MemoryPriority;
    default_visibility?: MemoryVisibility;
  };
}

/**
 * 记忆导出请求接口
 */
export interface ExportMemoryRequest {
  format: 'json' | 'csv' | 'txt';
  filters?: MemorySearchFilters;
  include_metadata?: boolean;
  include_history?: boolean;
  include_associations?: boolean;
}

/**
 * 记忆导入/导出结果接口
 */
export interface MemoryImportExportResult {
  success: boolean;
  total_processed: number;
  success_count: number;
  failed_count: number;
  errors?: Array<{
    line?: number;
    memory_id?: string;
    error: string;
  }>;
  file_url?: string;
  file_size?: number;
}

/**
 * 记忆备份接口
 */
export interface MemoryBackup {
  id: string;
  name: string;
  description?: string;
  backup_type: 'full' | 'incremental' | 'selective';
  filters?: MemorySearchFilters;
  file_url: string;
  file_size: number;
  memory_count: number;
  created_at: string;
  created_by?: string;
  expires_at?: string;
}

/**
 * 记忆恢复请求接口
 */
export interface RestoreMemoryRequest {
  backup_id: string;
  options?: {
    skip_existing?: boolean;
    update_existing?: boolean;
    restore_associations?: boolean;
    restore_history?: boolean;
  };
}
