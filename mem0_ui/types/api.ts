// ============================================================================
// API 类型定义
// ============================================================================

/**
 * 通用API响应格式
 */
export interface APIResponse<T> {
  data: T;
  status: 'success' | 'error';
  message?: string;
}

/**
 * 分页参数接口
 */
export interface PaginationParams {
  limit?: number;
  offset?: number;
}

/**
 * 获取记忆的参数接口
 */
export interface GetMemoriesParams extends PaginationParams {
  user_id?: string;
  agent_id?: string;
  run_id?: string;
  enable_graph?: boolean;
  output_format?: string;
}

/**
 * 记忆响应格式
 */
export interface MemoriesResponse {
  memories: Mem0Memory[];
  total: number;
  limit: number;
  offset: number;
}

/**
 * 基础记忆项接口
 */
export interface Mem0Memory {
  id: string;
  memory?: string;
  text?: string;
  user_id?: string;
  agent_id?: string;
  run_id?: string;
  created_at?: string;
  updated_at?: string;
  metadata?: Record<string, any>;
}

/**
 * 活动时间线参数接口
 */
export interface GetActivitiesParams extends PaginationParams {
  user_id?: string;
  agent_id?: string;
  run_id?: string;
}

/**
 * 统计信息响应接口
 */
export interface StatsResponse {
  total_memories: number;
  total_users: number;
  total_agents: number;
  recent_activities: number;
}

/**
 * 错误响应接口
 */
export interface ErrorResponse {
  error: string;
  message?: string;
  details?: Record<string, any>;
}

/**
 * 活动时间线项接口
 */
export interface ActivityItem {
  id: string;
  timestamp: string;
  operation: 'SEARCH' | 'ADD' | 'UPDATE' | 'DELETE' | 'GRAPH_CREATE';
  details: string;
  response_time?: string;
  status: 'success' | 'error' | 'pending';
  user_id?: string;
  memory_id?: string;
  metadata?: Record<string, any>;
}

/**
 * 活动时间线响应接口
 */
export interface ActivitiesResponse {
  activities: ActivityItem[];
  total: number;
  has_more: boolean;
  time_range: {
    start: string;
    end: string;
  };
}

/**
 * 用户信息接口
 */
export interface UserInfo {
  id: string;
  name?: string;
  email?: string;
  memory_count: number;
  last_activity?: string;
  created_at?: string;
  metadata?: Record<string, unknown>;
}

/**
 * 应用信息接口
 */
export interface AppInfo {
  id: string;
  name: string;
  user_id?: string;
  memory_count: number;
  last_activity?: string;
  created_at?: string;
  metadata?: Record<string, any>;
}

/**
 * 分类信息接口
 */
export interface CategoryInfo {
  id: string;
  name: string;
  description?: string;
  color?: string;
  usage_count: number;
  created_at?: string;
}

/**
 * 创建记忆请求接口
 */
export interface CreateMemoryRequest {
  memory: string;
  user_id?: string;
  agent_id?: string;
  run_id?: string;
  metadata?: Record<string, any>;
  categories?: string[];
}

/**
 * 更新记忆请求接口
 */
export interface UpdateMemoryRequest {
  memory?: string;
  metadata?: Record<string, any>;
  categories?: string[];
}

/**
 * 搜索记忆请求接口
 */
export interface SearchMemoryRequest {
  query: string;
  user_id?: string;
  agent_id?: string;
  run_id?: string;
  limit?: number;
  offset?: number;
  filters?: Record<string, any>;
}

/**
 * 批量操作请求接口
 */
export interface BatchOperationRequest {
  operation: 'delete' | 'archive' | 'restore' | 'update';
  memory_ids: string[];
  data?: UpdateMemoryRequest;
}

/**
 * 批量操作响应接口
 */
export interface BatchOperationResponse {
  success_count: number;
  failed_count: number;
  results: Array<{
    memory_id: string;
    success: boolean;
    error?: string;
  }>;
}

/**
 * 用户详细统计信息接口
 */
export interface UserDetailedStats extends StatsResponse {
  category_distribution: Record<string, number>;
  time_distribution: Record<string, number>;
  recent_activity: ActivityItem[];
  total_activities: number;
}
