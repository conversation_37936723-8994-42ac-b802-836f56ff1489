'use client';

import React, { useState } from 'react';
import { Users, Download, Trash2, Bar<PERSON>hart3, TrendingUp, User, Search, Filter } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { useUserManagement, UserInfo, UserAnalytics } from '@/hooks/useUserManagement';

interface UserDetailModalProps {
  user: UserInfo | null;
  isOpen: boolean;
  onClose: () => void;
  analytics: UserAnalytics | null;
  onExport: (userId: string) => void;
  onDelete: (userId: string) => void;
}

const UserDetailModal: React.FC<UserDetailModalProps> = ({
  user,
  isOpen,
  onClose,
  analytics,
  onExport,
  onDelete
}) => {
  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-zinc-900 border-zinc-800 max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <User className="w-5 h-5 text-[#00d4aa]" />
            用户详情 - {user.name || user.id}
          </DialogTitle>
          <DialogDescription className="text-zinc-400">
            查看用户的详细信息和活动统计
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-zinc-400">用户ID</label>
              <p className="text-white">{user.id}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-zinc-400">显示名称</label>
              <p className="text-white">{user.name || '未设置'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-zinc-400">记忆数量</label>
              <p className="text-white">{user.memory_count}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-zinc-400">最后活跃</label>
              <p className="text-white">
                {user.last_active ? new Date(user.last_active).toLocaleString() : '未知'}
              </p>
            </div>
          </div>

          {/* 分析数据 */}
          {analytics && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-[#00d4aa]" />
                活动分析
              </h3>
              
              <div className="grid grid-cols-2 gap-4">
                <Card className="bg-zinc-800 border-zinc-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm text-zinc-400">增长趋势</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2">
                      <TrendingUp className={`w-4 h-4 ${
                        analytics.memory_growth_trend === 'increasing' ? 'text-green-500' :
                        analytics.memory_growth_trend === 'decreasing' ? 'text-red-500' :
                        'text-yellow-500'
                      }`} />
                      <span className="text-white capitalize">
                        {analytics.memory_growth_trend === 'increasing' ? '上升' :
                         analytics.memory_growth_trend === 'decreasing' ? '下降' : '稳定'}
                      </span>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="bg-zinc-800 border-zinc-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm text-zinc-400">分类统计</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-1">
                      {Object.entries(analytics.memories_by_category).slice(0, 3).map(([category, count]) => (
                        <div key={category} className="flex justify-between text-sm">
                          <span className="text-zinc-300">{category}</span>
                          <span className="text-white">{count}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <h4 className="text-sm font-medium text-zinc-400 mb-2">最活跃日期</h4>
                <div className="flex flex-wrap gap-2">
                  {analytics.most_active_days.slice(0, 5).map((date) => (
                    <Badge key={date} variant="secondary" className="bg-zinc-700 text-zinc-300">
                      {new Date(date).toLocaleDateString()}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => onExport(user.id)}
            className="border-zinc-700 hover:border-zinc-600"
          >
            <Download className="w-4 h-4 mr-2" />
            导出数据
          </Button>
          {user.id !== 'default' && (
            <Button
              variant="destructive"
              onClick={() => onDelete(user.id)}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              删除用户
            </Button>
          )}
          <Button
            variant="outline"
            onClick={onClose}
            className="border-zinc-700"
          >
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default function UsersPage() {
  const { toast } = useToast();
  const {
    users,
    isLoading,
    error,
    fetchUsers,
    getUserAnalytics,
    exportUserData,
    deleteUser,
    batchUserOperations
  } = useUserManagement();

  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'memory_count' | 'last_active'>('memory_count');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserInfo | null>(null);
  const [userAnalytics, setUserAnalytics] = useState<UserAnalytics | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showBatchDialog, setShowBatchDialog] = useState(false);

  // 过滤和排序用户
  const filteredUsers = users
    .filter(user => 
      user.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.name && user.name.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return (a.name || a.id).localeCompare(b.name || b.id);
        case 'memory_count':
          return b.memory_count - a.memory_count;
        case 'last_active':
          return new Date(b.last_active || 0).getTime() - new Date(a.last_active || 0).getTime();
        default:
          return 0;
      }
    });

  const handleUserClick = async (user: UserInfo) => {
    setSelectedUser(user);
    setShowDetailModal(true);
    
    try {
      const analytics = await getUserAnalytics(user.id);
      setUserAnalytics(analytics);
    } catch (err) {
      toast({
        title: "错误",
        description: "获取用户分析数据失败",
        variant: "destructive"
      });
    }
  };

  const handleExportUser = async (userId: string) => {
    try {
      const blob = await exportUserData(userId);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `user_${userId}_export_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "成功",
        description: "用户数据导出成功",
        variant: "default"
      });
    } catch (err) {
      toast({
        title: "错误",
        description: "导出用户数据失败",
        variant: "destructive"
      });
    }
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      await deleteUser(userId);
      setShowDetailModal(false);
      toast({
        title: "成功",
        description: "用户删除成功",
        variant: "default"
      });
    } catch (err) {
      toast({
        title: "错误",
        description: "删除用户失败",
        variant: "destructive"
      });
    }
  };

  const handleBatchOperation = async (operation: 'delete' | 'export') => {
    if (selectedUsers.length === 0) {
      toast({
        title: "提示",
        description: "请选择要操作的用户",
        variant: "default"
      });
      return;
    }

    try {
      if (operation === 'export') {
        // 批量导出
        for (const userId of selectedUsers) {
          await handleExportUser(userId);
        }
      } else {
        // 批量删除
        await batchUserOperations([{
          type: 'delete',
          userIds: selectedUsers
        }]);
      }
      
      setSelectedUsers([]);
      setShowBatchDialog(false);
      
      toast({
        title: "成功",
        description: `批量${operation === 'delete' ? '删除' : '导出'}操作完成`,
        variant: "default"
      });
    } catch (err) {
      toast({
        title: "错误",
        description: `批量操作失败`,
        variant: "destructive"
      });
    }
  };

  const toggleUserSelection = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const toggleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers.map(user => user.id));
    }
  };

  return (
    <div className="min-h-screen bg-zinc-950 text-white p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Users className="w-8 h-8 text-[#00d4aa]" />
              用户管理
            </h1>
            <p className="text-zinc-400 mt-2">
              管理系统用户，查看活动统计和数据分析
            </p>
          </div>
          
          <div className="flex gap-2">
            {selectedUsers.length > 0 && (
              <Button
                variant="outline"
                onClick={() => setShowBatchDialog(true)}
                className="border-zinc-700 hover:border-zinc-600"
              >
                批量操作 ({selectedUsers.length})
              </Button>
            )}
            <Button
              onClick={fetchUsers}
              disabled={isLoading}
              className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-black"
            >
              {isLoading ? '刷新中...' : '刷新'}
            </Button>
          </div>
        </div>

        {/* 搜索和过滤 */}
        <Card className="bg-zinc-900 border-zinc-800">
          <CardContent className="p-4">
            <div className="flex gap-4 items-center">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-zinc-400" />
                <Input
                  placeholder="搜索用户ID或名称..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-zinc-800 border-zinc-700 text-white"
                />
              </div>
              
              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-48 bg-zinc-800 border-zinc-700 text-white">
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-zinc-800 border-zinc-700">
                  <SelectItem value="memory_count">按记忆数量排序</SelectItem>
                  <SelectItem value="name">按名称排序</SelectItem>
                  <SelectItem value="last_active">按最后活跃排序</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* 用户统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-zinc-400">总用户数</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{users.length}</div>
            </CardContent>
          </Card>
          
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-zinc-400">总记忆数</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {users.reduce((sum, user) => sum + user.memory_count, 0)}
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-zinc-400">活跃用户</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {users.filter(user => user.memory_count > 0).length}
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-zinc-400">平均记忆数</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {users.length > 0 ? Math.round(users.reduce((sum, user) => sum + user.memory_count, 0) / users.length) : 0}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 用户列表 */}
        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white">用户列表</CardTitle>
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                  onCheckedChange={toggleSelectAll}
                />
                <span className="text-sm text-zinc-400">全选</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {error && (
              <div className="bg-red-900/20 border border-red-800 rounded-lg p-4 mb-4">
                <p className="text-red-400">{error}</p>
              </div>
            )}

            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00d4aa]"></div>
                <span className="ml-2 text-zinc-400">加载中...</span>
              </div>
            ) : filteredUsers.length === 0 ? (
              <div className="text-center py-8 text-zinc-400">
                {searchTerm ? '未找到匹配的用户' : '暂无用户数据'}
              </div>
            ) : (
              <div className="space-y-2">
                {filteredUsers.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center gap-4 p-4 rounded-lg bg-zinc-800 hover:bg-zinc-700 transition-colors cursor-pointer"
                    onClick={() => handleUserClick(user)}
                  >
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={() => toggleUserSelection(user.id)}
                      onClick={(e) => e.stopPropagation()}
                    />

                    <div className="flex items-center gap-3 flex-1">
                      <div className="w-10 h-10 rounded-full bg-[#00d4aa]/20 flex items-center justify-center">
                        <User className="w-5 h-5 text-[#00d4aa]" />
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium text-white">
                            {user.name || user.id}
                          </h3>
                          {user.id === 'default' && (
                            <Badge variant="secondary" className="bg-zinc-700 text-zinc-300">
                              默认
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-zinc-400">
                          ID: {user.id} • {user.memory_count} 条记忆
                        </p>
                      </div>

                      <div className="text-right">
                        <p className="text-sm text-zinc-400">最后活跃</p>
                        <p className="text-sm text-white">
                          {user.last_active
                            ? new Date(user.last_active).toLocaleDateString()
                            : '未知'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 用户详情模态框 */}
      <UserDetailModal
        user={selectedUser}
        isOpen={showDetailModal}
        onClose={() => {
          setShowDetailModal(false);
          setSelectedUser(null);
          setUserAnalytics(null);
        }}
        analytics={userAnalytics}
        onExport={handleExportUser}
        onDelete={handleDeleteUser}
      />

      {/* 批量操作对话框 */}
      <Dialog open={showBatchDialog} onOpenChange={setShowBatchDialog}>
        <DialogContent className="bg-zinc-900 border-zinc-800">
          <DialogHeader>
            <DialogTitle className="text-white">批量操作</DialogTitle>
            <DialogDescription className="text-zinc-400">
              对选中的 {selectedUsers.length} 个用户执行批量操作
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="bg-zinc-800 rounded-lg p-4">
              <h4 className="text-sm font-medium text-zinc-400 mb-2">选中的用户：</h4>
              <div className="flex flex-wrap gap-2">
                {selectedUsers.map(userId => {
                  const user = users.find(u => u.id === userId);
                  return (
                    <Badge key={userId} variant="secondary" className="bg-zinc-700 text-zinc-300">
                      {user?.name || userId}
                    </Badge>
                  );
                })}
              </div>
            </div>
          </div>

          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handleBatchOperation('export')}
              className="border-zinc-700 hover:border-zinc-600"
            >
              <Download className="w-4 h-4 mr-2" />
              批量导出
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleBatchOperation('delete')}
              disabled={selectedUsers.includes('default')}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              批量删除
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowBatchDialog(false)}
              className="border-zinc-700"
            >
              取消
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
