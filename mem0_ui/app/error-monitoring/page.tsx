/**
 * 错误监控页面
 * 
 * 显示应用的错误统计和监控信息：
 * - 错误统计概览
 * - 错误分类和严重性分布
 * - 最近错误列表
 * - 错误导出功能
 */

'use client';

import React, { useEffect, useState } from 'react';
import { AlertTriangle, TrendingUp, Download, RefreshCw, Bug, Shield } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useErrorContext } from '@/components/providers/ErrorProvider';
import { exportErrorLogs, ErrorReport } from '@/lib/errorReporting';

export default function ErrorMonitoringPage() {
  const { errorStats, refreshErrorStats } = useErrorContext();
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    refreshErrorStats();
  }, [refreshErrorStats]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      refreshErrorStats();
    } finally {
      setTimeout(() => setIsRefreshing(false), 500);
    }
  };

  const handleExportLogs = () => {
    try {
      const logs = exportErrorLogs();
      const blob = new Blob([logs], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `error-logs-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export error logs:', error);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-yellow-500';
      case 'medium':
        return 'bg-orange-500';
      case 'high':
        return 'bg-red-500';
      case 'critical':
        return 'bg-red-700';
      default:
        return 'bg-zinc-500';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'network':
        return '🌐';
      case 'api':
        return '🔌';
      case 'validation':
        return '✅';
      case 'auth':
        return '🔐';
      case 'application':
        return '⚙️';
      default:
        return '❓';
    }
  };

  if (!errorStats) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-zinc-400" />
            <p className="text-zinc-400">加载错误统计中...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-red-500/20 rounded-full">
            <Shield className="h-6 w-6 text-red-500" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-white">错误监控</h1>
            <p className="text-zinc-400">应用错误统计和监控信息</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportLogs}
          >
            <Download className="h-4 w-4 mr-2" />
            导出日志
          </Button>
        </div>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-zinc-400">总错误数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{errorStats.totalErrors}</div>
          </CardContent>
        </Card>

        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-zinc-400">高严重性错误</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">
              {errorStats.errorsBySeverity.high + errorStats.errorsBySeverity.critical}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-zinc-400">网络错误</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-500">
              {errorStats.errorsByCategory.network}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-zinc-400">API错误</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-500">
              {errorStats.errorsByCategory.api}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 详细统计 */}
      <Tabs defaultValue="categories" className="space-y-4">
        <TabsList className="bg-zinc-800">
          <TabsTrigger value="categories">错误分类</TabsTrigger>
          <TabsTrigger value="severity">严重性分布</TabsTrigger>
          <TabsTrigger value="recent">最近错误</TabsTrigger>
          <TabsTrigger value="top">常见错误</TabsTrigger>
        </TabsList>

        <TabsContent value="categories">
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader>
              <CardTitle className="text-white">错误分类统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {Object.entries(errorStats.errorsByCategory).map(([category, count]) => (
                  <div key={category} className="flex items-center justify-between p-3 bg-zinc-800 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getCategoryIcon(category)}</span>
                      <span className="text-zinc-300 capitalize">{category}</span>
                    </div>
                    <Badge variant="secondary">{count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="severity">
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader>
              <CardTitle className="text-white">严重性分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(errorStats.errorsBySeverity).map(([severity, count]) => (
                  <div key={severity} className="flex items-center justify-between p-3 bg-zinc-800 rounded-lg">
                    <span className="text-zinc-300 capitalize">{severity}</span>
                    <Badge className={`${getSeverityColor(severity)} text-white`}>
                      {count}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent">
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader>
              <CardTitle className="text-white">最近错误</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {errorStats.recentErrors.length === 0 ? (
                  <div className="text-center py-8 text-zinc-400">
                    <Bug className="h-8 w-8 mx-auto mb-2" />
                    <p>暂无错误记录</p>
                  </div>
                ) : (
                  errorStats.recentErrors.map((error: ErrorReport) => (
                    <div key={error.id} className="p-3 bg-zinc-800 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <Badge className={`${getSeverityColor(error.severity)} text-white text-xs`}>
                              {error.severity}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {error.category}
                            </Badge>
                          </div>
                          <p className="text-zinc-300 text-sm">{error.message}</p>
                          <p className="text-zinc-500 text-xs mt-1">
                            {new Date(error.timestamp).toLocaleString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="top">
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader>
              <CardTitle className="text-white">常见错误</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {errorStats.topErrors.length === 0 ? (
                  <div className="text-center py-8 text-zinc-400">
                    <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                    <p>暂无错误统计</p>
                  </div>
                ) : (
                  errorStats.topErrors.map((error, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-zinc-800 rounded-lg">
                      <div className="flex-1">
                        <p className="text-zinc-300 text-sm">{error.message}</p>
                      </div>
                      <Badge variant="secondary">{error.count}次</Badge>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
