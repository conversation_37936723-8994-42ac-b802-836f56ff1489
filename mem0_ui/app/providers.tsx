"use client";

import { Provider } from "react-redux";
import { Toaster } from "sonner";

import { store } from "../store/store";
import { ErrorProvider } from "../components/providers/ErrorProvider";
import ErrorBoundary from "../components/common/ErrorBoundary";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary>
      <ErrorProvider>
        <Provider store={store}>
          {children}
          <Toaster
            position="top-right"
            richColors
            closeButton
            duration={5000}
          />
        </Provider>
      </ErrorProvider>
    </ErrorBoundary>
  );
}
