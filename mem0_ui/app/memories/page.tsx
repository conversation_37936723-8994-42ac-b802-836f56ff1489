"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import { MemoriesSection } from "@/app/memories/components/MemoriesSection";
import { MemoryFilters } from "@/app/memories/components/MemoryFilters";
import "@/styles/animation.css";
import UpdateMemory from "@/components/shared/update-memory";
import { useUI } from "@/hooks/useUI";
import ErrorBoundary from "@/components/common/ErrorBoundary";
import { performance } from "@/lib/utils";

export default function MemoriesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { updateMemoryDialog, handleCloseUpdateMemoryDialog } = useUI();
  useEffect(() => {
    // Set default pagination values if not present in URL
    const initializePagination = performance.debounce(() => {
      if (!searchParams.has("page") || !searchParams.has("size")) {
        const params = new URLSearchParams(searchParams.toString());
        if (!searchParams.has("page")) params.set("page", "1");
        if (!searchParams.has("size")) params.set("size", "10");
        router.push(`?${params.toString()}`);
      }
    }, 100);

    initializePagination();
  }, [searchParams, router]);

  return (
    <ErrorBoundary>
      <div className="min-h-screen">
        <UpdateMemory
          memoryId={updateMemoryDialog.memoryId || ""}
          memoryContent={updateMemoryDialog.memoryContent || ""}
          open={updateMemoryDialog.isOpen}
          onOpenChange={handleCloseUpdateMemoryDialog}
        />
        <main className="flex-1 py-6">
          <div className="container-responsive">
            <div className="space-responsive">
              <ErrorBoundary resetOnPropsChange>
                <div className="animate-fade-slide-down">
                  <MemoryFilters />
                </div>
              </ErrorBoundary>

              <ErrorBoundary resetOnPropsChange>
                <div className="animate-fade-slide-down">
                  <MemoriesSection />
                </div>
              </ErrorBoundary>
            </div>
          </div>
        </main>
      </div>
    </ErrorBoundary>
  );
}
