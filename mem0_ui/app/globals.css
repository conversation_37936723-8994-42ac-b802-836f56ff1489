@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 260 94% 59%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 260 94% 59%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 260 94% 59%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 260 94% 59%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* 改善滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-zinc-900;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-zinc-700 rounded-md;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-zinc-600;
  }

  /* 改善焦点样式 */
  *:focus-visible {
    @apply outline-none ring-2 ring-[#00d4aa] ring-offset-2 ring-offset-zinc-950;
  }

  /* 改善选择文本样式 */
  ::selection {
    @apply bg-[#00d4aa] text-black;
  }
}

@layer components {
  /* 响应式容器 */
  .container-responsive {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* 响应式网格 */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }

  /* 统计卡片网格 */
  .stats-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6;
  }

  /* 悬浮效果 */
  .hover-lift {
    @apply transition-all duration-200 ease-in-out;
  }

  .hover-lift:hover {
    @apply transform -translate-y-1 shadow-lg shadow-black/20;
  }

  /* 品牌渐变 */
  .brand-gradient {
    background: linear-gradient(135deg, #00d4aa 0%, #00c49a 100%);
  }

  /* 加载动画 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-fade-slide-down {
    animation: fadeSlideDown 0.4s ease-out;
  }

  /* 无障碍访问改进 */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
  }

  /* 跳转到主内容链接 */
  .skip-link {
    @apply absolute -top-10 left-4 z-50 bg-[#00d4aa] text-black px-4 py-2 rounded-md font-medium;
    @apply focus:top-4 transition-all duration-200;
  }
}

@layer utilities {
  /* 响应式文本大小 */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl;
  }

  /* 响应式间距 */
  .space-responsive {
    @apply space-y-4 sm:space-y-6;
  }

  .gap-responsive {
    @apply gap-4 sm:gap-6;
  }

  /* 性能优化类 */
  .will-change-transform {
    will-change: transform;
  }

  .gpu-accelerated {
    transform: translateZ(0);
  }
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeSlideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .mobile-optimized {
    @apply text-sm leading-relaxed;
  }

  .mobile-padding {
    @apply px-4 py-3;
  }

  .mobile-grid {
    @apply grid-cols-1 gap-3;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .high-contrast {
    @apply border-2 border-white;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
