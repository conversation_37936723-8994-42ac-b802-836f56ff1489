"use client";

import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store/store';
import { setMemoriesSuccess, setMemoriesError, clearError } from '@/store/memoriesSlice';
import { getPersistedStateSize, isLocalStorageAvailable } from '@/lib/persistence';

export default function TestPersistencePage() {
  const dispatch = useDispatch();
  const memories = useSelector((state: RootState) => state.memories);
  const [persistedSize, setPersistedSize] = useState<number>(0);
  const [isStorageAvailable, setIsStorageAvailable] = useState<boolean>(false);

  useEffect(() => {
    setPersistedSize(getPersistedStateSize());
    setIsStorageAvailable(isLocalStorageAvailable());
  }, []);

  const handleAddTestData = () => {
    const testMemories = [
      {
        id: '1',
        content: '测试记忆1 - 这是一个持久化测试',
        metadata: { test: true },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: 'test-user',
        hash: 'test-hash-1'
      },
      {
        id: '2',
        content: '测试记忆2 - 页面刷新后应该保持',
        metadata: { test: true },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: 'test-user',
        hash: 'test-hash-2'
      }
    ];
    
    dispatch(setMemoriesSuccess(testMemories));
    
    // 更新持久化大小
    setTimeout(() => {
      setPersistedSize(getPersistedStateSize());
    }, 100);
  };

  const handleTriggerError = () => {
    dispatch(setMemoriesError('这是一个测试错误 - 不应该清空数据'));
  };

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleClearStorage = () => {
    localStorage.removeItem('mem0_ui_state');
    setPersistedSize(0);
    window.location.reload();
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold text-white">Redux 持久化测试</h1>
      
      {/* 存储状态 */}
      <div className="bg-zinc-800 p-4 rounded-lg">
        <h2 className="text-lg font-semibold text-white mb-2">存储状态</h2>
        <div className="space-y-2 text-sm">
          <p className="text-zinc-300">
            LocalStorage 可用: {isStorageAvailable ? '✅ 是' : '❌ 否'}
          </p>
          <p className="text-zinc-300">
            持久化数据大小: {persistedSize.toFixed(2)} KB
          </p>
        </div>
      </div>

      {/* 当前状态 */}
      <div className="bg-zinc-800 p-4 rounded-lg">
        <h2 className="text-lg font-semibold text-white mb-2">当前 Redux 状态</h2>
        <div className="space-y-2 text-sm">
          <p className="text-zinc-300">
            状态: <span className={`px-2 py-1 rounded text-xs ${
              memories.status === 'succeeded' ? 'bg-green-600' :
              memories.status === 'failed' ? 'bg-red-600' :
              memories.status === 'loading' ? 'bg-yellow-600' :
              'bg-gray-600'
            }`}>
              {memories.status}
            </span>
          </p>
          <p className="text-zinc-300">记忆数量: {memories.memories.length}</p>
          <p className="text-zinc-300">选中数量: {memories.selectedMemoryIds.length}</p>
          {memories.error && (
            <p className="text-red-400">错误: {memories.error}</p>
          )}
        </div>
      </div>

      {/* 记忆列表 */}
      {memories.memories.length > 0 && (
        <div className="bg-zinc-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold text-white mb-2">记忆列表</h2>
          <div className="space-y-2">
            {memories.memories.map((memory) => (
              <div key={memory.id} className="bg-zinc-700 p-3 rounded">
                <p className="text-white text-sm">{memory.content}</p>
                <p className="text-zinc-400 text-xs mt-1">ID: {memory.id}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="bg-zinc-800 p-4 rounded-lg">
        <h2 className="text-lg font-semibold text-white mb-4">测试操作</h2>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={handleAddTestData}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            添加测试数据
          </button>
          
          <button
            onClick={handleTriggerError}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            触发错误状态
          </button>
          
          {memories.error && (
            <button
              onClick={handleClearError}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
            >
              清除错误
            </button>
          )}
          
          <button
            onClick={handleClearStorage}
            className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors"
          >
            清空存储并刷新
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
          >
            刷新页面测试持久化
          </button>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="bg-zinc-800 p-4 rounded-lg">
        <h2 className="text-lg font-semibold text-white mb-2">测试说明</h2>
        <div className="text-sm text-zinc-300 space-y-1">
          <p>1. 点击"添加测试数据"添加一些记忆数据</p>
          <p>2. 点击"刷新页面测试持久化"验证数据是否保持</p>
          <p>3. 点击"触发错误状态"测试错误不会清空数据</p>
          <p>4. 观察持久化数据大小的变化</p>
        </div>
      </div>
    </div>
  );
}
