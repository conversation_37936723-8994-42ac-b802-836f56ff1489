"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Network, RefreshCw, Activity, Database, GitBranch, BarChart3 } from "lucide-react";
import dynamic from 'next/dynamic';

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { PageSkeleton, CardSkeleton, GraphSkeleton } from '@/components/ui/LoadingSkeleton';
import { RootState, AppDispatch } from '@/store/store';
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';
import { useRenderPerformance, useAutoPerformanceTuning } from '@/hooks/usePerformance';
import { useDebounce } from '@/hooks/useDebounce';

// Graph Memory 组件导入

// 动态导入组件以避免SSR问题，添加loading状态
const GraphMemoryFilters = dynamic(() => import('@/components/graph/GraphMemoryFilters'), {
  ssr: false,
  loading: () => <CardSkeleton className="h-32" />
});
const GraphStats = dynamic(() => import('@/components/graph/GraphStats'), {
  ssr: false,
  loading: () => <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
    {Array.from({ length: 4 }).map((_, i) => (
      <CardSkeleton key={i} className="h-24" showHeader={false} />
    ))}
  </div>
});
const GraphVisualization = dynamic(() => import('@/components/graph/GraphVisualization'), {
  ssr: false,
  loading: () => <GraphSkeleton className="h-[600px]" />
});
const EntityPanel = dynamic(() => import('@/components/graph/EntityPanel'), {
  ssr: false,
  loading: () => <CardSkeleton className="h-96" />
});
const RelationshipPanel = dynamic(() => import('@/components/graph/RelationshipPanel'), {
  ssr: false,
  loading: () => <CardSkeleton className="h-96" />
});
const GraphHistory = dynamic(() => import('@/components/graph/GraphHistory'), {
  ssr: false,
  loading: () => <CardSkeleton className="h-64" />
});
const GraphPerformanceMonitor = dynamic(() => import('@/components/graph/GraphPerformanceMonitor'), { ssr: false });

import "@/styles/animation.css";
import "@/styles/graph-performance.css";

export default function GraphMemoryPage() {
  const { toast } = useToast();

  // Redux state
  const {
    nodes,
    edges,
    status,
    error,
    filters,
    history
  } = useSelector((state: RootState) => state.graphMemory);

  // API hooks
  const { fetchGraphMemories, isLoading } = useGraphMemoryApi();

  // 性能监控
  const { metrics } = useRenderPerformance('GraphMemoryPage');
  const { optimizationLevel, config } = useAutoPerformanceTuning('GraphMemoryPage');

  // Local state
  const [activeTab, setActiveTab] = useState('visualization');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false);

  // 初始化数据加载
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        await fetchGraphMemories(filters);
      } catch (error) {
        console.error('Failed to load initial graph data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load graph memory data',
          variant: 'destructive',
        });
      }
    };

    loadInitialData();
  }, [fetchGraphMemories, filters, toast]);

  // 防抖的刷新数据
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await fetchGraphMemories(filters);
      setLastRefresh(new Date());
      toast({
        title: 'Success',
        description: 'Graph memory data refreshed successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to refresh graph memory data',
        variant: 'destructive',
      });
    } finally {
      setIsRefreshing(false);
    }
  }, [filters, fetchGraphMemories, toast]);

  const debouncedRefresh = useDebounce(handleRefresh, config.debounceDelay);

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + R: 刷新数据
      if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        event.preventDefault();
        handleRefresh();
      }

      // 数字键切换标签页
      if (event.key >= '1' && event.key <= '6') {
        const tabIndex = parseInt(event.key) - 1;
        const tabs = ['visualization', 'entities', 'relationships', 'history', 'stats'];
        if (tabs[tabIndex]) {
          setActiveTab(tabs[tabIndex]);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleRefresh]);

  // 获取统计数据
  const getStatsData = () => {
    return {
      totalEntities: nodes.length,
      totalRelations: edges.length,
      totalOperations: history.length,
      lastUpdate: lastRefresh
    };
  };

  const statsData = getStatsData();

  return (
    <main className="flex-1 py-6">
      <div className="container max-w-7xl">
        <div className="space-y-6">
          {/* 页面标题和控制栏 */}
          <div className="animate-fade-slide-down">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold flex items-center gap-3">
                  <Network className="h-8 w-8 text-[#00d4aa]" />
                  Graph Memory
                </h1>
                <p className="text-muted-foreground mt-2">
                  可视化和管理记忆图谱，探索实体间的关系网络
                </p>
              </div>

              <div className="flex items-center space-x-4">
                {/* 状态指示器 */}
                <div className="flex items-center space-x-2">
                  <Badge variant={status === 'succeeded' ? 'default' : status === 'loading' ? 'secondary' : 'destructive'}>
                    {status === 'succeeded' && '✓ Connected'}
                    {status === 'loading' && '⟳ Loading'}
                    {status === 'failed' && '✗ Error'}
                    {status === 'idle' && '○ Ready'}
                  </Badge>

                  {/* 数据统计 */}
                  <div className="hidden md:flex items-center space-x-4 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Database className="h-4 w-4" />
                      <span>{statsData.totalEntities} entities</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <GitBranch className="h-4 w-4" />
                      <span>{statsData.totalRelations} relations</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Activity className="h-4 w-4" />
                      <span>{statsData.totalOperations} operations</span>
                    </div>
                  </div>
                </div>

                {/* 刷新按钮 */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isRefreshing || isLoading}
                  className="flex items-center space-x-2"
                >
                  <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                  <span className="hidden sm:inline">Refresh</span>
                </Button>
              </div>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="animate-fade-slide-down">
              <Card className="border-red-800 bg-red-900/20">
                <CardContent className="pt-6">
                  <div className="flex items-center space-x-2 text-red-400">
                    <span className="font-medium">Error:</span>
                    <span>{error}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 筛选器 */}
          <div className="animate-fade-slide-down delay-1">
            <GraphMemoryFilters />
          </div>

          {/* 主要内容区域 */}
          <div className="animate-fade-slide-down delay-2">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-5 lg:w-auto lg:grid-cols-5">
                <TabsTrigger value="visualization" className="flex items-center space-x-2">
                  <Network className="h-4 w-4" />
                  <span className="hidden sm:inline">Visualization</span>
                </TabsTrigger>
                <TabsTrigger value="entities" className="flex items-center space-x-2">
                  <Database className="h-4 w-4" />
                  <span className="hidden sm:inline">Entities</span>
                </TabsTrigger>
                <TabsTrigger value="relationships" className="flex items-center space-x-2">
                  <GitBranch className="h-4 w-4" />
                  <span className="hidden sm:inline">Relations</span>
                </TabsTrigger>
                <TabsTrigger value="history" className="flex items-center space-x-2">
                  <Activity className="h-4 w-4" />
                  <span className="hidden sm:inline">History</span>
                </TabsTrigger>
                <TabsTrigger value="stats" className="flex items-center space-x-2">
                  <BarChart3 className="h-4 w-4" />
                  <span className="hidden sm:inline">Stats</span>
                </TabsTrigger>
              </TabsList>

              {/* 可视化标签页 */}
              <TabsContent value="visualization" className="space-y-6">
                <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
                  {/* 统计卡片 */}
                  <div className="xl:col-span-4">
                    <GraphStats />
                  </div>

                  {/* 图可视化 */}
                  <div className="xl:col-span-4">
                    <Card className="bg-zinc-900 border-zinc-800">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center">
                          <Network className="h-5 w-5 mr-2" />
                          Graph Visualization
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-0">
                        <div className="h-[600px] w-full">
                          <GraphVisualization />
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              {/* 实体管理标签页 */}
              <TabsContent value="entities">
                <EntityPanel />
              </TabsContent>

              {/* 关系管理标签页 */}
              <TabsContent value="relationships">
                <RelationshipPanel />
              </TabsContent>

              {/* 历史记录标签页 */}
              <TabsContent value="history">
                <GraphHistory />
              </TabsContent>

              {/* 统计信息标签页 */}
              <TabsContent value="stats">
                <GraphStats />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* 性能监控组件 */}
      <GraphPerformanceMonitor
        isVisible={showPerformanceMonitor}
        onToggle={setShowPerformanceMonitor}
      />
    </main>
  );
}
