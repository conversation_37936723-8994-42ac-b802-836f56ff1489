'use client';

import React, { useState } from 'react';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  Server,
  TrendingUp,
  XCircle
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useSystemMonitoring } from '@/hooks/useSystemMonitoring';
import SystemHealthCard from '@/components/monitoring/SystemHealthCard';
import APIMetricsChart from '@/components/monitoring/APIMetricsChart';
import ResourceUsageMonitor from '@/components/monitoring/ResourceUsageMonitor';
import AlertsPanel from '@/components/monitoring/AlertsPanel';

export default function MonitoringPage() {
  const {
    systemHealth,
    isHealthLoading,
    apiMetrics,
    resourceUsage,
    alertEvents,
    unresolvedAlertCount,
    refreshSystemHealth,
    resolveAlert,
    clearAllAlerts,
    isMonitoring
  } = useSystemMonitoring();

  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshSystemHealth();
    setTimeout(() => setRefreshing(false), 1000);
  };

  // 获取系统状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-400';
      case 'warning':
        return 'text-yellow-400';
      case 'critical':
        return 'text-red-400';
      default:
        return 'text-zinc-400';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-400" />;
      default:
        return <Activity className="w-5 h-5 text-zinc-400" />;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">系统监控</h1>
          <p className="text-zinc-400 mt-1">
            实时监控系统性能和健康状态
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${isMonitoring ? 'bg-green-400' : 'bg-red-400'}`} />
            <span className="text-sm text-zinc-400">
              {isMonitoring ? '监控中' : '已停止'}
            </span>
          </div>
          <Button
            onClick={handleRefresh}
            disabled={refreshing || isHealthLoading}
            className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-black"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>

      {/* 系统状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-zinc-400 flex items-center gap-2">
              <Server className="w-4 h-4" />
              系统状态
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              {systemHealth && getStatusIcon(systemHealth.status)}
              <span className={`text-lg font-semibold ${systemHealth ? getStatusColor(systemHealth.status) : 'text-zinc-400'}`}>
                {systemHealth ? systemHealth.status.toUpperCase() : 'LOADING'}
              </span>
            </div>
            {systemHealth && (
              <div className="mt-2">
                <div className="flex justify-between text-xs text-zinc-400 mb-1">
                  <span>健康分数</span>
                  <span>{systemHealth.score}/100</span>
                </div>
                <Progress value={systemHealth.score} className="h-1" />
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-zinc-400 flex items-center gap-2">
              <Clock className="w-4 h-4" />
              API响应时间
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {apiMetrics ? `${apiMetrics.averageResponseTime}ms` : '--'}
            </div>
            <p className="text-xs text-zinc-400 mt-1">
              最近1小时平均值
            </p>
          </CardContent>
        </Card>

        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-zinc-400 flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              错误率
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {apiMetrics ? `${apiMetrics.errorRate}%` : '--'}
            </div>
            <p className="text-xs text-zinc-400 mt-1">
              {apiMetrics ? `${apiMetrics.requestCount} 次请求` : '无数据'}
            </p>
          </CardContent>
        </Card>

        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-zinc-400 flex items-center gap-2">
              <AlertTriangle className="w-4 h-4" />
              活跃告警
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {unresolvedAlertCount}
            </div>
            <p className="text-xs text-zinc-400 mt-1">
              未解决的告警事件
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 详细监控面板 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 系统健康状态 */}
        <SystemHealthCard 
          systemHealth={systemHealth}
          isLoading={isHealthLoading}
          onRefresh={refreshSystemHealth}
        />

        {/* 资源使用监控 */}
        <ResourceUsageMonitor 
          resourceUsage={resourceUsage}
        />
      </div>

      {/* API性能图表 */}
      <APIMetricsChart 
        apiMetrics={apiMetrics}
      />

      {/* 告警面板 */}
      <AlertsPanel
        alertEvents={alertEvents}
        onResolveAlert={resolveAlert}
        onClearAllAlerts={clearAllAlerts}
      />
    </div>
  );
}
