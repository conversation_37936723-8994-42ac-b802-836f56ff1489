services:
  mem0-ui:
    build:
      context: .
      dockerfile: Dockerfile
    image: mem0/mem0-ui:latest
    container_name: mem0-ui
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8000}
      - NEXT_PUBLIC_USER_ID=${NEXT_PUBLIC_USER_ID:-default}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - mem0-network

networks:
  mem0-network:
    driver: bridge
