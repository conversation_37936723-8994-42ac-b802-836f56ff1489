import { configureStore, Middleware } from '@reduxjs/toolkit';

import { validatePersistedState, clearCorruptedState, isLocalStorageAvailable } from '@/lib/persistence';

import memoriesReducer from './memoriesSlice';
import profileReducer from './profileSlice';
import appsReducer from './appsSlice';
import uiReducer from './uiSlice';
import filtersReducer from './filtersSlice';
import configReducer from './configSlice';
import graphMemoryReducer from './graphMemorySlice';

// 自定义持久化中间件
const persistenceMiddleware: Middleware = (store) => (next) => (action) => {
  const result = next(action);

  // 只在浏览器环境中执行持久化
  if (typeof window !== 'undefined' && isLocalStorageAvailable()) {
    try {
      const state = store.getState();

      // 只持久化关键数据
      const stateToPersist = {
        profile: state.profile,
        config: state.config,
        memories: {
          ...state.memories,
          // 不持久化loading状态
          status: 'idle',
          error: null,
        },
        apps: state.apps,
      };

      localStorage.setItem('mem0_ui_state', JSON.stringify(stateToPersist));
    } catch (error) {
      console.error('Error persisting state:', error);
    }
  }

  return result;
};

// 从localStorage恢复状态
function loadPersistedState() {
  if (typeof window === 'undefined' || !isLocalStorageAvailable()) {
    return {};
  }

  try {
    const persistedState = localStorage.getItem('mem0_ui_state');
    if (!persistedState) {
      return {};
    }

    const parsed = JSON.parse(persistedState);

    if (!validatePersistedState(parsed)) {
      console.warn('Invalid persisted state detected, clearing...');
      localStorage.removeItem('mem0_ui_state');
      return {};
    }

    return parsed;
  } catch (error) {
    console.error('Error loading persisted state:', error);
    localStorage.removeItem('mem0_ui_state');
    return {};
  }
}

// 获取持久化的初始状态
const persistedState = loadPersistedState();

export const store = configureStore({
  reducer: {
    memories: memoriesReducer,
    profile: profileReducer,
    apps: appsReducer,
    ui: uiReducer,
    filters: filtersReducer,
    config: configReducer,
    graphMemory: graphMemoryReducer,
  },
  preloadedState: persistedState,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略非序列化的action类型
        ignoredActionsPaths: ['meta.arg', 'payload.timestamp'],
      },
    }).concat(persistenceMiddleware),
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {memories: MemoriesState, profile: ProfileState, apps: AppsState, ui: UIState, ...}
export type AppDispatch = typeof store.dispatch; 