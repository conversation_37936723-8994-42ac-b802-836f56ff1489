import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface LLMConfig {
  model: string;
  temperature: number;
  max_tokens: number;
  api_key?: string;
  ollama_base_url?: string;
}

export interface LLMProvider {
  provider: string;
  config: LLMConfig;
}

export interface EmbedderConfig {
  model: string;
  api_key?: string;
  ollama_base_url?: string;
}

export interface EmbedderProvider {
  provider: string;
  config: EmbedderConfig;
}

export interface Mem0Config {
  llm?: LLMProvider;
  embedder?: EmbedderProvider;
}

export interface InstructionTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  category: string;
  tags: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface InstructionCategory {
  id: string;
  name: string;
  description: string;
  color: string;
}

export interface OpenMemoryConfig {
  custom_instructions?: string | null;
  instruction_templates?: InstructionTemplate[];
  instruction_categories?: InstructionCategory[];
}

export interface ConfigState {
  openmemory: OpenMemoryConfig;
  mem0: Mem0Config;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
}

const initialState: ConfigState = {
  openmemory: {
    custom_instructions: null,
    instruction_templates: [],
    instruction_categories: [
      {
        id: 'general',
        name: '通用指令',
        description: '适用于各种场景的通用指令模板',
        color: '#00d4aa'
      },
      {
        id: 'memory',
        name: '记忆管理',
        description: '专门用于记忆创建和管理的指令',
        color: '#3b82f6'
      },
      {
        id: 'analysis',
        name: '分析处理',
        description: '用于数据分析和处理的指令模板',
        color: '#8b5cf6'
      }
    ],
  },
  mem0: {
    llm: {
      provider: 'openai',
      config: {
        model: 'gpt-4o-mini',
        temperature: 0.1,
        max_tokens: 2000,
        api_key: 'env:OPENAI_API_KEY',
      },
    },
    embedder: {
      provider: 'openai',
      config: {
        model: 'text-embedding-3-small',
        api_key: 'env:OPENAI_API_KEY',
      },
    },
  },
  status: 'idle',
  error: null,
};

const configSlice = createSlice({
  name: 'config',
  initialState,
  reducers: {
    setConfigLoading: (state) => {
      state.status = 'loading';
      state.error = null;
    },
    setConfigSuccess: (state, action: PayloadAction<{ openmemory?: OpenMemoryConfig; mem0: Mem0Config }>) => {
      if (action.payload.openmemory) {
        state.openmemory = action.payload.openmemory;
      }
      state.mem0 = action.payload.mem0;
      state.status = 'succeeded';
      state.error = null;
    },
    setConfigError: (state, action: PayloadAction<string>) => {
      state.status = 'failed';
      state.error = action.payload;
    },
    updateOpenMemory: (state, action: PayloadAction<OpenMemoryConfig>) => {
      state.openmemory = action.payload;
    },
    updateLLM: (state, action: PayloadAction<LLMProvider>) => {
      state.mem0.llm = action.payload;
    },
    updateEmbedder: (state, action: PayloadAction<EmbedderProvider>) => {
      state.mem0.embedder = action.payload;
    },
    updateMem0Config: (state, action: PayloadAction<Mem0Config>) => {
      state.mem0 = action.payload;
    },
    // 指令模板管理
    addInstructionTemplate: (state, action: PayloadAction<InstructionTemplate>) => {
      if (!state.openmemory.instruction_templates) {
        state.openmemory.instruction_templates = [];
      }
      state.openmemory.instruction_templates.push(action.payload);
    },
    updateInstructionTemplate: (state, action: PayloadAction<InstructionTemplate>) => {
      if (state.openmemory.instruction_templates) {
        const index = state.openmemory.instruction_templates.findIndex(t => t.id === action.payload.id);
        if (index !== -1) {
          state.openmemory.instruction_templates[index] = action.payload;
        }
      }
    },
    deleteInstructionTemplate: (state, action: PayloadAction<string>) => {
      if (state.openmemory.instruction_templates) {
        state.openmemory.instruction_templates = state.openmemory.instruction_templates.filter(
          t => t.id !== action.payload
        );
      }
    },
    // 指令分类管理
    addInstructionCategory: (state, action: PayloadAction<InstructionCategory>) => {
      if (!state.openmemory.instruction_categories) {
        state.openmemory.instruction_categories = [];
      }
      state.openmemory.instruction_categories.push(action.payload);
    },
    updateInstructionCategory: (state, action: PayloadAction<InstructionCategory>) => {
      if (state.openmemory.instruction_categories) {
        const index = state.openmemory.instruction_categories.findIndex(c => c.id === action.payload.id);
        if (index !== -1) {
          state.openmemory.instruction_categories[index] = action.payload;
        }
      }
    },
    deleteInstructionCategory: (state, action: PayloadAction<string>) => {
      if (state.openmemory.instruction_categories) {
        state.openmemory.instruction_categories = state.openmemory.instruction_categories.filter(
          c => c.id !== action.payload
        );
      }
    },
  },
});

export const {
  setConfigLoading,
  setConfigSuccess,
  setConfigError,
  updateOpenMemory,
  updateLLM,
  updateEmbedder,
  updateMem0Config,
  addInstructionTemplate,
  updateInstructionTemplate,
  deleteInstructionTemplate,
  addInstructionCategory,
  updateInstructionCategory,
  deleteInstructionCategory,
} = configSlice.actions;

export default configSlice.reducer; 