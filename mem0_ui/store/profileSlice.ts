import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ProfileState {
  userId: string;
  viewMode: 'system' | 'user'; // 新增：视图模式
  availableUsers: string[]; // 新增：可用用户列表
  totalMemories: number;
  totalApps: number;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
  apps: any[];
}

const initialState: ProfileState = {
  userId: process.env.NEXT_PUBLIC_USER_ID || 'default',
  viewMode: 'system', // 默认为系统级视图
  availableUsers: [], // 将通过API获取
  totalMemories: 0,
  totalApps: 0,
  status: 'idle',
  error: null,
  apps: [],
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    setUserId: (state, action: PayloadAction<string>) => {
      state.userId = action.payload;
    },
    setViewMode: (state, action: PayloadAction<'system' | 'user'>) => {
      state.viewMode = action.payload;
      // 当切换到系统级视图时，清除特定用户ID
      if (action.payload === 'system') {
        state.userId = 'system';
      }
    },
    setAvailableUsers: (state, action: PayloadAction<string[]>) => {
      state.availableUsers = action.payload;
    },
    switchToUser: (state, action: PayloadAction<string>) => {
      state.viewMode = 'user';
      state.userId = action.payload;
    },
    switchToSystemView: (state) => {
      state.viewMode = 'system';
      state.userId = 'system';
    },
    setProfileLoading: (state) => {
      state.status = 'loading';
      state.error = null;
    },
    setProfileError: (state, action: PayloadAction<string>) => {
      state.status = 'failed';
      state.error = action.payload;
    },
    resetProfileState: (state) => {
      state.status = 'idle';
      state.error = null;
      state.viewMode = 'system';
      state.userId = 'system';
      state.availableUsers = [];
    },
    setTotalMemories: (state, action: PayloadAction<number>) => {
      state.totalMemories = action.payload;
    },
    setTotalApps: (state, action: PayloadAction<number>) => {
      state.totalApps = action.payload;
    },
    setApps: (state, action: PayloadAction<any[]>) => {
      state.apps = action.payload;
    }
  },
});

export const {
  setUserId,
  setViewMode,
  setAvailableUsers,
  switchToUser,
  switchToSystemView,
  setProfileLoading,
  setProfileError,
  resetProfileState,
  setTotalMemories,
  setTotalApps,
  setApps
} = profileSlice.actions;

export default profileSlice.reducer;