import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import {
  GraphNode,
  GraphEdge,
  GraphMemoryFilters,
  GraphMemoryViewState,
  GraphMemoryStats,
  GraphMemoryHistoryItem,
  Mem0GraphMemoryResponse,
  GraphMemoryBatchOperation,
  GraphMemoryBatchResult
} from '@/types/graph-memory'

// ============================================================================
// Graph Memory State 定义
// ============================================================================

export interface GraphMemoryState {
  // 图数据
  nodes: GraphNode[]
  edges: GraphEdge[]

  // 选择状态
  selectedNodeIds: string[]
  selectedEdgeIds: string[]
  selectedNode: GraphNode | null
  selectedEdge: GraphEdge | null

  // 筛选和视图
  filters: GraphMemoryFilters
  viewState: GraphMemoryViewState

  // 探索模式状态
  explorationMode: {
    isActive: boolean
    focusNodeId: string | null
    explorationNodes: GraphNode[]
    explorationEdges: GraphEdge[]
    originalNodes: GraphNode[]
    originalEdges: GraphEdge[]
    explorationDepth: number
  }

  // 统计信息
  stats: GraphMemoryStats | null

  // 操作历史
  history: GraphMemoryHistoryItem[]

  // 加载状态
  status: 'idle' | 'loading' | 'succeeded' | 'failed'
  error: string | null

  // 批量操作状态
  batchOperation: {
    isActive: boolean
    operation: GraphMemoryBatchOperation | null
    result: GraphMemoryBatchResult | null
  }
}

// ============================================================================
// 初始状态
// ============================================================================

const initialState: GraphMemoryState = {
  // 图数据
  nodes: [],
  edges: [],

  // 选择状态
  selectedNodeIds: [],
  selectedEdgeIds: [],
  selectedNode: null,
  selectedEdge: null,

  // 筛选和视图
  filters: {},
  viewState: {
    layout: 'force',
    zoom: 1,
    center: { x: 0, y: 0 },
    show_labels: true,
    show_edge_labels: false,
    show_minimap: true,
    show_controls: true,
    is_dragging: false,
    is_selecting: false,
    selection_mode: 'single'
  },

  // 探索模式状态
  explorationMode: {
    isActive: false,
    focusNodeId: null,
    explorationNodes: [],
    explorationEdges: [],
    originalNodes: [],
    originalEdges: [],
    explorationDepth: 1
  },

  // 统计信息
  stats: null,

  // 操作历史
  history: [],

  // 加载状态
  status: 'idle',
  error: null,

  // 批量操作状态
  batchOperation: {
    isActive: false,
    operation: null,
    result: null
  }
}

// ============================================================================
// Graph Memory Slice
// ============================================================================

const graphMemorySlice = createSlice({
  name: 'graphMemory',
  initialState,
  reducers: {
    // ========================================================================
    // 数据加载状态管理
    // ========================================================================
    
    setGraphMemoryLoading: (state) => {
      state.status = 'loading'
      state.error = null
    },
    
    setGraphMemorySuccess: (state, action: PayloadAction<Mem0GraphMemoryResponse>) => {
      state.status = 'succeeded'
      state.error = null

      // 转换 Mem0 API 数据为 React Flow 格式
      state.nodes = action.payload.entities.map(entity => ({
        id: entity.id,
        type: 'default',
        position: { x: Math.random() * 500, y: Math.random() * 500 }, // 临时位置，后续由布局算法计算
        data: {
          id: entity.id,
          label: entity.name,
          type: entity.type,
          description: entity.description,
          properties: entity.properties,
          metadata: entity.metadata
        }
      }))

      state.edges = action.payload.relations.map(relation => ({
        id: relation.id,
        source: relation.source_entity_id,
        target: relation.target_entity_id,
        type: 'default',
        data: {
          id: relation.id,
          label: relation.relation_type,
          relation_type: relation.relation_type,
          description: relation.description,
          weight: relation.weight,
          properties: relation.properties,
          metadata: relation.metadata
        }
      }))

      // 更新统计信息
      if (action.payload.metadata) {
        state.stats = {
          total_entities: action.payload.metadata.total_entities,
          total_relations: action.payload.metadata.total_relations,
          graph_density: action.payload.metadata.graph_density || 0,
          active_entities: action.payload.metadata.active_entities || 0,
          entity_types_count: {},
          relation_types_count: {},
          avg_connections_per_entity: 0,
          most_connected_entities: []
        }
      }
    },

    // 新增：直接设置已转换的节点和边数据
    setGraphMemoryNodesAndEdges: (state, action: PayloadAction<{ nodes: GraphNode[]; edges: GraphEdge[] }>) => {
      state.status = 'succeeded'
      state.error = null
      state.nodes = action.payload.nodes
      state.edges = action.payload.edges
    },
    
    setGraphMemoryError: (state, action: PayloadAction<string>) => {
      state.status = 'failed'
      state.error = action.payload
      // 错误状态不清空现有图数据，保持用户数据完整性
    },

    clearGraphMemoryError: (state) => {
      state.error = null
      if (state.status === 'failed') {
        state.status = 'idle'
      }
    },
    
    resetGraphMemoryState: (state) => {
      return { ...initialState }
    },
    
    // ========================================================================
    // 节点和边的选择管理
    // ========================================================================
    
    selectNode: (state, action: PayloadAction<string>) => {
      const nodeId = action.payload
      if (!state.selectedNodeIds.includes(nodeId)) {
        if (state.viewState.selection_mode === 'single') {
          state.selectedNodeIds = [nodeId]
        } else {
          state.selectedNodeIds.push(nodeId)
        }
      }
      
      // 设置当前选中的节点
      state.selectedNode = state.nodes.find(node => node.id === nodeId) || null
    },
    
    deselectNode: (state, action: PayloadAction<string>) => {
      state.selectedNodeIds = state.selectedNodeIds.filter(id => id !== action.payload)
      if (state.selectedNode?.id === action.payload) {
        state.selectedNode = null
      }
    },
    
    selectEdge: (state, action: PayloadAction<string>) => {
      const edgeId = action.payload
      if (!state.selectedEdgeIds.includes(edgeId)) {
        if (state.viewState.selection_mode === 'single') {
          state.selectedEdgeIds = [edgeId]
        } else {
          state.selectedEdgeIds.push(edgeId)
        }
      }
      
      // 设置当前选中的边
      state.selectedEdge = state.edges.find(edge => edge.id === edgeId) || null
    },
    
    deselectEdge: (state, action: PayloadAction<string>) => {
      state.selectedEdgeIds = state.selectedEdgeIds.filter(id => id !== action.payload)
      if (state.selectedEdge?.id === action.payload) {
        state.selectedEdge = null
      }
    },
    
    clearSelection: (state) => {
      state.selectedNodeIds = []
      state.selectedEdgeIds = []
      state.selectedNode = null
      state.selectedEdge = null
    },
    
    selectAllNodes: (state) => {
      state.selectedNodeIds = state.nodes.map(node => node.id)
    },
    
    // ========================================================================
    // 筛选器管理
    // ========================================================================
    
    updateFilters: (state, action: PayloadAction<Partial<GraphMemoryFilters>>) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    
    resetFilters: (state) => {
      state.filters = {}
    },
    
    // ========================================================================
    // 视图状态管理
    // ========================================================================
    
    updateViewState: (state, action: PayloadAction<Partial<GraphMemoryViewState>>) => {
      state.viewState = { ...state.viewState, ...action.payload }
    },
    
    setLayout: (state, action: PayloadAction<GraphMemoryViewState['layout']>) => {
      state.viewState.layout = action.payload
    },
    
    setZoom: (state, action: PayloadAction<number>) => {
      state.viewState.zoom = action.payload
    },
    
    setCenter: (state, action: PayloadAction<{ x: number; y: number }>) => {
      state.viewState.center = action.payload
    },
    
    // ========================================================================
    // 统计信息管理
    // ========================================================================
    
    setStats: (state, action: PayloadAction<GraphMemoryStats>) => {
      state.stats = action.payload
    },
    
    // ========================================================================
    // 操作历史管理
    // ========================================================================
    
    addHistoryItem: (state, action: PayloadAction<GraphMemoryHistoryItem>) => {
      state.history.unshift(action.payload) // 最新的在前面
      // 限制历史记录数量
      if (state.history.length > 100) {
        state.history = state.history.slice(0, 100)
      }
    },
    
    clearHistory: (state) => {
      state.history = []
    },
    
    // ========================================================================
    // 批量操作管理
    // ========================================================================
    
    startBatchOperation: (state, action: PayloadAction<GraphMemoryBatchOperation>) => {
      state.batchOperation.isActive = true
      state.batchOperation.operation = action.payload
      state.batchOperation.result = null
    },
    
    completeBatchOperation: (state, action: PayloadAction<GraphMemoryBatchResult>) => {
      state.batchOperation.isActive = false
      state.batchOperation.result = action.payload
    },
    
    cancelBatchOperation: (state) => {
      state.batchOperation.isActive = false
      state.batchOperation.operation = null
      state.batchOperation.result = null
    },

    // ========================================================================
    // 探索模式管理
    // ========================================================================

    enterExplorationMode: (state, action: PayloadAction<{
      focusNodeId: string
      explorationNodes: GraphNode[]
      explorationEdges: GraphEdge[]
      explorationDepth?: number
    }>) => {
      // 保存原始图数据
      state.explorationMode.originalNodes = [...state.nodes]
      state.explorationMode.originalEdges = [...state.edges]

      // 设置探索模式状态
      state.explorationMode.isActive = true
      state.explorationMode.focusNodeId = action.payload.focusNodeId
      state.explorationMode.explorationNodes = action.payload.explorationNodes
      state.explorationMode.explorationEdges = action.payload.explorationEdges
      state.explorationMode.explorationDepth = action.payload.explorationDepth || 1

      // 更新当前显示的节点和边为探索模式的子集
      state.nodes = action.payload.explorationNodes
      state.edges = action.payload.explorationEdges

      // 清除当前选择，选择焦点节点
      state.selectedNodeIds = [action.payload.focusNodeId]
      state.selectedEdgeIds = []
      state.selectedNode = action.payload.explorationNodes.find(node => node.id === action.payload.focusNodeId) || null
      state.selectedEdge = null
    },

    exitExplorationMode: (state) => {
      if (state.explorationMode.isActive) {
        // 恢复原始图数据
        state.nodes = state.explorationMode.originalNodes
        state.edges = state.explorationMode.originalEdges

        // 重置探索模式状态
        state.explorationMode.isActive = false
        state.explorationMode.focusNodeId = null
        state.explorationMode.explorationNodes = []
        state.explorationMode.explorationEdges = []
        state.explorationMode.originalNodes = []
        state.explorationMode.originalEdges = []
        state.explorationMode.explorationDepth = 1

        // 清除选择状态
        state.selectedNodeIds = []
        state.selectedEdgeIds = []
        state.selectedNode = null
        state.selectedEdge = null
      }
    },

    setFocusNode: (state, action: PayloadAction<string>) => {
      if (state.explorationMode.isActive) {
        state.explorationMode.focusNodeId = action.payload
        // 更新选择状态
        state.selectedNodeIds = [action.payload]
        state.selectedNode = state.explorationMode.explorationNodes.find(node => node.id === action.payload) || null
      }
    },

    setExplorationDepth: (state, action: PayloadAction<number>) => {
      state.explorationMode.explorationDepth = Math.max(1, Math.min(3, action.payload)) // 限制深度在1-3之间
    },

    updateExplorationData: (state, action: PayloadAction<{
      explorationNodes: GraphNode[]
      explorationEdges: GraphEdge[]
    }>) => {
      if (state.explorationMode.isActive) {
        state.explorationMode.explorationNodes = action.payload.explorationNodes
        state.explorationMode.explorationEdges = action.payload.explorationEdges
        state.nodes = action.payload.explorationNodes
        state.edges = action.payload.explorationEdges
      }
    }
  }
})

// ============================================================================
// 导出 Actions
// ============================================================================

export const {
  // 数据加载
  setGraphMemoryLoading,
  setGraphMemorySuccess,
  setGraphMemoryNodesAndEdges,
  setGraphMemoryError,
  clearGraphMemoryError,
  resetGraphMemoryState,

  // 选择管理
  selectNode,
  deselectNode,
  selectEdge,
  deselectEdge,
  clearSelection,
  selectAllNodes,

  // 筛选器
  updateFilters,
  resetFilters,

  // 视图状态
  updateViewState,
  setLayout,
  setZoom,
  setCenter,

  // 探索模式
  enterExplorationMode,
  exitExplorationMode,
  setFocusNode,
  setExplorationDepth,
  updateExplorationData,

  // 统计信息
  setStats,

  // 操作历史
  addHistoryItem,
  clearHistory,

  // 批量操作
  startBatchOperation,
  completeBatchOperation,
  cancelBatchOperation
} = graphMemorySlice.actions

export default graphMemorySlice.reducer

// ============================================================================
// 选择器函数
// ============================================================================

import type { RootState } from './store'

// 基础选择器
export const selectGraphMemoryState = (state: RootState) => state.graphMemory
export const selectGraphNodes = (state: RootState) => state.graphMemory.nodes
export const selectGraphEdges = (state: RootState) => state.graphMemory.edges
export const selectGraphMemoryStatus = (state: RootState) => state.graphMemory.status
export const selectGraphMemoryError = (state: RootState) => state.graphMemory.error

// 选择状态选择器
export const selectSelectedNodeIds = (state: RootState) => state.graphMemory.selectedNodeIds
export const selectSelectedEdgeIds = (state: RootState) => state.graphMemory.selectedEdgeIds
export const selectSelectedNode = (state: RootState) => state.graphMemory.selectedNode
export const selectSelectedEdge = (state: RootState) => state.graphMemory.selectedEdge

// 筛选和视图选择器
export const selectGraphMemoryFilters = (state: RootState) => state.graphMemory.filters
export const selectGraphMemoryViewState = (state: RootState) => state.graphMemory.viewState
export const selectGraphMemoryStats = (state: RootState) => state.graphMemory.stats

// 操作历史选择器
export const selectGraphMemoryHistory = (state: RootState) => state.graphMemory.history

// 批量操作选择器
export const selectBatchOperation = (state: RootState) => state.graphMemory.batchOperation

// 复合选择器
export const selectIsGraphMemoryLoading = (state: RootState) =>
  state.graphMemory.status === 'loading'

export const selectHasGraphMemoryError = (state: RootState) =>
  state.graphMemory.status === 'failed' && state.graphMemory.error !== null

export const selectGraphMemoryIsEmpty = (state: RootState) =>
  state.graphMemory.nodes.length === 0 && state.graphMemory.edges.length === 0

export const selectSelectedNodesCount = (state: RootState) =>
  state.graphMemory.selectedNodeIds.length

export const selectSelectedEdgesCount = (state: RootState) =>
  state.graphMemory.selectedEdgeIds.length

export const selectHasSelection = (state: RootState) =>
  state.graphMemory.selectedNodeIds.length > 0 || state.graphMemory.selectedEdgeIds.length > 0

// 探索模式选择器
export const selectExplorationMode = (state: RootState) => state.graphMemory.explorationMode
export const selectIsExplorationModeActive = (state: RootState) => state.graphMemory.explorationMode.isActive
export const selectFocusNodeId = (state: RootState) => state.graphMemory.explorationMode.focusNodeId
export const selectExplorationNodes = (state: RootState) => state.graphMemory.explorationMode.explorationNodes
export const selectExplorationEdges = (state: RootState) => state.graphMemory.explorationMode.explorationEdges
export const selectExplorationDepth = (state: RootState) => state.graphMemory.explorationMode.explorationDepth

// 复合探索模式选择器
export const selectFocusNode = (state: RootState) => {
  const focusNodeId = state.graphMemory.explorationMode.focusNodeId
  if (!focusNodeId) return null

  if (state.graphMemory.explorationMode.isActive) {
    return state.graphMemory.explorationMode.explorationNodes.find(node => node.id === focusNodeId) || null
  }

  return state.graphMemory.nodes.find(node => node.id === focusNodeId) || null
}

export const selectExplorationStats = (state: RootState) => {
  if (!state.graphMemory.explorationMode.isActive) return null

  return {
    totalNodes: state.graphMemory.explorationMode.explorationNodes.length,
    totalEdges: state.graphMemory.explorationMode.explorationEdges.length,
    focusNodeId: state.graphMemory.explorationMode.focusNodeId,
    explorationDepth: state.graphMemory.explorationMode.explorationDepth
  }
}
