#!/usr/bin/env node

/**
 * Debug script to test mem0 API connectivity and data transformation
 */

const axios = require('axios');

const MEM0_API_URL = 'http://localhost:8000';
const USER_ID = 'default';

async function testMem0API() {
  console.log('🔍 Testing Mem0 API connectivity and data transformation...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health endpoint...');
    try {
      const healthResponse = await axios.get(`${MEM0_API_URL}/health`);
      console.log('✅ Health check passed:', healthResponse.status);
    } catch (error) {
      console.log('❌ Health check failed:', error.message);
    }

    // Test 2: Get memories
    console.log('\n2. Testing memories endpoint...');
    try {
      const memoriesResponse = await axios.get(`${MEM0_API_URL}/v1/memories/?user_id=${USER_ID}&limit=5`);
      console.log('✅ Memories API response status:', memoriesResponse.status);
      console.log('📊 Response data structure:');
      
      if (Array.isArray(memoriesResponse.data)) {
        console.log('   - Response is array with', memoriesResponse.data.length, 'items');
        if (memoriesResponse.data.length > 0) {
          const firstMemory = memoriesResponse.data[0];
          console.log('   - First memory structure:');
          console.log('     * id:', firstMemory.id);
          console.log('     * memory:', firstMemory.memory);
          console.log('     * metadata:', JSON.stringify(firstMemory.metadata, null, 2));
          console.log('     * categories in metadata:', firstMemory.metadata?.categories);
          console.log('     * direct categories field:', firstMemory.categories);
          console.log('     * created_at:', firstMemory.created_at);
          console.log('     * user_id:', firstMemory.user_id);
        }
      } else {
        console.log('   - Response structure:', Object.keys(memoriesResponse.data));
      }
    } catch (error) {
      console.log('❌ Memories API failed:', error.message);
      if (error.response) {
        console.log('   - Status:', error.response.status);
        console.log('   - Data:', error.response.data);
      }
    }

    // Test 3: Get stats
    console.log('\n3. Testing stats endpoint...');
    try {
      const statsResponse = await axios.get(`${MEM0_API_URL}/v1/stats?user_id=${USER_ID}`);
      console.log('✅ Stats API response status:', statsResponse.status);
      console.log('📊 Stats data:', JSON.stringify(statsResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Stats API failed:', error.message);
    }

    // Test 4: Test data transformation
    console.log('\n4. Testing data transformation...');
    try {
      const memoriesResponse = await axios.get(`${MEM0_API_URL}/v1/memories/?user_id=${USER_ID}&limit=1`);
      if (Array.isArray(memoriesResponse.data) && memoriesResponse.data.length > 0) {
        const mem0Memory = memoriesResponse.data[0];
        
        // Simulate the conversion function from useMemoriesApi.ts
        const convertedMemory = {
          id: mem0Memory.id,
          memory: mem0Memory.text || mem0Memory.memory,
          created_at: mem0Memory.created_at ? new Date(mem0Memory.created_at).getTime() : Date.now(),
          state: mem0Memory.state || 'active',
          metadata: mem0Memory.metadata || {},
          categories: mem0Memory.categories || [], // This might be the issue!
          client: 'api',
          app_name: mem0Memory.app_name || 'unknown'
        };
        
        console.log('🔄 Original mem0 data:');
        console.log('   - categories field:', mem0Memory.categories);
        console.log('   - metadata.categories:', mem0Memory.metadata?.categories);
        
        console.log('🔄 Converted data:');
        console.log('   - categories:', convertedMemory.categories);
        console.log('   - Should be:', mem0Memory.metadata?.categories);
        
        // Check if categories are being lost
        if (!convertedMemory.categories || convertedMemory.categories.length === 0) {
          if (mem0Memory.metadata?.categories && mem0Memory.metadata.categories.length > 0) {
            console.log('❌ ISSUE FOUND: Categories are in metadata but not being extracted!');
            console.log('   - Correct categories should be:', mem0Memory.metadata.categories);
          }
        }
      }
    } catch (error) {
      console.log('❌ Data transformation test failed:', error.message);
    }

  } catch (error) {
    console.log('❌ General error:', error.message);
  }
}

// Run the test
testMem0API().then(() => {
  console.log('\n🏁 Debug test completed');
}).catch(error => {
  console.error('💥 Debug test failed:', error);
});
