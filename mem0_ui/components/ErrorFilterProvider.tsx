'use client';

import { useEffect } from 'react';
import { setupErrorFilter } from '@/lib/errorFilter';

interface ErrorFilterProviderProps {
  children: React.ReactNode;
}

/**
 * ErrorFilterProvider - 设置开发环境的错误过滤器
 * 用于过滤已知的无害错误，保持开发体验的清洁
 */
export function ErrorFilterProvider({ children }: ErrorFilterProviderProps) {
  useEffect(() => {
    // 只在开发环境中设置错误过滤器
    if (process.env.NODE_ENV === 'development') {
      setupErrorFilter();
    }
  }, []);

  return <>{children}</>;
}
