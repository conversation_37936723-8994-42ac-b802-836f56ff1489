/**
 * 全局错误处理Provider
 * 
 * 提供全应用的错误处理上下文：
 * - 全局错误状态管理
 * - 统一的错误处理接口
 * - 错误恢复机制
 * - 错误统计和监控
 */

'use client';

import React, { createContext, useContext, useCallback, useEffect, useState, ReactNode } from 'react';
import { toast } from 'sonner';

import { useErrorHandler } from '@/hooks/useErrorHandler';
import { cleanupOldErrors, getErrorStats, ErrorStats } from '@/lib/errorReporting';
import { parseError } from '@/utils/errorUtils';

interface ErrorContextValue {
  // 错误状态
  globalError: string | null;
  hasGlobalError: boolean;
  errorStats: ErrorStats | null;
  
  // 错误处理方法
  handleError: (error: unknown, context?: Record<string, any>) => void;
  handleApiError: (error: unknown, operation: string) => void;
  handleNetworkError: (error: unknown) => void;
  handleValidationError: (error: unknown, field?: string) => void;
  clearGlobalError: () => void;
  
  // 错误恢复
  retryOperation: (operation: () => Promise<void>) => Promise<void>;
  
  // 错误统计
  refreshErrorStats: () => void;
}

const ErrorContext = createContext<ErrorContextValue | undefined>(undefined);

interface ErrorProviderProps {
  children: ReactNode;
}

export function ErrorProvider({ children }: ErrorProviderProps) {
  const [errorStats, setErrorStats] = useState<ErrorStats | null>(null);
  const {
    error: globalError,
    isError: hasGlobalError,
    handleError,
    handleApiError,
    handleNetworkError,
    handleValidationError,
    clearError: clearGlobalError,
    retryOperation
  } = useErrorHandler();

  // 刷新错误统计
  const refreshErrorStats = useCallback(() => {
    try {
      const stats = getErrorStats();
      setErrorStats(stats);
    } catch (error) {
      console.warn('Failed to refresh error stats:', error);
    }
  }, []);

  // 全局错误监听器
  useEffect(() => {
    // 监听未捕获的Promise拒绝
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      handleError(event.reason, { type: 'unhandled_promise_rejection' });
      
      // 阻止默认的控制台错误输出
      event.preventDefault();
    };

    // 监听全局JavaScript错误
    const handleGlobalError = (event: ErrorEvent) => {
      console.error('Global error:', event.error);
      handleError(event.error, { 
        type: 'global_error',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    };

    // 监听资源加载错误
    const handleResourceError = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target && target.tagName) {
        const errorMessage = `Failed to load ${target.tagName.toLowerCase()}: ${
          (target as any).src || (target as any).href || 'unknown resource'
        }`;
        handleError(new Error(errorMessage), { 
          type: 'resource_error',
          tagName: target.tagName,
          src: (target as any).src,
          href: (target as any).href
        });
      }
    };

    // 添加事件监听器
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleGlobalError);
    window.addEventListener('error', handleResourceError, true); // 捕获阶段

    // 定期清理旧错误日志
    const cleanupInterval = setInterval(() => {
      cleanupOldErrors();
      refreshErrorStats();
    }, 60000); // 每分钟清理一次

    // 初始化错误统计
    refreshErrorStats();

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('error', handleResourceError, true);
      clearInterval(cleanupInterval);
    };
  }, [handleError, refreshErrorStats]);

  // 监听网络状态变化
  useEffect(() => {
    const handleOnline = () => {
      toast.success('网络连接已恢复');
      clearGlobalError();
    };

    const handleOffline = () => {
      toast.error('网络连接已断开，请检查您的网络设置');
      handleNetworkError(new Error('Network connection lost'));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [handleNetworkError, clearGlobalError]);

  const contextValue: ErrorContextValue = {
    globalError,
    hasGlobalError,
    errorStats,
    handleError,
    handleApiError,
    handleNetworkError,
    handleValidationError,
    clearGlobalError,
    retryOperation,
    refreshErrorStats
  };

  return (
    <ErrorContext.Provider value={contextValue}>
      {children}
    </ErrorContext.Provider>
  );
}

/**
 * 使用错误处理上下文的Hook
 */
export function useErrorContext(): ErrorContextValue {
  const context = useContext(ErrorContext);
  if (context === undefined) {
    throw new Error('useErrorContext must be used within an ErrorProvider');
  }
  return context;
}

/**
 * 高阶组件：为组件添加错误处理能力
 */
export function withErrorHandling<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  return function WrappedComponent(props: P) {
    const { handleError } = useErrorContext();

    // 包装组件的错误处理
    const handleComponentError = useCallback((error: Error, errorInfo: any) => {
      handleError(error, {
        component: Component.displayName || Component.name,
        errorInfo,
        type: 'component_error'
      });
    }, [handleError]);

    return (
      <ErrorBoundary onError={handleComponentError}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
}

// 导入ErrorBoundary组件
import ErrorBoundary from '@/components/common/ErrorBoundary';
