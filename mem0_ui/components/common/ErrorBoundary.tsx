'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { logError } from '@/lib/errorReporting';
import { parseError, createErrorBoundaryInfo } from '@/utils/errorUtils';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showErrorDetails?: boolean;
  resetOnPropsChange?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

class ErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: number | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // 使用统一的错误处理系统
    const parsedError = parseError(error);
    const errorId = logError({
      message: error.message,
      category: parsedError.category,
      severity: parsedError.severity,
      stack: error.stack,
      context: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
        ...createErrorBoundaryInfo(error, errorInfo)
      }
    });

    // 更新错误ID
    this.setState(prevState => ({
      ...prevState,
      errorId
    }));

    // 调用外部错误处理器
    this.props.onError?.(error, errorInfo);

    // 在开发环境中打印详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Boundary Caught Error');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Parsed Error:', parsedError);
      console.error('Error ID:', errorId);
      console.error('Component Stack:', errorInfo.componentStack);
      console.groupEnd();
    }
  }

  componentDidUpdate(prevProps: Props) {
    const { resetOnPropsChange } = this.props;
    const { hasError } = this.state;

    // 如果props发生变化且设置了resetOnPropsChange，重置错误状态
    if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) {
      this.resetErrorBoundary();
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  // 移除旧的sendErrorReport方法，现在使用统一的错误报告系统

  resetErrorBoundary = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  handleRetry = () => {
    this.resetErrorBoundary();
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  getSeverityLevel = (error: Error): 'low' | 'medium' | 'high' => {
    const parsedError = parseError(error);
    return parsedError.severity === 'critical' ? 'high' : parsedError.severity;
  };

  getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-yellow-500';
      case 'medium':
        return 'bg-orange-500';
      case 'high':
        return 'bg-red-500';
      default:
        return 'bg-zinc-500';
    }
  };

  renderErrorUI = () => {
    const { error, errorInfo, errorId } = this.state;
    const { showErrorDetails = false } = this.props;
    
    if (!error) return null;

    const severity = this.getSeverityLevel(error);
    const isNetworkError = error.message.toLowerCase().includes('network') || 
                          error.message.toLowerCase().includes('fetch');

    return (
      <div className="flex items-center justify-center min-h-[400px] p-6">
        <Card className="w-full max-w-2xl bg-zinc-900 border-zinc-800">
          <CardHeader>
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-red-500/20 rounded-full">
                <AlertTriangle className="h-6 w-6 text-red-500" />
              </div>
              <div className="flex-1">
                <CardTitle className="text-white">
                  {isNetworkError ? 'Network Error' : 'Something went wrong'}
                </CardTitle>
                <p className="text-zinc-400 text-sm mt-1">
                  {isNetworkError 
                    ? 'Unable to connect to the server. Please check your connection.'
                    : 'An unexpected error occurred while loading this page.'
                  }
                </p>
              </div>
              <Badge className={`${this.getSeverityColor(severity)} text-white`}>
                {severity.toUpperCase()}
              </Badge>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* 错误信息 */}
            <div className="p-3 bg-zinc-800 rounded-lg">
              <p className="text-sm text-zinc-300 font-mono">
                {error.message}
              </p>
              {errorId && (
                <p className="text-xs text-zinc-500 mt-2">
                  Error ID: {errorId}
                </p>
              )}
            </div>

            {/* 操作按钮 */}
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={this.handleRetry}
                className="bg-[#00d4aa] hover:bg-[#00c49a] text-black"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
              
              <Button
                onClick={this.handleReload}
                variant="outline"
                className="border-zinc-700 hover:border-zinc-600"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Reload Page
              </Button>
              
              <Button
                onClick={this.handleGoHome}
                variant="outline"
                className="border-zinc-700 hover:border-zinc-600"
              >
                <Home className="w-4 h-4 mr-2" />
                Go Home
              </Button>
            </div>

            {/* 错误详情（开发环境或显式启用时） */}
            {(showErrorDetails || process.env.NODE_ENV === 'development') && errorInfo && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm text-zinc-400 hover:text-zinc-300 flex items-center gap-2">
                  <Bug className="w-4 h-4" />
                  Technical Details
                </summary>
                <div className="mt-2 p-3 bg-zinc-800 rounded-lg">
                  <div className="text-xs text-zinc-400 space-y-2">
                    <div>
                      <strong>Stack Trace:</strong>
                      <pre className="mt-1 text-xs overflow-auto max-h-32 text-zinc-500">
                        {error.stack}
                      </pre>
                    </div>
                    <div>
                      <strong>Component Stack:</strong>
                      <pre className="mt-1 text-xs overflow-auto max-h-32 text-zinc-500">
                        {errorInfo.componentStack}
                      </pre>
                    </div>
                  </div>
                </div>
              </details>
            )}

            {/* 帮助信息 */}
            <div className="text-xs text-zinc-500 text-center">
              If this problem persists, please contact support with the Error ID above.
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // 否则使用默认错误UI
      return this.renderErrorUI();
    }

    // 正常渲染子组件
    return this.props.children;
  }
}

export default ErrorBoundary;
