'use client';

import React from 'react';
import { Loader2, Brain, Activity, Database } from 'lucide-react';

import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'brain' | 'activity' | 'database';
  text?: string;
  className?: string;
  fullScreen?: boolean;
  overlay?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  text,
  className,
  fullScreen = false,
  overlay = false
}) => {
  // 尺寸映射
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  // 文本尺寸映射
  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  };

  // 图标组件映射
  const iconComponents = {
    default: Loader2,
    brain: Brain,
    activity: Activity,
    database: Database
  };

  const IconComponent = iconComponents[variant];

  // 加载动画类
  const animationClass = variant === 'default' ? 'animate-spin' : 'animate-pulse';

  // 渲染加载器内容
  const renderSpinner = () => (
    <div className={cn(
      'flex flex-col items-center justify-center gap-3',
      className
    )}>
      <div className="relative">
        <IconComponent 
          className={cn(
            sizeClasses[size],
            'text-[#00d4aa]',
            animationClass
          )}
        />
        {variant !== 'default' && (
          <div className="absolute inset-0">
            <Loader2 
              className={cn(
                sizeClasses[size],
                'text-[#00d4aa]/30 animate-spin'
              )}
            />
          </div>
        )}
      </div>
      
      {text && (
        <p className={cn(
          'text-zinc-400 font-medium',
          textSizeClasses[size]
        )}>
          {text}
        </p>
      )}
    </div>
  );

  // 全屏模式
  if (fullScreen) {
    return (
      <div className={cn(
        'fixed inset-0 z-50 flex items-center justify-center',
        overlay ? 'bg-zinc-950/80 backdrop-blur-sm' : 'bg-zinc-950'
      )}>
        {renderSpinner()}
      </div>
    );
  }

  return renderSpinner();
};

// 预设的加载组件变体
export const PageLoader: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <LoadingSpinner 
    size="lg" 
    variant="brain" 
    text={text}
    className="min-h-[400px]"
  />
);

export const InlineLoader: React.FC<{ text?: string }> = ({ text }) => (
  <LoadingSpinner 
    size="sm" 
    variant="default" 
    text={text}
    className="py-2"
  />
);

export const OverlayLoader: React.FC<{ text?: string }> = ({ text = 'Processing...' }) => (
  <LoadingSpinner 
    size="lg" 
    variant="activity" 
    text={text}
    fullScreen
    overlay
  />
);

export const DataLoader: React.FC<{ text?: string }> = ({ text = 'Loading data...' }) => (
  <LoadingSpinner 
    size="md" 
    variant="database" 
    text={text}
    className="py-8"
  />
);

// 骨架屏加载组件
export const SkeletonLoader: React.FC<{
  lines?: number;
  className?: string;
}> = ({ lines = 3, className }) => (
  <div className={cn('space-y-3', className)}>
    {Array.from({ length: lines }, (_, i) => (
      <div
        key={i}
        className={cn(
          'h-4 bg-zinc-800 rounded animate-pulse',
          i === lines - 1 ? 'w-3/4' : 'w-full'
        )}
      />
    ))}
  </div>
);

// 卡片骨架屏
export const CardSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn(
    'p-6 bg-zinc-900 border border-zinc-800 rounded-lg space-y-4',
    className
  )}>
    <div className="flex items-center space-x-3">
      <div className="w-10 h-10 bg-zinc-800 rounded-full animate-pulse" />
      <div className="space-y-2 flex-1">
        <div className="h-4 bg-zinc-800 rounded animate-pulse w-1/3" />
        <div className="h-3 bg-zinc-800 rounded animate-pulse w-1/2" />
      </div>
    </div>
    <SkeletonLoader lines={2} />
  </div>
);

// 表格骨架屏
export const TableSkeleton: React.FC<{ 
  rows?: number; 
  columns?: number;
  className?: string;
}> = ({ rows = 5, columns = 4, className }) => (
  <div className={cn('space-y-3', className)}>
    {/* 表头 */}
    <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
      {Array.from({ length: columns }, (_, i) => (
        <div key={i} className="h-4 bg-zinc-700 rounded animate-pulse" />
      ))}
    </div>
    
    {/* 表格行 */}
    {Array.from({ length: rows }, (_, rowIndex) => (
      <div 
        key={rowIndex} 
        className="grid gap-4" 
        style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
      >
        {Array.from({ length: columns }, (_, colIndex) => (
          <div 
            key={colIndex} 
            className="h-4 bg-zinc-800 rounded animate-pulse" 
          />
        ))}
      </div>
    ))}
  </div>
);

// 统计卡片骨架屏
export const StatCardSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn(
    'p-6 bg-zinc-900 border border-zinc-800 rounded-lg',
    className
  )}>
    <div className="flex items-center justify-between mb-4">
      <div className="w-6 h-6 bg-zinc-800 rounded animate-pulse" />
      <div className="w-4 h-4 bg-zinc-800 rounded animate-pulse" />
    </div>
    <div className="space-y-2">
      <div className="h-8 bg-zinc-800 rounded animate-pulse w-1/2" />
      <div className="h-4 bg-zinc-800 rounded animate-pulse w-3/4" />
    </div>
  </div>
);

// 进度条加载器
export const ProgressLoader: React.FC<{
  progress?: number;
  text?: string;
  className?: string;
}> = ({ progress = 0, text, className }) => (
  <div className={cn('space-y-3', className)}>
    {text && (
      <p className="text-sm text-zinc-400 text-center">{text}</p>
    )}
    <div className="w-full bg-zinc-800 rounded-full h-2">
      <div 
        className="bg-[#00d4aa] h-2 rounded-full transition-all duration-300 ease-out"
        style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
      />
    </div>
    {progress > 0 && (
      <p className="text-xs text-zinc-500 text-center">
        {Math.round(progress)}% complete
      </p>
    )}
  </div>
);

export default LoadingSpinner;
