'use client';

import React, { useState } from 'react';
import { Plus, Edit, Trash2, Eye, Copy, Tag, Folder } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import { useConfig } from '@/hooks/useConfig';
import { InstructionTemplate, InstructionCategory } from '@/store/configSlice';

interface InstructionTemplateEditorProps {
  template: InstructionTemplate | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (template: Omit<InstructionTemplate, 'id' | 'createdAt' | 'updatedAt'>) => void;
  categories: InstructionCategory[];
}

const InstructionTemplateEditor: React.FC<InstructionTemplateEditorProps> = ({
  template,
  isOpen,
  onClose,
  onSave,
  categories
}) => {
  const [formData, setFormData] = useState({
    name: template?.name || '',
    description: template?.description || '',
    content: template?.content || '',
    category: template?.category || 'general',
    tags: template?.tags?.join(', ') || '',
    isActive: template?.isActive ?? true
  });

  const handleSave = () => {
    if (!formData.name.trim() || !formData.content.trim()) {
      return;
    }

    onSave({
      name: formData.name.trim(),
      description: formData.description.trim(),
      content: formData.content.trim(),
      category: formData.category,
      tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
      isActive: formData.isActive
    });

    onClose();
  };

  const handleClose = () => {
    setFormData({
      name: '',
      description: '',
      content: '',
      category: 'general',
      tags: '',
      isActive: true
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-zinc-900 border-zinc-800 max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Edit className="w-5 h-5 text-[#00d4aa]" />
            {template ? '编辑指令模板' : '创建指令模板'}
          </DialogTitle>
          <DialogDescription className="text-zinc-400">
            创建或编辑可重用的指令模板
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name" className="text-zinc-400">模板名称</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="输入模板名称"
                className="bg-zinc-800 border-zinc-700 text-white"
              />
            </div>
            <div>
              <Label htmlFor="category" className="text-zinc-400">分类</Label>
              <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                <SelectTrigger className="bg-zinc-800 border-zinc-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-zinc-800 border-zinc-700">
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description" className="text-zinc-400">描述</Label>
            <Input
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="简要描述这个模板的用途"
              className="bg-zinc-800 border-zinc-700 text-white"
            />
          </div>

          <div>
            <Label htmlFor="content" className="text-zinc-400">指令内容</Label>
            <Textarea
              id="content"
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              placeholder="输入指令内容，可以使用 {{变量名}} 作为占位符"
              rows={6}
              className="bg-zinc-800 border-zinc-700 text-white"
            />
            <p className="text-xs text-zinc-500 mt-1">
              提示：使用 {`{{变量名}}`} 创建可替换的占位符
            </p>
          </div>

          <div>
            <Label htmlFor="tags" className="text-zinc-400">标签</Label>
            <Input
              id="tags"
              value={formData.tags}
              onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
              placeholder="用逗号分隔多个标签"
              className="bg-zinc-800 border-zinc-700 text-white"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
            />
            <Label htmlFor="isActive" className="text-zinc-400">启用模板</Label>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            className="border-zinc-700"
          >
            取消
          </Button>
          <Button
            onClick={handleSave}
            disabled={!formData.name.trim() || !formData.content.trim()}
            className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-black"
          >
            {template ? '更新' : '创建'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

interface InstructionPreviewProps {
  template: InstructionTemplate | null;
  isOpen: boolean;
  onClose: () => void;
  onPreview: (template: string, variables?: Record<string, string>) => string;
}

const InstructionPreview: React.FC<InstructionPreviewProps> = ({
  template,
  isOpen,
  onClose,
  onPreview
}) => {
  const { toast } = useToast();
  const [variables, setVariables] = useState<Record<string, string>>({});
  const [previewResult, setPreviewResult] = useState('');

  React.useEffect(() => {
    if (template && isOpen) {
      // 提取模板中的变量
      const variableMatches = template.content.match(/\{\{\s*(\w+)\s*\}\}/g);
      const extractedVars: Record<string, string> = {};
      
      if (variableMatches) {
        variableMatches.forEach(match => {
          const varName = match.replace(/\{\{\s*|\s*\}\}/g, '');
          if (!extractedVars[varName]) {
            extractedVars[varName] = '';
          }
        });
      }
      
      setVariables(extractedVars);
      setPreviewResult(onPreview(template.content, extractedVars));
    }
  }, [template, isOpen, onPreview]);

  const handleVariableChange = (varName: string, value: string) => {
    const newVariables = { ...variables, [varName]: value };
    setVariables(newVariables);
    if (template) {
      setPreviewResult(onPreview(template.content, newVariables));
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(previewResult);
    toast({
      title: "已复制",
      description: "指令内容已复制到剪贴板",
      variant: "default"
    });
  };

  if (!template) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-zinc-900 border-zinc-800 max-w-3xl">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center gap-2">
            <Eye className="w-5 h-5 text-[#00d4aa]" />
            预览指令模板 - {template.name}
          </DialogTitle>
          <DialogDescription className="text-zinc-400">
            设置变量值并预览最终的指令内容
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {Object.keys(variables).length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-zinc-400 mb-2">变量设置</h3>
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(variables).map(([varName, value]) => (
                  <div key={varName}>
                    <Label htmlFor={varName} className="text-zinc-400">{varName}</Label>
                    <Input
                      id={varName}
                      value={value}
                      onChange={(e) => handleVariableChange(varName, e.target.value)}
                      placeholder={`输入 ${varName} 的值`}
                      className="bg-zinc-800 border-zinc-700 text-white"
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          <div>
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-zinc-400">预览结果</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyToClipboard}
                className="border-zinc-700 hover:border-zinc-600"
              >
                <Copy className="w-4 h-4 mr-2" />
                复制
              </Button>
            </div>
            <Textarea
              value={previewResult}
              readOnly
              rows={8}
              className="bg-zinc-800 border-zinc-700 text-white"
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            className="border-zinc-700"
          >
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default function InstructionManager() {
  const { toast } = useToast();
  const {
    instructionTemplates,
    instructionCategories,
    createInstructionTemplate,
    updateInstructionTemplateById,
    deleteInstructionTemplateById,
    previewInstruction
  } = useConfig();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showEditor, setShowEditor] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<InstructionTemplate | null>(null);
  const [previewingTemplate, setPreviewingTemplate] = useState<InstructionTemplate | null>(null);

  // 过滤模板
  const filteredTemplates = instructionTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    setShowEditor(true);
  };

  const handleEditTemplate = (template: InstructionTemplate) => {
    setEditingTemplate(template);
    setShowEditor(true);
  };

  const handleSaveTemplate = (templateData: Omit<InstructionTemplate, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (editingTemplate) {
      updateInstructionTemplateById(editingTemplate.id, templateData);
      toast({
        title: "成功",
        description: "指令模板已更新",
        variant: "default"
      });
    } else {
      createInstructionTemplate(templateData);
      toast({
        title: "成功",
        description: "指令模板已创建",
        variant: "default"
      });
    }
  };

  const handleDeleteTemplate = (template: InstructionTemplate) => {
    deleteInstructionTemplateById(template.id);
    toast({
      title: "成功",
      description: "指令模板已删除",
      variant: "default"
    });
  };

  const handlePreviewTemplate = (template: InstructionTemplate) => {
    setPreviewingTemplate(template);
    setShowPreview(true);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">指令管理</h2>
          <p className="text-zinc-400 mt-1">
            创建和管理可重用的指令模板
          </p>
        </div>
        <Button
          onClick={handleCreateTemplate}
          className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-black"
        >
          <Plus className="w-4 h-4 mr-2" />
          创建模板
        </Button>
      </div>

      {/* 搜索和过滤 */}
      <Card className="bg-zinc-900 border-zinc-800">
        <CardContent className="p-4">
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <Input
                placeholder="搜索模板名称、描述或标签..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="bg-zinc-800 border-zinc-700 text-white"
              />
            </div>

            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48 bg-zinc-800 border-zinc-700 text-white">
                <Folder className="w-4 h-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-zinc-800 border-zinc-700">
                <SelectItem value="all">所有分类</SelectItem>
                {instructionCategories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-zinc-400">总模板数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{instructionTemplates.length}</div>
          </CardContent>
        </Card>

        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-zinc-400">活跃模板</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {instructionTemplates.filter(t => t.isActive).length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-zinc-400">分类数量</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{instructionCategories.length}</div>
          </CardContent>
        </Card>

        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-zinc-400">当前筛选</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{filteredTemplates.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* 模板列表 */}
      <Card className="bg-zinc-900 border-zinc-800">
        <CardHeader>
          <CardTitle className="text-white">指令模板</CardTitle>
          <CardDescription className="text-zinc-400">
            管理您的指令模板库
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredTemplates.length === 0 ? (
            <div className="text-center py-8 text-zinc-400">
              {searchTerm || selectedCategory !== 'all' ? '未找到匹配的模板' : '暂无指令模板'}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredTemplates.map((template) => {
                const category = instructionCategories.find(c => c.id === template.category);

                return (
                  <div
                    key={template.id}
                    className="p-4 rounded-lg bg-zinc-800 hover:bg-zinc-700 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-medium text-white">{template.name}</h3>
                          {!template.isActive && (
                            <Badge variant="secondary" className="bg-zinc-700 text-zinc-300">
                              未启用
                            </Badge>
                          )}
                          {category && (
                            <Badge
                              variant="secondary"
                              className="bg-zinc-700 text-zinc-300"
                              style={{ backgroundColor: `${category.color}20`, color: category.color }}
                            >
                              {category.name}
                            </Badge>
                          )}
                        </div>

                        <p className="text-sm text-zinc-400 mb-2">{template.description}</p>

                        <div className="flex items-center gap-2 mb-2">
                          {template.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="border-zinc-600 text-zinc-300">
                              <Tag className="w-3 h-3 mr-1" />
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        <p className="text-xs text-zinc-500">
                          创建于 {new Date(template.createdAt).toLocaleDateString()} •
                          更新于 {new Date(template.updatedAt).toLocaleDateString()}
                        </p>
                      </div>

                      <div className="flex gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePreviewTemplate(template)}
                          className="border-zinc-700 hover:border-zinc-600"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditTemplate(template)}
                          className="border-zinc-700 hover:border-zinc-600"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteTemplate(template)}
                          className="border-zinc-700 hover:border-red-600 text-red-400"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 模态框 */}
      <InstructionTemplateEditor
        template={editingTemplate}
        isOpen={showEditor}
        onClose={() => setShowEditor(false)}
        onSave={handleSaveTemplate}
        categories={instructionCategories}
      />

      <InstructionPreview
        template={previewingTemplate}
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        onPreview={previewInstruction}
      />
    </div>
  );
}
