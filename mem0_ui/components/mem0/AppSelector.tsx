import React, { useState } from 'react';
import { Layers, Plus, Trash2, Package } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { useAppManagement } from '@/hooks/useAppManagement';

interface AppSelectorProps {
  value?: string | undefined;
  onChange?: ((runId: string) => void) | undefined;
  className?: string;
}

const AppSelector: React.FC<AppSelectorProps> = ({
  value,
  onChange,
  className
}) => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [newRunId, setNewRunId] = useState('');
  const [newAppName, setNewAppName] = useState('');
  const [newAppDescription, setNewAppDescription] = useState('');
  const [appToDelete, setAppToDelete] = useState<string>('');
  
  const { toast } = useToast();
  const {
    apps,
    currentRunId,
    isLoading,
    setCurrentRunId,
    createApp,
    deleteApp
  } = useAppManagement();

  const handleAppChange = (runId: string) => {
    setCurrentRunId(runId);
    onChange?.(runId);
  };

  const handleCreateApp = async () => {
    if (!newRunId.trim()) {
      toast({
        title: "错误",
        description: "请输入运行ID",
        variant: "destructive"
      });
      return;
    }

    try {
      await createApp(
        newRunId.trim(), 
        newAppName.trim() || undefined,
        newAppDescription.trim() || undefined
      );
      toast({
        title: "成功",
        description: "应用创建成功",
        variant: "default"
      });
      setNewRunId('');
      setNewAppName('');
      setNewAppDescription('');
      setShowCreateDialog(false);
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "创建应用失败",
        variant: "destructive"
      });
    }
  };

  const handleDeleteApp = async () => {
    if (!appToDelete) return;

    try {
      await deleteApp(appToDelete);
      toast({
        title: "成功",
        description: "应用删除成功",
        variant: "default"
      });
      setAppToDelete('');
      setShowDeleteDialog(false);
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "删除应用失败",
        variant: "destructive"
      });
    }
  };

  const selectedApp = apps.find(app => app.id === (value || currentRunId));

  return (
    <>
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center justify-between">
          <Label className="text-zinc-400 flex items-center gap-2">
            <Layers className="w-4 h-4" />
            应用管理
          </Label>
          <div className="flex gap-1">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowCreateDialog(true)}
              className="h-6 w-6 p-0 border-zinc-700 hover:border-zinc-600"
              disabled={isLoading}
            >
              <Plus className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                if (selectedApp && selectedApp.id !== 'default') {
                  setAppToDelete(selectedApp.id);
                  setShowDeleteDialog(true);
                } else {
                  toast({
                    title: "提示",
                    description: "无法删除默认应用",
                    variant: "default"
                  });
                }
              }}
              className="h-6 w-6 p-0 border-zinc-700 hover:border-zinc-600"
              disabled={isLoading || !selectedApp || selectedApp.id === 'default'}
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </div>
        
        <Select 
          value={value || currentRunId || ''} 
          onValueChange={handleAppChange}
          disabled={isLoading}
        >
          <SelectTrigger className="bg-zinc-800 border-zinc-700 text-white">
            <SelectValue placeholder="选择应用">
              {selectedApp && (
                <div className="flex items-center gap-2">
                  <Package className="w-4 h-4 text-[#00d4aa]" />
                  <span>{selectedApp.name}</span>
                  <span className="text-xs text-zinc-400">
                    ({selectedApp.memory_count} 记忆)
                  </span>
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="bg-zinc-800 border-zinc-700">
            {apps.map((app) => (
              <SelectItem key={app.id} value={app.id}>
                <div className="flex items-center gap-2">
                  <Package className="w-4 h-4 text-[#00d4aa]" />
                  <div className="flex flex-col">
                    <span>{app.name}</span>
                    {app.description && (
                      <span className="text-xs text-zinc-500">{app.description}</span>
                    )}
                  </div>
                  <span className="text-xs text-zinc-400">
                    ({app.memory_count} 记忆)
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 创建应用对话框 */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="bg-zinc-900 border-zinc-800">
          <DialogHeader>
            <DialogTitle className="text-white">创建新应用</DialogTitle>
            <DialogDescription className="text-zinc-400">
              输入运行ID和应用信息。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-zinc-400">运行ID *</Label>
              <Input
                value={newRunId}
                onChange={(e) => setNewRunId(e.target.value)}
                placeholder="输入唯一的运行ID"
                className="bg-zinc-800 border-zinc-700 text-white"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-zinc-400">应用名称</Label>
              <Input
                value={newAppName}
                onChange={(e) => setNewAppName(e.target.value)}
                placeholder="输入应用显示名称（可选）"
                className="bg-zinc-800 border-zinc-700 text-white"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-zinc-400">应用描述</Label>
              <Textarea
                value={newAppDescription}
                onChange={(e) => setNewAppDescription(e.target.value)}
                placeholder="输入应用描述（可选）"
                className="bg-zinc-800 border-zinc-700 text-white"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              className="border-zinc-700"
            >
              取消
            </Button>
            <Button
              onClick={handleCreateApp}
              disabled={isLoading || !newRunId.trim()}
              className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-black"
            >
              {isLoading ? '创建中...' : '创建应用'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除应用确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="bg-zinc-900 border-zinc-800">
          <DialogHeader>
            <DialogTitle className="text-white">确认删除应用</DialogTitle>
            <DialogDescription className="text-zinc-400">
              此操作将删除应用 &quot;{appToDelete}&quot; 及其所有记忆数据，此操作不可撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              className="border-zinc-700"
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteApp}
              disabled={isLoading}
            >
              {isLoading ? '删除中...' : '确认删除'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AppSelector;
