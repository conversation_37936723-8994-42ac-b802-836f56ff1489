import React, { useState } from 'react';
import {
  Plus,
  Search,
  Download,
  Trash2,
  Refresh<PERSON>w,
  Layers,
  AlertCircle
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';

import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { realMem0Client } from '@/lib/mem0-client';

import UserSelector from './UserSelector';
import AppSelector from './AppSelector';
import AgentSelector from './AgentSelector';

interface QuickActionsProps {
  currentUserId?: string;
  currentRunId?: string;
  currentAgentId?: string;
  onUserChange?: (userId: string) => void;
  onRunChange?: (runId: string) => void;
  onAgentChange?: (agentId: string) => void;
  onRefresh?: () => void;
}

interface ActionButtonProps {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  onClick: () => void;
  variant?: 'default' | 'destructive';
  disabled?: boolean;
}

const ActionButton: React.FC<ActionButtonProps> = ({ 
  icon: Icon, 
  label, 
  onClick, 
  variant = 'default',
  disabled = false 
}) => (
  <Button
    variant={variant === 'destructive' ? 'destructive' : 'outline'}
    className={`h-20 flex-col gap-2 border-zinc-800 hover:border-zinc-700 ${
      variant === 'default' ? 'hover:bg-[#00d4aa]/10 hover:text-[#00d4aa]' : ''
    }`}
    onClick={onClick}
    disabled={disabled}
  >
    <Icon className="w-5 h-5" />
    <span className="text-xs">{label}</span>
  </Button>
);

const QuickActions: React.FC<QuickActionsProps> = ({
  currentUserId,
  currentRunId,
  currentAgentId,
  onUserChange,
  onRunChange,
  onAgentChange,
  onRefresh
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [newMemoryText, setNewMemoryText] = useState('');
  const [exportFormat, setExportFormat] = useState<'json' | 'csv'>('json');
  
  const { toast } = useToast();
  const router = useRouter();

  const handleCreateMemory = async () => {
    if (!newMemoryText.trim()) {
      toast({
        title: "错误",
        description: "请输入记忆内容",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      // 构建请求参数，过滤掉undefined值
      const createParams: Record<string, unknown> = {
        messages: [{ role: 'user', content: newMemoryText }]
      };
      if (currentUserId) createParams.user_id = currentUserId;
      if (currentRunId) createParams.run_id = currentRunId;
      if (currentAgentId) createParams.agent_id = currentAgentId;

      await realMem0Client.createMemory(createParams);

      toast({
        title: "成功",
        description: "记忆创建成功",
        variant: "default"
      });

      setNewMemoryText('');
      setShowCreateDialog(false);
      onRefresh?.();
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "创建记忆失败",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBatchDelete = async () => {
    setIsLoading(true);
    try {
      // 这里需要实现批量删除逻辑
      // 由于需要选择要删除的记忆，这里先实现清空所有记忆的功能
      // 构建删除参数，过滤掉undefined值
      const deleteParams: Record<string, string> = {};
      if (currentUserId) deleteParams.user_id = currentUserId;
      if (currentRunId) deleteParams.run_id = currentRunId;
      if (currentAgentId) deleteParams.agent_id = currentAgentId;

      await realMem0Client.deleteAllMemories(deleteParams);

      toast({
        title: "成功",
        description: "批量删除完成",
        variant: "default"
      });

      setShowDeleteDialog(false);
      onRefresh?.();
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "批量删除失败",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async () => {
    setIsLoading(true);
    try {
      // 构建导出参数，过滤掉undefined值
      const exportParams: Record<string, string> = { format: exportFormat };
      if (currentUserId) exportParams.user_id = currentUserId;
      if (currentAgentId) exportParams.agent_id = currentAgentId;
      if (currentRunId) exportParams.run_id = currentRunId;

      const result = await realMem0Client.exportMemories(exportParams);

      toast({
        title: "导出已开始",
        description: `导出ID: ${result.export_id}`,
        variant: "default"
      });

      setShowExportDialog(false);
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "导出失败",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearCache = async () => {
    setIsLoading(true);
    try {
      await realMem0Client.clearCache();
      toast({
        title: "成功",
        description: "缓存清理完成",
        variant: "default"
      });
      onRefresh?.();
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "清理缓存失败",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Card className="bg-zinc-900 border-zinc-800">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Layers className="w-5 h-5 text-[#00d4aa]" />
            快速操作
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 上下文选择器 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <UserSelector
              value={currentUserId || undefined}
              onChange={onUserChange || undefined}
            />
            <AppSelector
              value={currentRunId || undefined}
              onChange={onRunChange || undefined}
            />
            <AgentSelector
              value={currentAgentId || undefined}
              onChange={onAgentChange || undefined}
            />
          </div>

          {/* 操作按钮网格 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <ActionButton
              icon={Plus}
              label="创建记忆"
              onClick={() => setShowCreateDialog(true)}
              disabled={isLoading}
            />
            <ActionButton
              icon={Search}
              label="搜索记忆"
              onClick={() => router.push('/memories')}
              disabled={isLoading}
            />
            <ActionButton
              icon={Download}
              label="导出数据"
              onClick={() => setShowExportDialog(true)}
              disabled={isLoading}
            />
            <ActionButton
              icon={Trash2}
              label="批量删除"
              onClick={() => setShowDeleteDialog(true)}
              variant="destructive"
              disabled={isLoading}
            />
            <ActionButton
              icon={RefreshCw}
              label="清理缓存"
              onClick={handleClearCache}
              disabled={isLoading}
            />
          </div>
        </CardContent>
      </Card>

      {/* 创建记忆对话框 */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="bg-zinc-900 border-zinc-800">
          <DialogHeader>
            <DialogTitle className="text-white">创建新记忆</DialogTitle>
            <DialogDescription className="text-zinc-400">
              输入记忆内容，系统将自动处理并存储。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-zinc-400">记忆内容</Label>
              <Textarea
                value={newMemoryText}
                onChange={(e) => setNewMemoryText(e.target.value)}
                placeholder="输入要记忆的内容..."
                className="bg-zinc-800 border-zinc-700 text-white min-h-[100px]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              className="border-zinc-700"
            >
              取消
            </Button>
            <Button
              onClick={handleCreateMemory}
              disabled={isLoading || !newMemoryText.trim()}
              className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-black"
            >
              {isLoading ? '创建中...' : '创建记忆'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 批量删除确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="bg-zinc-900 border-zinc-800">
          <DialogHeader>
            <DialogTitle className="text-white flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-red-400" />
              确认批量删除
            </DialogTitle>
            <DialogDescription className="text-zinc-400">
              此操作将删除当前上下文下的所有记忆，此操作不可撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              className="border-zinc-700"
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleBatchDelete}
              disabled={isLoading}
            >
              {isLoading ? '删除中...' : '确认删除'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导出对话框 */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent className="bg-zinc-900 border-zinc-800">
          <DialogHeader>
            <DialogTitle className="text-white">导出记忆数据</DialogTitle>
            <DialogDescription className="text-zinc-400">
              选择导出格式，系统将生成下载链接。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-zinc-400">导出格式</Label>
              <Select value={exportFormat} onValueChange={(value: 'json' | 'csv') => setExportFormat(value)}>
                <SelectTrigger className="bg-zinc-800 border-zinc-700 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-zinc-800 border-zinc-700">
                  <SelectItem value="json">JSON格式</SelectItem>
                  <SelectItem value="csv">CSV格式</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowExportDialog(false)}
              className="border-zinc-700"
            >
              取消
            </Button>
            <Button
              onClick={handleExport}
              disabled={isLoading}
              className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-black"
            >
              {isLoading ? '导出中...' : '开始导出'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default QuickActions;
