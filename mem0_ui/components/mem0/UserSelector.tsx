import React, { useState, useEffect } from 'react';
import { Users, Plus, Trash2, User, Globe, UserCheck } from 'lucide-react';
import { useSelector, useDispatch } from 'react-redux';

import { Button } from '@/components/ui/button';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { useUserManagement } from '@/hooks/useUserManagement';
import { RootState } from '@/store/store';
import {
  switchToUser,
  switchToSystemView,
  setAvailableUsers
} from '@/store/profileSlice';
import { realMem0Client } from '@/lib/mem0-client';

interface UserSelectorProps {
  value?: string | undefined;
  onChange?: ((userId: string) => void) | undefined;
  className?: string;
  showSystemView?: boolean; // 新增：是否显示系统级视图选项
}

const UserSelector: React.FC<UserSelectorProps> = ({
  value,
  onChange,
  className,
  showSystemView = true
}) => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [newUserId, setNewUserId] = useState('');
  const [newUserName, setNewUserName] = useState('');
  const [userToDelete, setUserToDelete] = useState<string>('');
  const [availableUsersList, setAvailableUsersList] = useState<Array<{user_id: string; name?: string; total_memories: number}>>([]);
  
  const { toast } = useToast();
  const dispatch = useDispatch();
  
  // 从 Redux store 获取状态
  const { 
    viewMode, 
    userId: currentUserId, 
    availableUsers 
  } = useSelector((state: RootState) => state.profile);
  
  const {
    isLoading,
    createUser,
    deleteUser
  } = useUserManagement();

  // 获取可用用户列表
  useEffect(() => {
    const fetchAvailableUsers = async () => {
      try {
        const usersResponse = await realMem0Client.getUsers({ limit: 100 });
        const usersList = usersResponse.users || [];
        setAvailableUsersList(usersList);
        dispatch(setAvailableUsers(usersList.map(u => u.user_id)));
      } catch (error) {
        console.warn('Failed to fetch users list:', error);
        // 如果获取用户列表失败，使用默认用户
        setAvailableUsersList([{ user_id: 'default', total_memories: 0 }]);
        dispatch(setAvailableUsers(['default']));
      }
    };

    fetchAvailableUsers();
  }, [dispatch]);

  // 处理视图模式切换
  const handleViewModeChange = (selectedValue: string) => {
    if (selectedValue === 'system') {
      dispatch(switchToSystemView());
      onChange?.('system');
    } else {
      dispatch(switchToUser(selectedValue));
      onChange?.(selectedValue);
    }
  };

  const handleCreateUser = async () => {
    if (!newUserId.trim()) {
      toast({
        title: "错误",
        description: "请输入用户ID",
        variant: "destructive"
      });
      return;
    }

    try {
      const result = await createUser(newUserId.trim(), newUserName.trim() || undefined);

      if (result?.existing) {
        toast({
          title: "用户已存在",
          description: `用户 "${newUserId}" 已存在，包含 ${result.memory_count} 条记忆`,
          variant: "default"
        });
      } else {
        toast({
          title: "成功",
          description: `用户 "${newUserId}" 创建成功`,
          variant: "default"
        });
      }

      setNewUserId('');
      setNewUserName('');
      setShowCreateDialog(false);
      
      // 刷新用户列表
      const usersResponse = await realMem0Client.getUsers({ limit: 100 });
      const usersList = usersResponse.users || [];
      setAvailableUsersList(usersList);
      dispatch(setAvailableUsers(usersList.map(u => u.user_id)));
    } catch (error) {
      console.error('User creation error:', error);
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "创建用户失败",
        variant: "destructive"
      });
    }
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      await deleteUser(userToDelete);
      toast({
        title: "成功",
        description: "用户删除成功",
        variant: "default"
      });
      setUserToDelete('');
      setShowDeleteDialog(false);
      
      // 刷新用户列表
      const usersResponse = await realMem0Client.getUsers({ limit: 100 });
      const usersList = usersResponse.users || [];
      setAvailableUsersList(usersList);
      dispatch(setAvailableUsers(usersList.map(u => u.user_id)));
      
      // 如果删除了当前选中的用户，切换到系统视图
      if (userToDelete === currentUserId) {
        dispatch(switchToSystemView());
        onChange?.('system');
      }
    } catch (error) {
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "删除用户失败",
        variant: "destructive"
      });
    }
  };

  // 获取当前选中的显示信息
  const getCurrentSelection = () => {
    if (viewMode === 'system') {
      return {
        id: 'system',
        displayName: '系统级视图',
        memoryCount: availableUsersList.reduce((sum, user) => sum + user.total_memories, 0),
        icon: Globe,
        badge: '所有用户'
      };
    } else {
      const selectedUser = availableUsersList.find(user => user.user_id === currentUserId);
      return {
        id: currentUserId,
        displayName: selectedUser?.name || selectedUser?.user_id || '未知用户',
        memoryCount: selectedUser?.total_memories || 0,
        icon: User,
        badge: '单用户'
      };
    }
  };

  const currentSelection = getCurrentSelection();

  return (
    <>
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center justify-between">
          <Label className="text-zinc-400 flex items-center gap-2">
            <Users className="w-4 h-4" />
            视图管理
          </Label>
          <div className="flex gap-1">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowCreateDialog(true)}
              className="h-6 w-6 p-0 border-zinc-700 hover:border-zinc-600"
              disabled={isLoading}
            >
              <Plus className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                if (viewMode === 'user' && currentUserId !== 'default' && currentUserId !== 'system') {
                  setUserToDelete(currentUserId);
                  setShowDeleteDialog(true);
                } else {
                  toast({
                    title: "提示",
                    description: "无法删除系统视图或默认用户",
                    variant: "default"
                  });
                }
              }}
              className="h-6 w-6 p-0 border-zinc-700 hover:border-zinc-600"
              disabled={isLoading || viewMode === 'system' || currentUserId === 'default'}
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </div>
        
        <Select 
          value={value || (viewMode === 'system' ? 'system' : currentUserId) || ''} 
          onValueChange={handleViewModeChange}
          disabled={isLoading}
        >
          <SelectTrigger className="bg-zinc-800 border-zinc-700 text-white">
            <SelectValue placeholder="选择视图">
              <div className="flex items-center gap-2">
                <currentSelection.icon className="w-4 h-4 text-[#00d4aa]" />
                <span>{currentSelection.displayName}</span>
                <span className="text-xs text-zinc-400">
                  ({currentSelection.memoryCount} 记忆)
                </span>
                <Badge variant="outline" className="text-xs border-zinc-600 text-zinc-300">
                  {currentSelection.badge}
                </Badge>
              </div>
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="bg-zinc-800 border-zinc-700">
            {/* 系统级视图选项 */}
            {showSystemView && (
              <SelectItem value="system">
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4 text-[#00d4aa]" />
                  <span>系统级视图</span>
                  <span className="text-xs text-zinc-400">
                    ({availableUsersList.reduce((sum, user) => sum + user.total_memories, 0)} 记忆)
                  </span>
                  <Badge variant="outline" className="text-xs border-zinc-600 text-zinc-300">
                    所有用户
                  </Badge>
                </div>
              </SelectItem>
            )}
            
            {/* 用户视图选项 */}
            {availableUsersList.map((user) => (
              <SelectItem key={user.user_id} value={user.user_id}>
                <div className="flex items-center gap-2">
                  <UserCheck className="w-4 h-4 text-[#00d4aa]" />
                  <span>{user.name || user.user_id}</span>
                  <span className="text-xs text-zinc-400">
                    ({user.total_memories} 记忆)
                  </span>
                  <Badge variant="outline" className="text-xs border-zinc-600 text-zinc-300">
                    单用户
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 创建用户对话框 */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="bg-zinc-900 border-zinc-800">
          <DialogHeader>
            <DialogTitle className="text-white">创建新用户</DialogTitle>
            <DialogDescription className="text-zinc-400">
              输入用户ID和可选的显示名称。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-zinc-400">用户ID *</Label>
              <Input
                value={newUserId}
                onChange={(e) => setNewUserId(e.target.value)}
                placeholder="输入唯一的用户ID"
                className="bg-zinc-800 border-zinc-700 text-white"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-zinc-400">显示名称</Label>
              <Input
                value={newUserName}
                onChange={(e) => setNewUserName(e.target.value)}
                placeholder="输入用户显示名称（可选）"
                className="bg-zinc-800 border-zinc-700 text-white"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              className="border-zinc-700"
            >
              取消
            </Button>
            <Button
              onClick={handleCreateUser}
              disabled={isLoading || !newUserId.trim()}
              className="bg-[#00d4aa] hover:bg-[#00d4aa]/90 text-black"
            >
              {isLoading ? '创建中...' : '创建用户'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除用户确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="bg-zinc-900 border-zinc-800">
          <DialogHeader>
            <DialogTitle className="text-white">确认删除用户</DialogTitle>
            <DialogDescription className="text-zinc-400">
              此操作将删除用户 &quot;{userToDelete}&quot; 及其所有记忆数据，此操作不可撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              className="border-zinc-700"
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteUser}
              disabled={isLoading}
            >
              {isLoading ? '删除中...' : '确认删除'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UserSelector;
