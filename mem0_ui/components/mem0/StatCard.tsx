import React from 'react';
import { LucideIcon } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  subtitle?: string;
  isLoading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon: Icon,
  trend,
  subtitle,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <div className="bg-zinc-900 rounded-lg border border-zinc-800 p-6" data-testid="loading-card">
        <div className="flex items-center justify-between mb-4">
          <div className="w-8 h-8 bg-zinc-800 rounded animate-pulse"></div>
          <div className="w-12 h-4 bg-zinc-800 rounded animate-pulse"></div>
        </div>
        <div className="space-y-2">
          <div className="w-16 h-8 bg-zinc-800 rounded animate-pulse"></div>
          <div className="w-24 h-4 bg-zinc-800 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-zinc-900 rounded-lg border border-zinc-800 p-6 hover:border-zinc-700 transition-colors">
      <div className="flex items-center justify-between mb-4">
        <div className="p-2 bg-[#00d4aa]/10 rounded-lg">
          <Icon className="w-5 h-5 text-[#00d4aa]" />
        </div>
        {trend && (
          <div className={`flex items-center text-sm ${
            trend.isPositive ? 'text-green-400' : 'text-red-400'
          }`}>
            <span className="mr-1">
              {trend.isPositive ? '↗' : '↘'}
            </span>
            {Math.abs(trend.value)}%
          </div>
        )}
      </div>
      
      <div className="space-y-1">
        <h3 className="text-2xl font-bold text-white">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </h3>
        <p className="text-zinc-400 text-sm">{title}</p>
        {subtitle && (
          <p className="text-zinc-500 text-xs">{subtitle}</p>
        )}
      </div>
    </div>
  );
};

export default StatCard;
