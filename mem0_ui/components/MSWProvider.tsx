'use client';

import { useEffect, useState } from 'react';
import { NoSSR } from './NoSSR';

/**
 * MSW Provider - 在开发环境中启动Mock Service Worker
 * 确保MSW在客户端组件挂载后启动，避免SSR问题
 */
export function MSWProvider({ children }: { children: React.ReactNode }) {
  const [mswReady, setMswReady] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);

    const initMSW = async () => {
      // 检查是否应该启用MSW（可通过环境变量DISABLE_MSW=true禁用）
      const shouldEnableMSW = process.env.NODE_ENV === 'development'
        && typeof window !== 'undefined'
        && process.env.NEXT_PUBLIC_DISABLE_MSW !== 'true';

      if (shouldEnableMSW) {
        try {
          const { worker } = await import('@/src/mocks/browser');

          // 启动MSW worker
          await worker.start({
            onUnhandledRequest: 'bypass', // 对未处理的请求放行
            serviceWorker: {
              url: '/mockServiceWorker.js'
            }
          });

          console.log('🚀 MSW Mock API started successfully');

          // 导入并调用端点信息打印函数
          const { printAvailableEndpoints } = await import('@/src/mocks/handlers');
          printAvailableEndpoints();

        } catch (error) {
          console.error('❌ Failed to start MSW:', error);
        }
      } else {
        console.log('🚫 MSW disabled - using real API at', process.env.NEXT_PUBLIC_MEM0_API_URL || 'http://localhost:8000');
      }

      setMswReady(true);
    };

    initMSW();
  }, []);

  // 在开发环境中等待MSW准备就绪（如果MSW被禁用则直接渲染）
  const needWaitForMSW = isClient
    && process.env.NODE_ENV === 'development'
    && process.env.NEXT_PUBLIC_DISABLE_MSW !== 'true'
    && !mswReady;

  return (
    <NoSSR fallback={<div className="min-h-screen bg-zinc-950" />}>
      {needWaitForMSW ? (
        <div className="flex items-center justify-center min-h-screen bg-black text-white">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#00d4aa] mx-auto mb-4"></div>
            <p className="text-sm text-gray-400">Initializing Mock API...</p>
          </div>
        </div>
      ) : (
        children
      )}
    </NoSSR>
  );
}
