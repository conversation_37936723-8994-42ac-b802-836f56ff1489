/**
 * 统一的加载骨架屏组件
 * 
 * 提供一致的加载状态展示：
 * - 多种预设骨架屏样式
 * - 自定义骨架屏组合
 * - 响应式设计
 * - 平滑动画效果
 */

import React from 'react';
import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  animate?: boolean;
}

/**
 * 基础骨架屏组件
 */
export function Skeleton({ className, animate = true }: SkeletonProps) {
  return (
    <div
      className={cn(
        'bg-zinc-800 rounded-md',
        animate && 'animate-pulse',
        className
      )}
    />
  );
}

/**
 * 文本行骨架屏
 */
export function TextSkeleton({ 
  lines = 3, 
  className,
  animate = true 
}: { 
  lines?: number; 
  className?: string;
  animate?: boolean;
}) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          className={cn(
            'h-4',
            index === lines - 1 ? 'w-3/4' : 'w-full'
          )}
          animate={animate}
        />
      ))}
    </div>
  );
}

/**
 * 卡片骨架屏
 */
export function CardSkeleton({ 
  className,
  animate = true,
  showHeader = true,
  showContent = true 
}: { 
  className?: string;
  animate?: boolean;
  showHeader?: boolean;
  showContent?: boolean;
}) {
  return (
    <div className={cn('p-6 bg-zinc-900 border border-zinc-800 rounded-lg', className)}>
      {showHeader && (
        <div className="space-y-2 mb-4">
          <Skeleton className="h-6 w-1/3" animate={animate} />
          <Skeleton className="h-4 w-1/2" animate={animate} />
        </div>
      )}
      {showContent && (
        <div className="space-y-3">
          <TextSkeleton lines={3} animate={animate} />
        </div>
      )}
    </div>
  );
}

/**
 * 表格骨架屏
 */
export function TableSkeleton({ 
  rows = 5, 
  columns = 4,
  className,
  animate = true 
}: { 
  rows?: number; 
  columns?: number;
  className?: string;
  animate?: boolean;
}) {
  return (
    <div className={cn('space-y-3', className)}>
      {/* 表头 */}
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, index) => (
          <Skeleton key={`header-${index}`} className="h-6 w-full" animate={animate} />
        ))}
      </div>
      
      {/* 表格行 */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div 
          key={`row-${rowIndex}`} 
          className="grid gap-4" 
          style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
        >
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton 
              key={`cell-${rowIndex}-${colIndex}`} 
              className="h-4 w-full" 
              animate={animate} 
            />
          ))}
        </div>
      ))}
    </div>
  );
}

/**
 * 列表骨架屏
 */
export function ListSkeleton({ 
  items = 5,
  className,
  animate = true,
  showAvatar = false 
}: { 
  items?: number;
  className?: string;
  animate?: boolean;
  showAvatar?: boolean;
}) {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: items }).map((_, index) => (
        <div key={index} className="flex items-start space-x-3">
          {showAvatar && (
            <Skeleton className="h-10 w-10 rounded-full" animate={animate} />
          )}
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" animate={animate} />
            <Skeleton className="h-3 w-1/2" animate={animate} />
          </div>
        </div>
      ))}
    </div>
  );
}

/**
 * 图表骨架屏
 */
export function ChartSkeleton({ 
  className,
  animate = true,
  type = 'bar' 
}: { 
  className?: string;
  animate?: boolean;
  type?: 'bar' | 'line' | 'pie';
}) {
  return (
    <div className={cn('p-4', className)}>
      {type === 'bar' && (
        <div className="flex items-end justify-between h-32 space-x-2">
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton
              key={index}
              className={`w-8 bg-zinc-700`}
              style={{ height: `${Math.random() * 80 + 20}%` }}
              animate={animate}
            />
          ))}
        </div>
      )}
      
      {type === 'line' && (
        <div className="relative h-32">
          <Skeleton className="absolute inset-0 bg-zinc-700" animate={animate} />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-full h-px bg-zinc-600" />
          </div>
        </div>
      )}
      
      {type === 'pie' && (
        <div className="flex justify-center">
          <Skeleton className="h-32 w-32 rounded-full bg-zinc-700" animate={animate} />
        </div>
      )}
    </div>
  );
}

/**
 * 图形可视化骨架屏
 */
export function GraphSkeleton({ 
  className,
  animate = true 
}: { 
  className?: string;
  animate?: boolean;
}) {
  return (
    <div className={cn('relative h-96 bg-zinc-900 border border-zinc-800 rounded-lg overflow-hidden', className)}>
      {/* 节点骨架 */}
      <div className="absolute inset-0 p-8">
        {Array.from({ length: 8 }).map((_, index) => (
          <Skeleton
            key={index}
            className="absolute h-8 w-8 rounded-full bg-zinc-700"
            style={{
              left: `${Math.random() * 80 + 10}%`,
              top: `${Math.random() * 80 + 10}%`,
            }}
            animate={animate}
          />
        ))}
      </div>
      
      {/* 连接线骨架 */}
      <div className="absolute inset-0">
        {Array.from({ length: 6 }).map((_, index) => (
          <div
            key={index}
            className={cn(
              'absolute h-px bg-zinc-700',
              animate && 'animate-pulse'
            )}
            style={{
              left: `${Math.random() * 60 + 20}%`,
              top: `${Math.random() * 60 + 20}%`,
              width: `${Math.random() * 30 + 10}%`,
              transform: `rotate(${Math.random() * 360}deg)`,
            }}
          />
        ))}
      </div>
      
      {/* 中心加载指示器 */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <Skeleton className="h-6 w-32 mx-auto mb-2" animate={animate} />
          <Skeleton className="h-4 w-24 mx-auto" animate={animate} />
        </div>
      </div>
    </div>
  );
}

/**
 * 统计卡片骨架屏
 */
export function StatCardSkeleton({ 
  className,
  animate = true 
}: { 
  className?: string;
  animate?: boolean;
}) {
  return (
    <div className={cn('p-6 bg-zinc-900 border border-zinc-800 rounded-lg', className)}>
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-4 w-20" animate={animate} />
          <Skeleton className="h-8 w-16" animate={animate} />
        </div>
        <Skeleton className="h-8 w-8 rounded" animate={animate} />
      </div>
    </div>
  );
}

/**
 * 页面加载骨架屏
 */
export function PageSkeleton({ 
  className,
  animate = true 
}: { 
  className?: string;
  animate?: boolean;
}) {
  return (
    <div className={cn('space-y-6 p-6', className)}>
      {/* 页面标题 */}
      <div className="space-y-2">
        <Skeleton className="h-8 w-1/3" animate={animate} />
        <Skeleton className="h-4 w-1/2" animate={animate} />
      </div>
      
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <StatCardSkeleton key={index} animate={animate} />
        ))}
      </div>
      
      {/* 主要内容 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <CardSkeleton animate={animate} />
        </div>
        <div>
          <CardSkeleton animate={animate} />
        </div>
      </div>
    </div>
  );
}
