/**
 * 虚拟滚动列表组件
 * 
 * 用于优化大数据量列表的渲染性能：
 * - 只渲染可见区域的项目
 * - 支持动态高度
 * - 平滑滚动体验
 * - 内存使用优化
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { cn } from '@/lib/utils';

interface VirtualListProps<T> {
  items: T[];
  itemHeight: number | ((item: T, index: number) => number);
  renderItem: (item: T, index: number) => React.ReactNode;
  className?: string;
  height: number;
  overscan?: number;
  onScroll?: (scrollTop: number) => void;
  getItemKey?: (item: T, index: number) => string | number;
}

interface ItemInfo {
  index: number;
  height: number;
  top: number;
}

export function VirtualList<T>({
  items,
  itemHeight,
  renderItem,
  className,
  height,
  overscan = 5,
  onScroll,
  getItemKey
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollElementRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();

  // 计算项目信息
  const itemInfos = useMemo(() => {
    const infos: ItemInfo[] = [];
    let top = 0;

    for (let i = 0; i < items.length; i++) {
      const height = typeof itemHeight === 'function' 
        ? itemHeight(items[i], i) 
        : itemHeight;
      
      infos.push({
        index: i,
        height,
        top
      });
      
      top += height;
    }

    return infos;
  }, [items, itemHeight]);

  // 计算总高度
  const totalHeight = useMemo(() => {
    return itemInfos.length > 0 
      ? itemInfos[itemInfos.length - 1].top + itemInfos[itemInfos.length - 1].height
      : 0;
  }, [itemInfos]);

  // 查找可见范围
  const visibleRange = useMemo(() => {
    if (itemInfos.length === 0) {
      return { start: 0, end: 0 };
    }

    // 二分查找起始索引
    let start = 0;
    let end = itemInfos.length - 1;
    
    while (start <= end) {
      const mid = Math.floor((start + end) / 2);
      const itemInfo = itemInfos[mid];
      
      if (itemInfo.top <= scrollTop && itemInfo.top + itemInfo.height > scrollTop) {
        start = mid;
        break;
      } else if (itemInfo.top < scrollTop) {
        start = mid + 1;
      } else {
        end = mid - 1;
      }
    }

    // 查找结束索引
    let endIndex = start;
    let currentTop = itemInfos[start]?.top || 0;
    
    while (endIndex < itemInfos.length && currentTop < scrollTop + height) {
      currentTop += itemInfos[endIndex].height;
      endIndex++;
    }

    // 应用overscan
    const overscanStart = Math.max(0, start - overscan);
    const overscanEnd = Math.min(itemInfos.length - 1, endIndex + overscan);

    return {
      start: overscanStart,
      end: overscanEnd
    };
  }, [itemInfos, scrollTop, height, overscan]);

  // 处理滚动事件
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    setIsScrolling(true);
    
    onScroll?.(newScrollTop);

    // 清除之前的定时器
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 设置新的定时器来检测滚动结束
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 150);
  }, [onScroll]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // 渲染可见项目
  const visibleItems = useMemo(() => {
    const items_to_render = [];
    
    for (let i = visibleRange.start; i <= visibleRange.end; i++) {
      if (i >= items.length) break;
      
      const item = items[i];
      const itemInfo = itemInfos[i];
      const key = getItemKey ? getItemKey(item, i) : i;
      
      items_to_render.push(
        <div
          key={key}
          style={{
            position: 'absolute',
            top: itemInfo.top,
            height: itemInfo.height,
            width: '100%'
          }}
        >
          {renderItem(item, i)}
        </div>
      );
    }
    
    return items_to_render;
  }, [items, itemInfos, visibleRange, renderItem, getItemKey]);

  // 滚动到指定索引
  const scrollToIndex = useCallback((index: number, align: 'start' | 'center' | 'end' = 'start') => {
    if (!scrollElementRef.current || index < 0 || index >= itemInfos.length) {
      return;
    }

    const itemInfo = itemInfos[index];
    let scrollTop = itemInfo.top;

    if (align === 'center') {
      scrollTop = itemInfo.top - (height - itemInfo.height) / 2;
    } else if (align === 'end') {
      scrollTop = itemInfo.top - height + itemInfo.height;
    }

    scrollTop = Math.max(0, Math.min(scrollTop, totalHeight - height));
    scrollElementRef.current.scrollTop = scrollTop;
  }, [itemInfos, height, totalHeight]);

  return (
    <div
      ref={scrollElementRef}
      className={cn(
        'overflow-auto',
        className
      )}
      style={{ height }}
      onScroll={handleScroll}
    >
      <div
        style={{
          height: totalHeight,
          position: 'relative'
        }}
      >
        {visibleItems}
      </div>
    </div>
  );
}

/**
 * 虚拟网格组件
 */
interface VirtualGridProps<T> {
  items: T[];
  itemWidth: number;
  itemHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  className?: string;
  height: number;
  width: number;
  gap?: number;
  overscan?: number;
  getItemKey?: (item: T, index: number) => string | number;
}

export function VirtualGrid<T>({
  items,
  itemWidth,
  itemHeight,
  renderItem,
  className,
  height,
  width,
  gap = 0,
  overscan = 5,
  getItemKey
}: VirtualGridProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  // 计算列数
  const columnsCount = Math.floor((width + gap) / (itemWidth + gap));
  const rowsCount = Math.ceil(items.length / columnsCount);
  const totalHeight = rowsCount * (itemHeight + gap) - gap;

  // 计算可见范围
  const visibleRange = useMemo(() => {
    const startRow = Math.floor(scrollTop / (itemHeight + gap));
    const endRow = Math.min(
      rowsCount - 1,
      Math.ceil((scrollTop + height) / (itemHeight + gap))
    );

    const overscanStart = Math.max(0, startRow - overscan);
    const overscanEnd = Math.min(rowsCount - 1, endRow + overscan);

    return {
      startRow: overscanStart,
      endRow: overscanEnd
    };
  }, [scrollTop, height, itemHeight, gap, rowsCount, overscan]);

  // 处理滚动
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  // 渲染可见项目
  const visibleItems = useMemo(() => {
    const items_to_render = [];

    for (let row = visibleRange.startRow; row <= visibleRange.endRow; row++) {
      for (let col = 0; col < columnsCount; col++) {
        const index = row * columnsCount + col;
        if (index >= items.length) break;

        const item = items[index];
        const key = getItemKey ? getItemKey(item, index) : index;
        const x = col * (itemWidth + gap);
        const y = row * (itemHeight + gap);

        items_to_render.push(
          <div
            key={key}
            style={{
              position: 'absolute',
              left: x,
              top: y,
              width: itemWidth,
              height: itemHeight
            }}
          >
            {renderItem(item, index)}
          </div>
        );
      }
    }

    return items_to_render;
  }, [items, visibleRange, columnsCount, itemWidth, itemHeight, gap, renderItem, getItemKey]);

  return (
    <div
      ref={scrollElementRef}
      className={cn('overflow-auto', className)}
      style={{ height, width }}
      onScroll={handleScroll}
    >
      <div
        style={{
          height: totalHeight,
          width: '100%',
          position: 'relative'
        }}
      >
        {visibleItems}
      </div>
    </div>
  );
}
