/**
 * 性能优化的组件包装器
 * 
 * 提供React.memo优化的组件：
 * - 防止不必要的重新渲染
 * - 深度比较优化
 * - 条件渲染优化
 * - 懒加载支持
 */

import React, { memo, useMemo, useCallback, forwardRef } from 'react';
import { cn } from '@/lib/utils';

/**
 * 优化的卡片组件
 */
interface OptimizedCardProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  loading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
}

export const OptimizedCard = memo<OptimizedCardProps>(({
  title,
  description,
  children,
  className,
  loading = false,
  error = null,
  onRefresh
}) => {
  const cardContent = useMemo(() => {
    if (loading) {
      return (
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-zinc-800 rounded w-3/4"></div>
          <div className="h-4 bg-zinc-800 rounded w-1/2"></div>
          <div className="h-32 bg-zinc-800 rounded"></div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-center py-8">
          <div className="text-red-400 mb-2">Error loading content</div>
          <div className="text-sm text-zinc-500 mb-4">{error}</div>
          {onRefresh && (
            <button
              onClick={onRefresh}
              className="px-4 py-2 bg-zinc-800 text-white rounded hover:bg-zinc-700"
            >
              Retry
            </button>
          )}
        </div>
      );
    }

    return children;
  }, [loading, error, children, onRefresh]);

  return (
    <div className={cn('bg-zinc-900 border border-zinc-800 rounded-lg p-6', className)}>
      {(title || description) && (
        <div className="mb-4">
          {title && <h3 className="text-lg font-semibold text-white mb-1">{title}</h3>}
          {description && <p className="text-sm text-zinc-400">{description}</p>}
        </div>
      )}
      {cardContent}
    </div>
  );
});

OptimizedCard.displayName = 'OptimizedCard';

/**
 * 优化的列表项组件
 */
interface OptimizedListItemProps {
  id: string | number;
  title: string;
  subtitle?: string;
  avatar?: React.ReactNode;
  actions?: React.ReactNode;
  onClick?: (id: string | number) => void;
  className?: string;
  isSelected?: boolean;
}

export const OptimizedListItem = memo<OptimizedListItemProps>(({
  id,
  title,
  subtitle,
  avatar,
  actions,
  onClick,
  className,
  isSelected = false
}) => {
  const handleClick = useCallback(() => {
    onClick?.(id);
  }, [onClick, id]);

  return (
    <div
      className={cn(
        'flex items-center space-x-3 p-3 rounded-lg transition-colors',
        'hover:bg-zinc-800 cursor-pointer',
        isSelected && 'bg-zinc-800 border border-zinc-700',
        className
      )}
      onClick={handleClick}
    >
      {avatar && <div className="flex-shrink-0">{avatar}</div>}
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium text-white truncate">{title}</div>
        {subtitle && <div className="text-xs text-zinc-400 truncate">{subtitle}</div>}
      </div>
      {actions && <div className="flex-shrink-0">{actions}</div>}
    </div>
  );
});

OptimizedListItem.displayName = 'OptimizedListItem';

/**
 * 优化的数据表格组件
 */
interface OptimizedTableProps<T> {
  data: T[];
  columns: Array<{
    key: keyof T;
    title: string;
    render?: (value: T[keyof T], item: T, index: number) => React.ReactNode;
    width?: string;
  }>;
  loading?: boolean;
  emptyMessage?: string;
  onRowClick?: (item: T, index: number) => void;
  className?: string;
  maxHeight?: string;
}

export const OptimizedTable = memo(<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  emptyMessage = 'No data available',
  onRowClick,
  className,
  maxHeight = '400px'
}: OptimizedTableProps<T>) => {
  const tableContent = useMemo(() => {
    if (loading) {
      return (
        <div className="space-y-2">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex space-x-4">
              {columns.map((_, colIndex) => (
                <div
                  key={colIndex}
                  className="h-4 bg-zinc-800 rounded animate-pulse"
                  style={{ width: `${Math.random() * 40 + 20}%` }}
                />
              ))}
            </div>
          ))}
        </div>
      );
    }

    if (data.length === 0) {
      return (
        <div className="text-center py-8 text-zinc-500">
          {emptyMessage}
        </div>
      );
    }

    return (
      <div className="space-y-1">
        {data.map((item, index) => (
          <div
            key={index}
            className={cn(
              'grid gap-4 p-2 rounded hover:bg-zinc-800 transition-colors',
              onRowClick && 'cursor-pointer'
            )}
            style={{ gridTemplateColumns: columns.map(col => col.width || '1fr').join(' ') }}
            onClick={() => onRowClick?.(item, index)}
          >
            {columns.map((column) => (
              <div key={String(column.key)} className="text-sm text-zinc-300 truncate">
                {column.render
                  ? column.render(item[column.key], item, index)
                  : String(item[column.key] || '')
                }
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  }, [data, columns, loading, emptyMessage, onRowClick]);

  return (
    <div className={cn('bg-zinc-900 border border-zinc-800 rounded-lg', className)}>
      {/* 表头 */}
      <div
        className="grid gap-4 p-3 border-b border-zinc-800 bg-zinc-800/50"
        style={{ gridTemplateColumns: columns.map(col => col.width || '1fr').join(' ') }}
      >
        {columns.map((column) => (
          <div key={String(column.key)} className="text-sm font-medium text-zinc-200">
            {column.title}
          </div>
        ))}
      </div>

      {/* 表格内容 */}
      <div
        className="overflow-auto"
        style={{ maxHeight }}
      >
        <div className="p-3">
          {tableContent}
        </div>
      </div>
    </div>
  );
}) as <T extends Record<string, any>>(props: OptimizedTableProps<T>) => JSX.Element;

OptimizedTable.displayName = 'OptimizedTable';

/**
 * 优化的统计卡片组件
 */
interface OptimizedStatCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease' | 'neutral';
  };
  icon?: React.ReactNode;
  loading?: boolean;
  className?: string;
}

export const OptimizedStatCard = memo<OptimizedStatCardProps>(({
  title,
  value,
  change,
  icon,
  loading = false,
  className
}) => {
  const content = useMemo(() => {
    if (loading) {
      return (
        <div className="animate-pulse space-y-3">
          <div className="h-4 bg-zinc-800 rounded w-2/3"></div>
          <div className="h-8 bg-zinc-800 rounded w-1/2"></div>
          <div className="h-3 bg-zinc-800 rounded w-1/3"></div>
        </div>
      );
    }

    return (
      <>
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium text-zinc-400">{title}</div>
          {icon && <div className="text-zinc-500">{icon}</div>}
        </div>
        <div className="text-2xl font-bold text-white">{value}</div>
        {change && (
          <div className={cn(
            'text-xs flex items-center',
            change.type === 'increase' && 'text-green-400',
            change.type === 'decrease' && 'text-red-400',
            change.type === 'neutral' && 'text-zinc-400'
          )}>
            <span className="mr-1">
              {change.type === 'increase' && '↗'}
              {change.type === 'decrease' && '↘'}
              {change.type === 'neutral' && '→'}
            </span>
            {Math.abs(change.value)}%
          </div>
        )}
      </>
    );
  }, [title, value, change, icon, loading]);

  return (
    <div className={cn('bg-zinc-900 border border-zinc-800 rounded-lg p-4', className)}>
      {content}
    </div>
  );
});

OptimizedStatCard.displayName = 'OptimizedStatCard';

/**
 * 优化的搜索框组件
 */
interface OptimizedSearchProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  debounceMs?: number;
  className?: string;
  onClear?: () => void;
}

export const OptimizedSearch = memo<OptimizedSearchProps>(({
  value,
  onChange,
  placeholder = 'Search...',
  className,
  onClear
}) => {
  const handleClear = useCallback(() => {
    onChange('');
    onClear?.();
  }, [onChange, onClear]);

  return (
    <div className={cn('relative', className)}>
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-white placeholder-zinc-400 focus:outline-none focus:border-zinc-600"
      />
      {value && (
        <button
          onClick={handleClear}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 text-zinc-400 hover:text-white"
        >
          ×
        </button>
      )}
    </div>
  );
});

OptimizedSearch.displayName = 'OptimizedSearch';
