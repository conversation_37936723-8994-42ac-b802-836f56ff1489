'use client';

import React from 'react';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Database, 
  HardDrive, 
  RefreshCw, 
  Server, 
  XCircle 
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { SystemHealth } from '@/lib/monitoring/SystemMonitor';

interface SystemHealthCardProps {
  systemHealth: SystemHealth | null;
  isLoading: boolean;
  onRefresh: () => void;
}

const SystemHealthCard: React.FC<SystemHealthCardProps> = ({
  systemHealth,
  isLoading,
  onRefresh
}) => {
  // 获取服务状态颜色
  const getServiceStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'degraded':
        return 'bg-yellow-500';
      case 'offline':
        return 'bg-red-500';
      default:
        return 'bg-zinc-500';
    }
  };

  // 获取服务状态图标
  const getServiceStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'degraded':
        return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      case 'offline':
        return <XCircle className="w-4 h-4 text-red-400" />;
      default:
        return <Activity className="w-4 h-4 text-zinc-400" />;
    }
  };

  // 获取系统状态颜色
  const getSystemStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-400';
      case 'warning':
        return 'text-yellow-400';
      case 'critical':
        return 'text-red-400';
      default:
        return 'text-zinc-400';
    }
  };

  return (
    <Card className="bg-zinc-900 border-zinc-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-white flex items-center gap-2">
              <Server className="w-5 h-5 text-[#00d4aa]" />
              系统健康状态
            </CardTitle>
            <CardDescription className="text-zinc-400">
              实时监控系统各组件的健康状态
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isLoading}
            className="border-zinc-700 hover:border-zinc-600"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2 text-zinc-400">
              <RefreshCw className="w-4 h-4 animate-spin" />
              <span>检查系统健康状态...</span>
            </div>
          </div>
        ) : systemHealth ? (
          <>
            {/* 总体健康状态 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-zinc-400">总体状态</h3>
                <Badge 
                  className={`${
                    systemHealth.status === 'healthy' ? 'bg-green-500' :
                    systemHealth.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                  } text-white`}
                >
                  {systemHealth.status.toUpperCase()}
                </Badge>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-zinc-400">健康分数</span>
                  <span className={`font-medium ${getSystemStatusColor(systemHealth.status)}`}>
                    {systemHealth.score}/100
                  </span>
                </div>
                <Progress value={systemHealth.score} className="h-2" />
              </div>
              
              <p className="text-xs text-zinc-500">
                最后检查: {new Date(systemHealth.lastCheck).toLocaleString()}
              </p>
            </div>

            {/* 服务状态详情 */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-zinc-400">服务状态</h3>
              
              {/* API服务 */}
              <div className="flex items-center justify-between p-3 rounded-lg bg-zinc-800">
                <div className="flex items-center gap-3">
                  <Server className="w-4 h-4 text-zinc-400" />
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-white">API 服务</span>
                      {getServiceStatusIcon(systemHealth.services.api.status)}
                    </div>
                    <div className="flex items-center gap-4 text-xs text-zinc-400 mt-1">
                      <span>响应时间: {systemHealth.services.api.responseTime}ms</span>
                      <span>错误率: {systemHealth.services.api.errorRate}%</span>
                    </div>
                  </div>
                </div>
                <Badge 
                  className={`${getServiceStatusColor(systemHealth.services.api.status)} text-white text-xs`}
                >
                  {systemHealth.services.api.status.toUpperCase()}
                </Badge>
              </div>

              {/* 数据库服务 */}
              <div className="flex items-center justify-between p-3 rounded-lg bg-zinc-800">
                <div className="flex items-center gap-3">
                  <Database className="w-4 h-4 text-zinc-400" />
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-white">数据库</span>
                      {getServiceStatusIcon(systemHealth.services.database.status)}
                    </div>
                    <div className="flex items-center gap-4 text-xs text-zinc-400 mt-1">
                      <span>连接正常</span>
                    </div>
                  </div>
                </div>
                <Badge 
                  className={`${getServiceStatusColor(systemHealth.services.database.status)} text-white text-xs`}
                >
                  {systemHealth.services.database.status.toUpperCase()}
                </Badge>
              </div>

              {/* 内存服务 */}
              <div className="flex items-center justify-between p-3 rounded-lg bg-zinc-800">
                <div className="flex items-center gap-3">
                  <HardDrive className="w-4 h-4 text-zinc-400" />
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-white">内存管理</span>
                      {getServiceStatusIcon(systemHealth.services.memory.status)}
                    </div>
                    <div className="flex items-center gap-4 text-xs text-zinc-400 mt-1">
                      <span>内存使用正常</span>
                    </div>
                  </div>
                </div>
                <Badge 
                  className={`${getServiceStatusColor(systemHealth.services.memory.status)} text-white text-xs`}
                >
                  {systemHealth.services.memory.status.toUpperCase()}
                </Badge>
              </div>
            </div>

            {/* 健康建议 */}
            {systemHealth.status !== 'healthy' && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-zinc-400">优化建议</h3>
                <div className="p-3 rounded-lg bg-zinc-800 border-l-4 border-yellow-500">
                  <div className="text-sm text-zinc-300 space-y-1">
                    {systemHealth.status === 'warning' && (
                      <>
                        <div>• 监控API响应时间，考虑优化慢查询</div>
                        <div>• 检查系统资源使用情况</div>
                      </>
                    )}
                    {systemHealth.status === 'critical' && (
                      <>
                        <div>• 立即检查API服务状态</div>
                        <div>• 检查系统资源是否耗尽</div>
                        <div>• 考虑重启相关服务</div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="flex items-center justify-center py-8">
            <div className="text-center text-zinc-400">
              <XCircle className="w-8 h-8 mx-auto mb-2" />
              <p>无法获取系统健康状态</p>
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                className="mt-2 border-zinc-700 hover:border-zinc-600"
              >
                重试
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SystemHealthCard;
