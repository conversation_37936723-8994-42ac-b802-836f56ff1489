'use client';

import React, { useState } from 'react';
import { 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  X, 
  XCircle 
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertEvent } from '@/lib/monitoring/SystemMonitor';

interface AlertsPanelProps {
  alertEvents: AlertEvent[];
  onResolveAlert: (alertId: string) => void;
  onClearAllAlerts: () => void;
}

const AlertsPanel: React.FC<AlertsPanelProps> = ({
  alertEvents,
  onResolveAlert,
  onClearAllAlerts
}) => {
  const [showResolved, setShowResolved] = useState(false);

  // 过滤告警事件
  const filteredAlerts = showResolved 
    ? alertEvents 
    : alertEvents.filter(alert => !alert.resolved);

  // 按类型分组告警
  const criticalAlerts = filteredAlerts.filter(alert => alert.type === 'critical');
  const warningAlerts = filteredAlerts.filter(alert => alert.type === 'warning');

  // 获取告警图标
  const getAlertIcon = (type: string, resolved: boolean) => {
    if (resolved) {
      return <CheckCircle className="w-4 h-4 text-green-400" />;
    }
    
    switch (type) {
      case 'critical':
        return <XCircle className="w-4 h-4 text-red-400" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-zinc-400" />;
    }
  };

  // 获取告警颜色
  const getAlertColor = (type: string, resolved: boolean) => {
    if (resolved) return 'border-green-500';
    
    switch (type) {
      case 'critical':
        return 'border-red-500';
      case 'warning':
        return 'border-yellow-500';
      default:
        return 'border-zinc-500';
    }
  };

  // 格式化时间
  const formatTime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) {
      return '刚刚';
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return new Date(timestamp).toLocaleDateString();
    }
  };

  return (
    <Card className="bg-zinc-900 border-zinc-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-white flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-[#00d4aa]" />
              系统告警
            </CardTitle>
            <CardDescription className="text-zinc-400">
              系统监控告警事件管理
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowResolved(!showResolved)}
              className="border-zinc-700 hover:border-zinc-600"
            >
              {showResolved ? '隐藏已解决' : '显示已解决'}
            </Button>
            {alertEvents.some(alert => !alert.resolved) && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearAllAlerts}
                className="border-zinc-700 hover:border-zinc-600 text-red-400"
              >
                全部解决
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 告警统计 */}
        <div className="grid grid-cols-3 gap-4">
          <div className="p-3 rounded-lg bg-zinc-800 text-center">
            <div className="text-red-400 text-lg font-bold">
              {criticalAlerts.length}
            </div>
            <div className="text-xs text-zinc-400">严重告警</div>
          </div>
          <div className="p-3 rounded-lg bg-zinc-800 text-center">
            <div className="text-yellow-400 text-lg font-bold">
              {warningAlerts.length}
            </div>
            <div className="text-xs text-zinc-400">警告告警</div>
          </div>
          <div className="p-3 rounded-lg bg-zinc-800 text-center">
            <div className="text-green-400 text-lg font-bold">
              {alertEvents.filter(alert => alert.resolved).length}
            </div>
            <div className="text-xs text-zinc-400">已解决</div>
          </div>
        </div>

        {/* 告警列表 */}
        {filteredAlerts.length > 0 ? (
          <div className="space-y-3">
            {filteredAlerts
              .sort((a, b) => {
                // 未解决的排在前面，然后按时间倒序
                if (a.resolved !== b.resolved) {
                  return a.resolved ? 1 : -1;
                }
                return b.timestamp - a.timestamp;
              })
              .map((alert) => (
                <div
                  key={alert.id}
                  className={`p-4 rounded-lg bg-zinc-800 border-l-4 ${getAlertColor(alert.type, alert.resolved)} ${
                    alert.resolved ? 'opacity-60' : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      {getAlertIcon(alert.type, alert.resolved)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge 
                            className={`${
                              alert.resolved ? 'bg-green-500' :
                              alert.type === 'critical' ? 'bg-red-500' : 'bg-yellow-500'
                            } text-white text-xs`}
                          >
                            {alert.resolved ? '已解决' : alert.type === 'critical' ? '严重' : '警告'}
                          </Badge>
                          <span className="text-xs text-zinc-400">{alert.metric}</span>
                        </div>
                        
                        <p className="text-sm text-white mb-2">
                          {alert.message}
                        </p>
                        
                        <div className="flex items-center gap-4 text-xs text-zinc-400">
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            <span>{formatTime(alert.timestamp)}</span>
                          </div>
                          <span>当前值: {alert.value}</span>
                          <span>阈值: {alert.threshold}</span>
                        </div>
                      </div>
                    </div>
                    
                    {!alert.resolved && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onResolveAlert(alert.id)}
                        className="border-zinc-700 hover:border-zinc-600 ml-2"
                      >
                        <CheckCircle className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
          </div>
        ) : (
          <div className="flex items-center justify-center py-8">
            <div className="text-center text-zinc-400">
              <CheckCircle className="w-8 h-8 mx-auto mb-2 text-green-400" />
              <p>
                {showResolved ? '暂无告警事件' : '暂无未解决的告警'}
              </p>
              <p className="text-xs mt-1">
                系统运行正常
              </p>
            </div>
          </div>
        )}

        {/* 告警配置提示 */}
        {alertEvents.length === 0 && (
          <div className="p-3 rounded-lg bg-zinc-800 border-l-4 border-[#00d4aa]">
            <div className="text-sm text-zinc-300">
              <div className="font-medium mb-1">告警配置说明</div>
              <div className="text-xs text-zinc-400 space-y-1">
                <div>• API响应时间 &gt; 2秒触发警告，&gt; 5秒触发严重告警</div>
                <div>• API错误率 &gt; 5%触发警告，&gt; 10%触发严重告警</div>
                <div>• 内存使用率 &gt; 80%触发警告，&gt; 95%触发严重告警</div>
                <div>• CPU使用率 &gt; 80%触发警告，&gt; 95%触发严重告警</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AlertsPanel;
