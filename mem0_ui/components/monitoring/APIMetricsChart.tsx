'use client';

import React from 'react';
import { 
  Activity, 
  Clock, 
  TrendingDown, 
  TrendingUp, 
  Zap 
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface APIMetricsChartProps {
  apiMetrics: {
    averageResponseTime: number;
    errorRate: number;
    requestCount: number;
    slowestEndpoints: Array<{ endpoint: string; avgTime: number }>;
  } | null;
}

const APIMetricsChart: React.FC<APIMetricsChartProps> = ({ apiMetrics }) => {
  // 获取响应时间颜色
  const getResponseTimeColor = (time: number) => {
    if (time < 500) return 'text-green-400';
    if (time < 1000) return 'text-yellow-400';
    return 'text-red-400';
  };

  // 获取错误率颜色
  const getErrorRateColor = (rate: number) => {
    if (rate < 1) return 'text-green-400';
    if (rate < 5) return 'text-yellow-400';
    return 'text-red-400';
  };

  // 获取性能等级
  const getPerformanceLevel = (time: number) => {
    if (time < 500) return { label: '优秀', color: 'bg-green-500' };
    if (time < 1000) return { label: '良好', color: 'bg-yellow-500' };
    if (time < 2000) return { label: '一般', color: 'bg-orange-500' };
    return { label: '较差', color: 'bg-red-500' };
  };

  return (
    <Card className="bg-zinc-900 border-zinc-800">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Activity className="w-5 h-5 text-[#00d4aa]" />
          API 性能监控
        </CardTitle>
        <CardDescription className="text-zinc-400">
          API响应时间和错误率统计分析
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {apiMetrics ? (
          <>
            {/* 核心指标概览 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 平均响应时间 */}
              <div className="p-4 rounded-lg bg-zinc-800">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-zinc-400" />
                    <span className="text-sm text-zinc-400">平均响应时间</span>
                  </div>
                  <Badge className={`${getPerformanceLevel(apiMetrics.averageResponseTime).color} text-white text-xs`}>
                    {getPerformanceLevel(apiMetrics.averageResponseTime).label}
                  </Badge>
                </div>
                <div className={`text-2xl font-bold ${getResponseTimeColor(apiMetrics.averageResponseTime)}`}>
                  {apiMetrics.averageResponseTime}ms
                </div>
                <div className="mt-2">
                  <Progress 
                    value={Math.min(100, (apiMetrics.averageResponseTime / 2000) * 100)} 
                    className="h-1"
                  />
                </div>
              </div>

              {/* 错误率 */}
              <div className="p-4 rounded-lg bg-zinc-800">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="w-4 h-4 text-zinc-400" />
                  <span className="text-sm text-zinc-400">错误率</span>
                </div>
                <div className={`text-2xl font-bold ${getErrorRateColor(apiMetrics.errorRate)}`}>
                  {apiMetrics.errorRate}%
                </div>
                <div className="mt-2">
                  <Progress 
                    value={Math.min(100, apiMetrics.errorRate * 10)} 
                    className="h-1"
                  />
                </div>
              </div>

              {/* 请求总数 */}
              <div className="p-4 rounded-lg bg-zinc-800">
                <div className="flex items-center gap-2 mb-2">
                  <Zap className="w-4 h-4 text-zinc-400" />
                  <span className="text-sm text-zinc-400">请求总数</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {apiMetrics.requestCount.toLocaleString()}
                </div>
                <p className="text-xs text-zinc-400 mt-1">
                  最近1小时
                </p>
              </div>
            </div>

            {/* 性能趋势指示器 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 响应时间趋势 */}
              <div className="p-4 rounded-lg bg-zinc-800">
                <h3 className="text-sm font-medium text-zinc-400 mb-3">响应时间分布</h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-zinc-400">优秀 (&lt;500ms)</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-zinc-700 rounded-full h-1">
                        <div className="bg-green-500 h-1 rounded-full" style={{ width: '70%' }} />
                      </div>
                      <span className="text-xs text-zinc-300">70%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-zinc-400">良好 (500-1000ms)</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-zinc-700 rounded-full h-1">
                        <div className="bg-yellow-500 h-1 rounded-full" style={{ width: '20%' }} />
                      </div>
                      <span className="text-xs text-zinc-300">20%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-zinc-400">较慢 (&gt;1000ms)</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-zinc-700 rounded-full h-1">
                        <div className="bg-red-500 h-1 rounded-full" style={{ width: '10%' }} />
                      </div>
                      <span className="text-xs text-zinc-300">10%</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 错误类型分布 */}
              <div className="p-4 rounded-lg bg-zinc-800">
                <h3 className="text-sm font-medium text-zinc-400 mb-3">错误类型分布</h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-zinc-400">4xx 客户端错误</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-zinc-700 rounded-full h-1">
                        <div className="bg-yellow-500 h-1 rounded-full" style={{ width: '60%' }} />
                      </div>
                      <span className="text-xs text-zinc-300">60%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-zinc-400">5xx 服务器错误</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-zinc-700 rounded-full h-1">
                        <div className="bg-red-500 h-1 rounded-full" style={{ width: '30%' }} />
                      </div>
                      <span className="text-xs text-zinc-300">30%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-zinc-400">网络错误</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-zinc-700 rounded-full h-1">
                        <div className="bg-orange-500 h-1 rounded-full" style={{ width: '10%' }} />
                      </div>
                      <span className="text-xs text-zinc-300">10%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 最慢端点排行 */}
            {apiMetrics.slowestEndpoints.length > 0 && (
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-zinc-400">响应时间最慢的端点</h3>
                <div className="space-y-2">
                  {apiMetrics.slowestEndpoints.map((endpoint, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-zinc-800">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="border-zinc-600 text-zinc-300">
                          #{index + 1}
                        </Badge>
                        <span className="text-sm text-white font-mono">
                          {endpoint.endpoint.length > 50 
                            ? `${endpoint.endpoint.substring(0, 47)}...` 
                            : endpoint.endpoint
                          }
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`text-sm font-medium ${getResponseTimeColor(endpoint.avgTime)}`}>
                          {endpoint.avgTime}ms
                        </span>
                        {endpoint.avgTime > 1000 && (
                          <TrendingUp className="w-4 h-4 text-red-400" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 性能建议 */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-zinc-400">性能优化建议</h3>
              <div className="p-3 rounded-lg bg-zinc-800 border-l-4 border-[#00d4aa]">
                <div className="text-sm text-zinc-300 space-y-1">
                  {apiMetrics.averageResponseTime > 1000 && (
                    <div>• 平均响应时间较高，建议优化数据库查询和API逻辑</div>
                  )}
                  {apiMetrics.errorRate > 5 && (
                    <div>• 错误率偏高，建议检查API错误处理和输入验证</div>
                  )}
                  {apiMetrics.requestCount > 1000 && (
                    <div>• 请求量较大，建议考虑添加缓存和负载均衡</div>
                  )}
                  {apiMetrics.averageResponseTime < 500 && apiMetrics.errorRate < 1 && (
                    <div>• API性能表现良好，继续保持当前优化水平</div>
                  )}
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="flex items-center justify-center py-8">
            <div className="text-center text-zinc-400">
              <Activity className="w-8 h-8 mx-auto mb-2" />
              <p>暂无API性能数据</p>
              <p className="text-xs mt-1">数据将在API调用后显示</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default APIMetricsChart;
