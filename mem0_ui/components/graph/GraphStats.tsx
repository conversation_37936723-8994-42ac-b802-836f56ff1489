'use client';

import React, { useEffect, useState } from 'react';
import { Network, GitBranch, Activity, Users } from 'lucide-react';
import { useSelector } from 'react-redux';

import StatCard from '@/components/mem0/StatCard';
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';
import { RootState } from '@/store/store';
import { GraphMemoryStats as GraphMemoryStatsType } from '@/types/graph-memory';

interface GraphStatsProps {
  className?: string;
}

const GraphStats: React.FC<GraphStatsProps> = ({ className = '' }) => {
  const [stats, setStats] = useState<GraphMemoryStatsType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [previousStats, setPreviousStats] = useState<GraphMemoryStatsType | null>(null);

  const { fetchGraphStats } = useGraphMemoryApi();
  const filters = useSelector((state: RootState) => state.graphMemory.filters);

  // 计算趋势数据
  const calculateTrend = (current: number, previous: number) => {
    if (previous === 0) return { value: 0, isPositive: true };
    const change = ((current - previous) / previous) * 100;
    return {
      value: Math.abs(Math.round(change)),
      isPositive: change >= 0
    };
  };

  const fetchStats = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const newStats = await fetchGraphStats(filters);
      
      // 保存之前的统计数据用于趋势计算
      if (stats) {
        setPreviousStats(stats);
      }
      
      setStats(newStats);
    } catch (err) {
      console.error('Failed to fetch graph stats:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch graph statistics');
      
      // 提供默认数据以防API失败
      setStats({
        total_entities: 0,
        total_relations: 0,
        graph_density: 0,
        active_entities: 0,
        entity_types_count: {},
        relation_types_count: {},
        avg_connections_per_entity: 0,
        most_connected_entities: []
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    
    // 设置定时刷新（每30秒）
    const interval = setInterval(fetchStats, 30000);
    return () => clearInterval(interval);
  }, [filters]); // 当筛选条件变化时重新获取数据

  // 计算趋势
  const trends = previousStats && stats ? {
    entities: calculateTrend(stats.total_entities, previousStats.total_entities),
    relations: calculateTrend(stats.total_relations, previousStats.total_relations),
    density: calculateTrend(stats.graph_density, previousStats.graph_density),
    active: calculateTrend(stats.active_entities, previousStats.active_entities)
  } : undefined;

  if (error && !stats) {
    return (
      <div className={`bg-zinc-900 rounded-lg border border-zinc-800 p-6 ${className}`}>
        <div className="text-center">
          <div className="text-red-400 mb-2">⚠️ 统计数据加载失败</div>
          <p className="text-zinc-400 text-sm mb-4">{error}</p>
          <button
            onClick={fetchStats}
            className="px-4 py-2 bg-[#00d4aa] text-black rounded-lg hover:bg-[#00d4aa]/90 transition-colors text-sm font-medium"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* 统计卡片网格 */}
      <div className="bg-zinc-900 rounded-lg border border-zinc-800 p-4 md:p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          <StatCard
            title="实体总数"
            value={stats?.total_entities || 0}
            icon={Users}
            trend={trends?.entities}
            subtitle="图中的实体节点数量"
            isLoading={isLoading}
          />
          
          <StatCard
            title="关系总数"
            value={stats?.total_relations || 0}
            icon={GitBranch}
            trend={trends?.relations}
            subtitle="实体间的关系连接数"
            isLoading={isLoading}
          />
          
          <StatCard
            title="图密度"
            value={stats?.graph_density ? `${(stats.graph_density * 100).toFixed(1)}%` : '0%'}
            icon={Network}
            trend={trends?.density}
            subtitle="图的连接密集程度"
            isLoading={isLoading}
          />

          <StatCard
            title="活跃实体"
            value={stats?.active_entities || 0}
            icon={Activity}
            trend={trends?.active}
            subtitle="当前活跃的实体数量"
            isLoading={isLoading}
          />
        </div>
      </div>

      {/* 详细统计信息 */}
      {stats && !isLoading && (
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 实体类型分布 */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-800 p-4">
            <h3 className="text-lg font-semibold text-white mb-3">实体类型分布</h3>
            <div className="space-y-2">
              {Object.entries(stats.entity_types_count).length > 0 ? (
                Object.entries(stats.entity_types_count)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 5)
                  .map(([type, count]) => (
                    <div key={type} className="flex justify-between items-center">
                      <span className="text-zinc-300 capitalize">{type.replace(/_/g, ' ')}</span>
                      <span className="text-[#00d4aa] font-medium">{count}</span>
                    </div>
                  ))
              ) : (
                <p className="text-zinc-500 text-sm">暂无数据</p>
              )}
            </div>
          </div>

          {/* 关系类型分布 */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-800 p-4">
            <h3 className="text-lg font-semibold text-white mb-3">关系类型分布</h3>
            <div className="space-y-2">
              {Object.entries(stats.relation_types_count).length > 0 ? (
                Object.entries(stats.relation_types_count)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 5)
                  .map(([type, count]) => (
                    <div key={type} className="flex justify-between items-center">
                      <span className="text-zinc-300 capitalize">{type.replace(/_/g, ' ')}</span>
                      <span className="text-[#00d4aa] font-medium">{count}</span>
                    </div>
                  ))
              ) : (
                <p className="text-zinc-500 text-sm">暂无数据</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 最连接实体 */}
      {stats && stats.most_connected_entities.length > 0 && !isLoading && (
        <div className="mt-4 bg-zinc-900 rounded-lg border border-zinc-800 p-4">
          <h3 className="text-lg font-semibold text-white mb-3">最连接实体</h3>
          <div className="space-y-2">
            {stats.most_connected_entities.slice(0, 5).map((entity, index) => (
              <div key={entity.id} className="flex justify-between items-center">
                <div className="flex items-center">
                  <span className="text-zinc-500 text-sm mr-2">#{index + 1}</span>
                  <span className="text-zinc-300">{entity.name}</span>
                </div>
                <span className="text-[#00d4aa] font-medium">{entity.connections} 连接</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 平均连接数 */}
      {stats && !isLoading && (
        <div className="mt-4 bg-zinc-900 rounded-lg border border-zinc-800 p-4">
          <div className="flex justify-between items-center">
            <span className="text-zinc-300">平均每个实体的连接数</span>
            <span className="text-[#00d4aa] font-medium text-lg">
              {stats.avg_connections_per_entity.toFixed(1)}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default GraphStats;
