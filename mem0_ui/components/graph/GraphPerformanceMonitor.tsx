/**
 * Graph Memory 性能监控组件
 * 
 * 实时显示图可视化的性能指标：
 * - 渲染时间和帧率
 * - 内存使用情况
 * - 设备性能评估
 * - LOD级别和渲染策略
 * - 缓存命中率
 */

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  Zap, 
  Eye, 
  TrendingUp,
  Settings,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import GraphPerformanceManager from '@/lib/performance/GraphPerformanceManager';

interface GraphPerformanceMonitorProps {
  isVisible?: boolean;
  onToggle?: (visible: boolean) => void;
  className?: string;
}

interface PerformanceData {
  renderTime: number;
  frameRate: number;
  memoryUsage: number;
  nodeCount: number;
  edgeCount: number;
  lodLevel: string;
  deviceScore: number;
  cacheHitRate: number;
}

const GraphPerformanceMonitor: React.FC<GraphPerformanceMonitorProps> = ({
  isVisible = false,
  onToggle,
  className = ''
}) => {
  const [performanceData, setPerformanceData] = useState<PerformanceData>({
    renderTime: 0,
    frameRate: 60,
    memoryUsage: 0,
    nodeCount: 0,
    edgeCount: 0,
    lodLevel: 'HIGH',
    deviceScore: 50,
    cacheHitRate: 0
  });

  const [isExpanded, setIsExpanded] = useState(false);
  const [performanceManager] = useState(() => GraphPerformanceManager.getInstance());

  // 更新性能数据
  const updatePerformanceData = useCallback(async () => {
    try {
      const stats = performanceManager.getPerformanceStats();
      const capability = await performanceManager.assessDeviceCapability();

      if (stats) {
        setPerformanceData(prev => ({
          ...prev,
          renderTime: stats.averageRenderTime,
          frameRate: stats.averageFrameRate,
          memoryUsage: stats.averageMemoryUsage,
          deviceScore: capability.score,
          // 这些值通常从其他地方获取
          nodeCount: prev.nodeCount,
          edgeCount: prev.edgeCount,
          lodLevel: prev.lodLevel,
          cacheHitRate: prev.cacheHitRate
        }));
      }
    } catch (error) {
      console.warn('Failed to update performance data:', error);
    }
  }, [performanceManager]);

  // 定期更新性能数据
  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(updatePerformanceData, 1000);
    updatePerformanceData(); // 立即更新一次

    return () => clearInterval(interval);
  }, [isVisible, updatePerformanceData]);

  // 获取性能等级颜色
  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value >= thresholds.good) return 'text-green-400';
    if (value >= thresholds.warning) return 'text-yellow-400';
    return 'text-red-400';
  };

  // 获取内存使用颜色
  const getMemoryColor = (usage: number) => {
    if (usage < 100) return 'text-green-400';
    if (usage < 200) return 'text-yellow-400';
    return 'text-red-400';
  };

  // 获取设备性能等级
  const getDeviceCategory = (score: number) => {
    if (score >= 80) return { label: 'High', color: 'bg-green-500' };
    if (score >= 50) return { label: 'Medium', color: 'bg-yellow-500' };
    return { label: 'Low', color: 'bg-red-500' };
  };

  if (!isVisible) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onToggle?.(true)}
        className="fixed bottom-4 right-4 z-50 bg-zinc-900/90 backdrop-blur-sm border border-zinc-700 text-zinc-300 hover:bg-zinc-800"
      >
        <Activity className="h-4 w-4 mr-2" />
        Performance
      </Button>
    );
  }

  const deviceCategory = getDeviceCategory(performanceData.deviceScore);

  return (
    <Card className={`fixed bottom-4 right-4 z-50 w-80 bg-zinc-900/95 backdrop-blur-sm border-zinc-700 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium text-white flex items-center">
            <Activity className="h-4 w-4 mr-2 text-[#00d4aa]" />
            Performance Monitor
          </CardTitle>
          <div className="flex items-center space-x-1">
            <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-zinc-400">
                  {isExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
                </Button>
              </CollapsibleTrigger>
            </Collapsible>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggle?.(false)}
              className="h-6 w-6 p-0 text-zinc-400 hover:text-white"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-3">
        {/* 核心指标 */}
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs text-zinc-400">Render Time</span>
              <span className={`text-xs font-mono ${getPerformanceColor(performanceData.renderTime, { good: 16, warning: 33 })}`}>
                {performanceData.renderTime}ms
              </span>
            </div>
            <Progress 
              value={Math.min(100, (performanceData.renderTime / 50) * 100)} 
              className="h-1"
            />
          </div>

          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs text-zinc-400">Frame Rate</span>
              <span className={`text-xs font-mono ${getPerformanceColor(performanceData.frameRate, { good: 50, warning: 30 })}`}>
                {performanceData.frameRate}fps
              </span>
            </div>
            <Progress 
              value={(performanceData.frameRate / 60) * 100} 
              className="h-1"
            />
          </div>
        </div>

        {/* 内存和设备信息 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <HardDrive className="h-3 w-3 text-zinc-400" />
            <span className="text-xs text-zinc-400">Memory</span>
            <span className={`text-xs font-mono ${getMemoryColor(performanceData.memoryUsage)}`}>
              {performanceData.memoryUsage}MB
            </span>
          </div>
          <Badge className={`${deviceCategory.color} text-white text-xs`}>
            {deviceCategory.label}
          </Badge>
        </div>

        {/* 数据统计 */}
        <div className="flex items-center justify-between text-xs text-zinc-400">
          <span>Nodes: {performanceData.nodeCount}</span>
          <span>Edges: {performanceData.edgeCount}</span>
          <span>LOD: {performanceData.lodLevel}</span>
        </div>

        {/* 详细信息（可折叠） */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent className="space-y-3">
            {/* 设备性能详情 */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Cpu className="h-3 w-3 text-zinc-400" />
                <span className="text-xs text-zinc-400">Device Performance</span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-zinc-400">Score</span>
                  <span className="text-white">{performanceData.deviceScore}/100</span>
                </div>
                <Progress value={performanceData.deviceScore} className="h-1" />
              </div>
            </div>

            {/* 缓存性能 */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Zap className="h-3 w-3 text-zinc-400" />
                <span className="text-xs text-zinc-400">Cache Performance</span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-zinc-400">Hit Rate</span>
                  <span className="text-white">{performanceData.cacheHitRate.toFixed(1)}%</span>
                </div>
                <Progress value={performanceData.cacheHitRate} className="h-1" />
              </div>
            </div>

            {/* 优化建议 */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-3 w-3 text-zinc-400" />
                <span className="text-xs text-zinc-400">Optimization Tips</span>
              </div>
              <div className="text-xs text-zinc-300 space-y-1">
                {performanceData.renderTime > 33 && (
                  <div className="text-yellow-400">• Consider reducing node count or enabling LOD</div>
                )}
                {performanceData.memoryUsage > 200 && (
                  <div className="text-red-400">• High memory usage detected</div>
                )}
                {performanceData.frameRate < 30 && (
                  <div className="text-red-400">• Low frame rate - enable virtualization</div>
                )}
                {performanceData.cacheHitRate < 50 && (
                  <div className="text-yellow-400">• Low cache hit rate - consider preloading</div>
                )}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="flex-1 h-7 text-xs border-zinc-700 text-zinc-300 hover:bg-zinc-800"
                onClick={() => {
                  // 清除性能缓存
                  performanceManager.cleanup();
                  updatePerformanceData();
                }}
              >
                <Settings className="h-3 w-3 mr-1" />
                Reset
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex-1 h-7 text-xs border-zinc-700 text-zinc-300 hover:bg-zinc-800"
                onClick={() => {
                  // 导出性能报告
                  const report = {
                    timestamp: new Date().toISOString(),
                    performance: performanceData,
                    userAgent: navigator.userAgent
                  };
                  console.log('Performance Report:', report);
                }}
              >
                <Eye className="h-3 w-3 mr-1" />
                Export
              </Button>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
};

export default GraphPerformanceMonitor;
