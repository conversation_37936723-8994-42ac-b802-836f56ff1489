'use client';

import React from 'react';
import { 
  Plus, 
  Edit, 
  Trash2, 
  User, 
  Bot, 
  Package, 
  Tag,
  Clock,
  GitBranch,
  Database,
  Eye,
  Layout,
  Filter,
  ChevronDown,
  ChevronRight,
  Hash,
  ArrowRight,
  Info
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { GraphMemoryHistoryItem, GraphMemoryOperationType } from '@/types/graph-memory';
import { formatDate } from '@/lib/helpers';

interface GraphHistoryItemProps {
  data: GraphMemoryHistoryItem;
  isLast?: boolean;
  isExpanded?: boolean;
  onToggleExpansion?: () => void;
}

const GraphHistoryItem: React.FC<GraphHistoryItemProps> = ({ 
  data, 
  isLast = false,
  isExpanded = false,
  onToggleExpansion
}) => {
  // 获取操作图标
  const getOperationIcon = (operation: GraphMemoryOperationType) => {
    switch (operation) {
      case 'entity_create':
        return <Plus className="w-4 h-4 text-green-400" />;
      case 'entity_update':
        return <Edit className="w-4 h-4 text-blue-400" />;
      case 'entity_delete':
        return <Trash2 className="w-4 h-4 text-red-400" />;
      case 'relation_create':
        return <GitBranch className="w-4 h-4 text-green-400" />;
      case 'relation_update':
        return <GitBranch className="w-4 h-4 text-blue-400" />;
      case 'relation_delete':
        return <GitBranch className="w-4 h-4 text-red-400" />;
      case 'graph_layout_change':
        return <Layout className="w-4 h-4 text-purple-400" />;
      case 'filter_change':
        return <Filter className="w-4 h-4 text-yellow-400" />;
      case 'view_change':
        return <Eye className="w-4 h-4 text-cyan-400" />;
      default:
        return <Clock className="w-4 h-4 text-zinc-400" />;
    }
  };

  // 获取操作颜色
  const getOperationColor = (operation: GraphMemoryOperationType) => {
    switch (operation) {
      case 'entity_create':
      case 'relation_create':
        return 'text-green-400 border-green-400/20 bg-green-400/10';
      case 'entity_update':
      case 'relation_update':
        return 'text-blue-400 border-blue-400/20 bg-blue-400/10';
      case 'entity_delete':
      case 'relation_delete':
        return 'text-red-400 border-red-400/20 bg-red-400/10';
      case 'graph_layout_change':
        return 'text-purple-400 border-purple-400/20 bg-purple-400/10';
      case 'filter_change':
        return 'text-yellow-400 border-yellow-400/20 bg-yellow-400/10';
      case 'view_change':
        return 'text-cyan-400 border-cyan-400/20 bg-cyan-400/10';
      default:
        return 'text-zinc-400 border-zinc-400/20 bg-zinc-400/10';
    }
  };

  // 获取操作文本
  const getOperationText = (operation: GraphMemoryOperationType) => {
    switch (operation) {
      case 'entity_create':
        return 'Created Entity';
      case 'entity_update':
        return 'Updated Entity';
      case 'entity_delete':
        return 'Deleted Entity';
      case 'relation_create':
        return 'Created Relation';
      case 'relation_update':
        return 'Updated Relation';
      case 'relation_delete':
        return 'Deleted Relation';
      case 'graph_layout_change':
        return 'Changed Layout';
      case 'filter_change':
        return 'Applied Filter';
      case 'view_change':
        return 'Changed View';
      default:
        return 'Unknown Operation';
    }
  };

  // 获取目标类型图标
  const getTargetTypeIcon = (targetType: string) => {
    switch (targetType) {
      case 'entity':
        return <Database className="w-3 h-3" />;
      case 'relation':
        return <GitBranch className="w-3 h-3" />;
      case 'view':
        return <Eye className="w-3 h-3" />;
      default:
        return <Package className="w-3 h-3" />;
    }
  };

  // 格式化变更内容
  const formatChanges = (changes: Record<string, unknown>) => {
    if (!changes) return null;

    const { before, after } = changes;

    return (
      <div className="space-y-2">
        {before !== null && before !== undefined && (
          <div className="bg-red-900/20 border border-red-800/50 rounded p-2">
            <div className="text-xs text-red-300 font-medium mb-1">Before:</div>
            <pre className="text-xs text-red-200 whitespace-pre-wrap overflow-x-auto">
              {typeof before === 'string' ? before : JSON.stringify(before, null, 2)}
            </pre>
          </div>
        )}
        {after !== null && after !== undefined && (
          <div className="bg-green-900/20 border border-green-800/50 rounded p-2">
            <div className="text-xs text-green-300 font-medium mb-1">After:</div>
            <pre className="text-xs text-green-200 whitespace-pre-wrap overflow-x-auto">
              {typeof after === 'string' ? after : JSON.stringify(after, null, 2)}
            </pre>
          </div>
        )}
      </div>
    );
  };

  // 格式化元数据
  const formatMetadata = (metadata: Record<string, unknown>) => {
    if (!metadata || Object.keys(metadata).length === 0) return null;

    return (
      <div className="bg-zinc-900/50 border border-zinc-800/50 rounded p-2">
        <div className="text-xs text-zinc-300 font-medium mb-1">Metadata:</div>
        <pre className="text-xs text-zinc-400 whitespace-pre-wrap overflow-x-auto">
          {JSON.stringify(metadata, null, 2)}
        </pre>
      </div>
    );
  };

  const hasDetails = data.changes || (data.metadata && Object.keys(data.metadata).length > 0);

  return (
    <div className="relative">
      {/* 时间线连接线 */}
      {!isLast && (
        <div className="absolute left-6 top-12 w-0.5 h-full bg-zinc-800"></div>
      )}
      
      {/* 历史项目卡片 */}
      <div className="flex items-start space-x-4">
        {/* 操作图标 */}
        <div className={`
          flex-shrink-0 w-12 h-12 rounded-full border-2 flex items-center justify-center
          ${getOperationColor(data.operation)}
        `}>
          {getOperationIcon(data.operation)}
        </div>

        {/* 内容区域 */}
        <div className="flex-1 min-w-0">
          <Card className="bg-zinc-900/50 border-zinc-800/50 hover:bg-zinc-900/70 transition-colors">
            <div className="p-4">
              {/* 头部信息 */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Badge 
                    variant="outline" 
                    className={`text-xs ${getOperationColor(data.operation)}`}
                  >
                    {getOperationText(data.operation)}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    <div className="flex items-center space-x-1">
                      {getTargetTypeIcon(data.target_type)}
                      <span className="capitalize">{data.target_type}</span>
                    </div>
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-zinc-400">
                    {formatDate(data.timestamp)}
                  </span>
                  {hasDetails && onToggleExpansion && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onToggleExpansion}
                      className="h-6 w-6 p-0 text-zinc-400 hover:text-zinc-300"
                    >
                      {isExpanded ? (
                        <ChevronDown className="h-3 w-3" />
                      ) : (
                        <ChevronRight className="h-3 w-3" />
                      )}
                    </Button>
                  )}
                </div>
              </div>

              {/* 描述 */}
              <p className="text-sm text-zinc-300 mb-2">
                {data.description}
              </p>

              {/* 目标ID */}
              <div className="flex items-center space-x-2 text-xs text-zinc-500">
                <Hash className="w-3 h-3" />
                <span>Target: {data.target_id}</span>
                {data.user_id && (
                  <>
                    <ArrowRight className="w-3 h-3" />
                    <User className="w-3 h-3" />
                    <span>User: {data.user_id}</span>
                  </>
                )}
              </div>

              {/* 展开的详细信息 */}
              {isExpanded && hasDetails && (
                <div className="mt-4 space-y-3 border-t border-zinc-800 pt-3">
                  {data.changes && (
                    <div>
                      <div className="flex items-center space-x-2 mb-2">
                        <Info className="w-4 h-4 text-zinc-400" />
                        <span className="text-sm font-medium text-zinc-300">Changes</span>
                      </div>
                      {formatChanges(data.changes)}
                    </div>
                  )}
                  
                  {data.metadata && Object.keys(data.metadata).length > 0 && (
                    <div>
                      <div className="flex items-center space-x-2 mb-2">
                        <Package className="w-4 h-4 text-zinc-400" />
                        <span className="text-sm font-medium text-zinc-300">Additional Info</span>
                      </div>
                      {formatMetadata(data.metadata)}
                    </div>
                  )}
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default GraphHistoryItem;
