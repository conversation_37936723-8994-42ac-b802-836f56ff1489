'use client';

import React, { memo, useMemo } from 'react';
import {
  EdgeProps,
  getBezierPath,
  EdgeLabelRenderer,
  BaseEdge
} from 'reactflow';

import { Badge } from '@/components/ui/badge';

// 边数据接口
interface GraphEdgeData {
  id: string;
  label?: string;
  relation_type: string;
  weight?: number;
  description?: string;
  properties?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

// 关系类型颜色映射
const getRelationColor = (type: string, weight?: number) => {
  const baseColors: Record<string, { stroke: string; label: string }> = {
    knows: { stroke: '#3b82f6', label: 'bg-blue-500/10 text-blue-400 border-blue-500/20' },
    works_at: { stroke: '#8b5cf6', label: 'bg-purple-500/10 text-purple-400 border-purple-500/20' },
    lives_in: { stroke: '#10b981', label: 'bg-green-500/10 text-green-400 border-green-500/20' },
    related_to: { stroke: '#f59e0b', label: 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20' },
    part_of: { stroke: '#ef4444', label: 'bg-red-500/10 text-red-400 border-red-500/20' },
    created_by: { stroke: '#06b6d4', label: 'bg-cyan-500/10 text-cyan-400 border-cyan-500/20' },
    depends_on: { stroke: '#ec4899', label: 'bg-pink-500/10 text-pink-400 border-pink-500/20' },
    similar_to: { stroke: '#6366f1', label: 'bg-indigo-500/10 text-indigo-400 border-indigo-500/20' },
    other: { stroke: '#6b7280', label: 'bg-zinc-500/10 text-zinc-400 border-zinc-500/20' }
  };

  const colors = baseColors[type.toLowerCase()] || baseColors.other;
  
  // 根据权重调整透明度
  let opacity = 0.8;
  if (weight !== undefined) {
    opacity = Math.max(0.3, Math.min(1, weight));
  }

  return {
    ...colors,
    stroke: colors.stroke,
    opacity
  };
};

// 获取边的粗细
const getStrokeWidth = (weight?: number, selected?: boolean) => {
  let baseWidth = 2;
  
  if (weight !== undefined) {
    baseWidth = Math.max(1, Math.min(6, weight * 4));
  }
  
  if (selected) {
    baseWidth += 2;
  }
  
  return baseWidth;
};

// 格式化关系类型显示
const formatRelationType = (type: string) => {
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const GraphEdge: React.FC<EdgeProps<GraphEdgeData>> = ({
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  data,
  selected
}) => {
  const colors = useMemo(() => 
    getRelationColor(data?.relation_type || 'other', data?.weight), 
    [data?.relation_type, data?.weight]
  );
  
  const strokeWidth = useMemo(() => 
    getStrokeWidth(data?.weight, selected), 
    [data?.weight, selected]
  );

  // 计算贝塞尔路径
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  // 边样式
  const edgeStyle = useMemo(() => ({
    stroke: colors.stroke,
    strokeWidth,
    opacity: colors.opacity,
    strokeDasharray: selected ? '5,5' : undefined,
    filter: selected ? 'drop-shadow(0 0 6px rgba(0, 212, 170, 0.6))' : undefined,
  }), [colors, strokeWidth, selected]);

  // 标记样式
  const markerEndId = useMemo(() =>
    `arrow-${data?.relation_type || 'default'}-${colors.stroke.replace('#', '')}`,
    [data?.relation_type, colors.stroke]
  );

  // 权重显示
  const getWeightDisplay = () => {
    if (data?.weight === undefined) return null;
    
    const percentage = Math.round(data.weight * 100);
    let intensity = 'Low';
    let intensityColor = 'text-zinc-400';
    
    if (percentage >= 80) {
      intensity = 'High';
      intensityColor = 'text-green-400';
    } else if (percentage >= 50) {
      intensity = 'Medium';
      intensityColor = 'text-yellow-400';
    } else if (percentage >= 30) {
      intensity = 'Low';
      intensityColor = 'text-orange-400';
    } else {
      intensity = 'Weak';
      intensityColor = 'text-red-400';
    }

    return { percentage, intensity, intensityColor };
  };

  const weightDisplay = getWeightDisplay();

  return (
    <>
      {/* 基础边路径 */}
      <BaseEdge
        path={edgePath}
        style={edgeStyle}
        markerEnd={markerEndId}
      />
      
      {/* 边标签 */}
      <EdgeLabelRenderer>
        <div
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            pointerEvents: 'all',
          }}
          className="nodrag nopan"
        >
          <div className={`
            flex flex-col items-center gap-1 p-2 rounded-lg border
            bg-zinc-900/95 backdrop-blur-sm transition-all duration-200
            ${selected ? 'border-[#00d4aa] shadow-lg shadow-[#00d4aa]/30' : 'border-zinc-700'}
            hover:border-zinc-600 hover:shadow-md
          `}>
            {/* 关系类型 */}
            <Badge 
              variant="secondary" 
              className={`text-xs ${colors.label} px-2 py-0.5`}
            >
              {formatRelationType(data?.relation_type || 'unknown')}
            </Badge>
            
            {/* 权重信息 */}
            {weightDisplay && (
              <div className="flex items-center gap-1 text-xs">
                <span className="text-zinc-500">强度:</span>
                <span className={weightDisplay.intensityColor}>
                  {weightDisplay.intensity}
                </span>
                <span className="text-zinc-400">
                  ({weightDisplay.percentage}%)
                </span>
              </div>
            )}
            
            {/* 自定义标签 */}
            {data?.label && (
              <div className="text-xs text-zinc-300 max-w-[120px] text-center truncate">
                {data.label}
              </div>
            )}
            
            {/* 描述 */}
            {data?.description && (
              <div className="text-xs text-zinc-400 max-w-[150px] text-center truncate">
                {data.description}
              </div>
            )}
            
            {/* 选中状态指示器 */}
            {selected && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-[#00d4aa] rounded-full animate-pulse" />
            )}
          </div>
        </div>
      </EdgeLabelRenderer>
    </>
  );
};

export default memo(GraphEdge);
