/**
 * 虚拟化图渲染器
 * 
 * 用于优化大数据量图的渲染性能，通过以下技术：
 * - 视口裁剪：只渲染可见区域的节点和边
 * - 分层渲染：根据重要性分层渲染
 * - 懒加载：按需加载节点详细信息
 * - 内存池：复用DOM元素减少GC压力
 */

import React, { useMemo, useCallback, useRef, useEffect } from 'react';

import { GraphNode, GraphEdge } from '@/types/graph-memory';
import GraphPerformanceManager, { LODLevel } from '@/lib/performance/GraphPerformanceManager';

interface ViewportBounds {
  x: number;
  y: number;
  width: number;
  height: number;
  zoom: number;
}

interface VirtualizedGraphRendererProps {
  nodes: GraphNode[];
  edges: GraphEdge[];
  viewport: ViewportBounds;
  onNodeClick?: (node: GraphNode) => void;
  onEdgeClick?: (edge: GraphEdge) => void;
  className?: string;
}

interface VisibilityInfo {
  visible: boolean;
  distance: number;
  priority: number;
}

const VirtualizedGraphRenderer: React.FC<VirtualizedGraphRendererProps> = ({
  nodes,
  edges,
  viewport,
  onNodeClick,
  onEdgeClick,
  className = ''
}) => {
  const performanceManager = useRef(GraphPerformanceManager.getInstance());
  const renderStartTime = useRef<number>(0);

  // 计算节点可见性
  const nodeVisibility = useMemo(() => {
    const visibility = new Map<string, VisibilityInfo>();
    
    nodes.forEach(node => {
      const nodeX = node.position.x;
      const nodeY = node.position.y;
      
      // 计算节点是否在视口内
      const buffer = 200; // 视口缓冲区
      const visible = (
        nodeX >= viewport.x - buffer &&
        nodeX <= viewport.x + viewport.width + buffer &&
        nodeY >= viewport.y - buffer &&
        nodeY <= viewport.y + viewport.height + buffer
      );
      
      // 计算距离视口中心的距离
      const centerX = viewport.x + viewport.width / 2;
      const centerY = viewport.y + viewport.height / 2;
      const distance = Math.sqrt(
        Math.pow(nodeX - centerX, 2) + Math.pow(nodeY - centerY, 2)
      );
      
      // 计算渲染优先级
      let priority = 0;
      if (visible) {
        priority = 100 - Math.min(distance / 10, 99); // 距离越近优先级越高
        
        // 根据节点类型调整优先级
        if (node.data.type === 'person') priority += 10;
        if (node.data.type === 'organization') priority += 5;
        
        // 根据连接数调整优先级
        const connectionCount = edges.filter(
          edge => edge.source === node.id || edge.target === node.id
        ).length;
        priority += Math.min(connectionCount * 2, 20);
      }
      
      visibility.set(node.id, { visible, distance, priority });
    });
    
    return visibility;
  }, [nodes, edges, viewport]);

  // 计算边可见性
  const edgeVisibility = useMemo(() => {
    const visibility = new Map<string, VisibilityInfo>();
    
    edges.forEach(edge => {
      const sourceNode = nodes.find(n => n.id === edge.source);
      const targetNode = nodes.find(n => n.id === edge.target);
      
      if (!sourceNode || !targetNode) {
        visibility.set(edge.id, { visible: false, distance: Infinity, priority: 0 });
        return;
      }
      
      const sourceVisible = nodeVisibility.get(sourceNode.id)?.visible || false;
      const targetVisible = nodeVisibility.get(targetNode.id)?.visible || false;
      const visible = sourceVisible || targetVisible;
      
      // 计算边的中点距离
      const midX = (sourceNode.position.x + targetNode.position.x) / 2;
      const midY = (sourceNode.position.y + targetNode.position.y) / 2;
      const centerX = viewport.x + viewport.width / 2;
      const centerY = viewport.y + viewport.height / 2;
      const distance = Math.sqrt(
        Math.pow(midX - centerX, 2) + Math.pow(midY - centerY, 2)
      );
      
      // 计算优先级
      let priority = 0;
      if (visible) {
        priority = 100 - Math.min(distance / 10, 99);
        priority += (edge.data?.weight || 0) * 50; // 权重越高优先级越高
      }
      
      visibility.set(edge.id, { visible, distance, priority });
    });
    
    return visibility;
  }, [nodes, edges, viewport, nodeVisibility]);

  // 获取当前LOD级别
  const lodLevel = useMemo(() => {
    return performanceManager.current.calculateLODLevel(
      nodes.length,
      edges.length,
      viewport.zoom
    );
  }, [nodes.length, edges.length, viewport.zoom]);

  // 筛选和排序可见节点
  const visibleNodes = useMemo(() => {
    renderStartTime.current = performance.now();
    
    const visible = nodes
      .filter(node => nodeVisibility.get(node.id)?.visible)
      .sort((a, b) => {
        const priorityA = nodeVisibility.get(a.id)?.priority || 0;
        const priorityB = nodeVisibility.get(b.id)?.priority || 0;
        return priorityB - priorityA;
      });
    
    // 根据性能限制节点数量
    const maxNodes = performanceManager.current.getPerformanceStats()?.deviceCapability?.category === 'low' ? 50 : 200;
    const limitedNodes = visible.slice(0, maxNodes);
    
    // 应用LOD优化
    return performanceManager.current.optimizeNodesForRendering(limitedNodes, lodLevel);
  }, [nodes, nodeVisibility, lodLevel]);

  // 筛选和排序可见边
  const visibleEdges = useMemo(() => {
    const visible = edges
      .filter(edge => edgeVisibility.get(edge.id)?.visible)
      .sort((a, b) => {
        const priorityA = edgeVisibility.get(a.id)?.priority || 0;
        const priorityB = edgeVisibility.get(b.id)?.priority || 0;
        return priorityB - priorityA;
      });
    
    // 根据性能限制边数量
    const maxEdges = performanceManager.current.getPerformanceStats()?.deviceCapability?.category === 'low' ? 100 : 500;
    const limitedEdges = visible.slice(0, maxEdges);
    
    // 应用LOD优化
    return performanceManager.current.optimizeEdgesForRendering(limitedEdges, lodLevel);
  }, [edges, edgeVisibility, lodLevel]);

  // 记录渲染性能
  useEffect(() => {
    if (renderStartTime.current > 0) {
      const renderTime = performance.now() - renderStartTime.current;
      performanceManager.current.recordPerformanceMetrics(
        renderTime,
        visibleNodes.length,
        visibleEdges.length
      );
    }
  }, [visibleNodes.length, visibleEdges.length]);

  // 节点点击处理
  const handleNodeClick = useCallback((node: GraphNode) => {
    onNodeClick?.(node);
  }, [onNodeClick]);

  // 边点击处理
  const handleEdgeClick = useCallback((edge: GraphEdge) => {
    onEdgeClick?.(edge);
  }, [onEdgeClick]);

  // 渲染节点
  const renderNode = useCallback((node: GraphNode) => {
    const visibility = nodeVisibility.get(node.id);
    if (!visibility?.visible) return null;

    // 根据LOD级别选择渲染样式
    let nodeClass = 'graph-node';
    let showDetails = true;
    
    switch (lodLevel) {
      case LODLevel.MINIMAL:
        nodeClass += ' minimal';
        showDetails = false;
        break;
      case LODLevel.LOW:
        nodeClass += ' low-detail';
        showDetails = false;
        break;
      case LODLevel.MEDIUM:
        nodeClass += ' medium-detail';
        break;
      case LODLevel.HIGH:
      default:
        nodeClass += ' high-detail';
        break;
    }

    return (
      <div
        key={node.id}
        className={nodeClass}
        style={{
          position: 'absolute',
          left: node.position.x,
          top: node.position.y,
          transform: 'translate(-50%, -50%)',
          opacity: Math.max(0.3, 1 - visibility.distance / 1000)
        }}
        onClick={() => handleNodeClick(node)}
      >
        <div className="node-content">
          <div className="node-icon">
            {/* 根据节点类型显示图标 */}
            {node.data.type === 'person' && '👤'}
            {node.data.type === 'organization' && '🏢'}
            {node.data.type === 'location' && '📍'}
            {node.data.type === 'event' && '📅'}
            {node.data.type === 'concept' && '💡'}
            {node.data.type === 'document' && '📄'}
            {node.data.type === 'website' && '🌐'}
            {node.data.type === 'skill' && '🎯'}
            {node.data.type === 'data' && '📊'}
            {!['person', 'organization', 'location', 'event', 'concept', 'document', 'website', 'skill', 'data'].includes(node.data.type) && '⚪'}
          </div>
          {showDetails && (
            <>
              <div className="node-label">{node.data.label}</div>
              {lodLevel >= LODLevel.MEDIUM && node.data.description && (
                <div className="node-description">{node.data.description}</div>
              )}
            </>
          )}
        </div>
      </div>
    );
  }, [nodeVisibility, lodLevel, handleNodeClick]);

  // 渲染边
  const renderEdge = useCallback((edge: GraphEdge) => {
    const visibility = edgeVisibility.get(edge.id);
    if (!visibility?.visible) return null;

    const sourceNode = nodes.find(n => n.id === edge.source);
    const targetNode = nodes.find(n => n.id === edge.target);
    
    if (!sourceNode || !targetNode) return null;

    const strokeWidth = Math.max(1, (edge.data?.weight || 0.5) * 4);
    const opacity = Math.max(0.2, 1 - visibility.distance / 1000);

    return (
      <line
        key={edge.id}
        x1={sourceNode.position.x}
        y1={sourceNode.position.y}
        x2={targetNode.position.x}
        y2={targetNode.position.y}
        stroke="#00d4aa"
        strokeWidth={strokeWidth}
        opacity={opacity}
        onClick={() => handleEdgeClick(edge)}
        style={{ cursor: 'pointer' }}
      />
    );
  }, [nodes, edgeVisibility, handleEdgeClick]);

  return (
    <div className={`virtualized-graph-renderer ${className}`}>
      {/* SVG层用于渲染边 */}
      <svg
        className="edges-layer"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none'
        }}
      >
        {visibleEdges.map(renderEdge)}
      </svg>
      
      {/* DOM层用于渲染节点 */}
      <div
        className="nodes-layer"
        style={{
          position: 'relative',
          width: '100%',
          height: '100%'
        }}
      >
        {visibleNodes.map(renderNode)}
      </div>
      
      {/* 性能信息显示（开发模式） */}
      {process.env.NODE_ENV === 'development' && (
        <div className="performance-info" style={{
          position: 'absolute',
          top: 10,
          right: 10,
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '8px',
          borderRadius: '4px',
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          <div>LOD: {LODLevel[lodLevel]}</div>
          <div>Nodes: {visibleNodes.length}/{nodes.length}</div>
          <div>Edges: {visibleEdges.length}/{edges.length}</div>
          <div>Zoom: {viewport.zoom.toFixed(2)}</div>
        </div>
      )}
    </div>
  );
};

export default VirtualizedGraphRenderer;
