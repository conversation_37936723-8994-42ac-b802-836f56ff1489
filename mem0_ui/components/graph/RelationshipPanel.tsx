'use client';

import React, { useState, useEffect } from 'react';
import {
  Edit,
  MoreHorizontal,
  Trash2,
  Plus,
  Search,
  Filter,
  GitBranch,
  ArrowRight,
  Calendar,
  Hash,
  Weight,
  AlertTriangle
} from 'lucide-react';
import { useSelector, useDispatch } from 'react-redux';

import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { useToast } from '@/hooks/use-toast';
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';
import { RootState } from '@/store/store';
import { formatDate } from '@/lib/helpers';
import { Mem0GraphRelation, Mem0GraphEntity } from '@/types/graph-memory';

// 关系类型选项
const RELATION_TYPES = [
  'works_at',
  'lives_in',
  'knows',
  'related_to',
  'part_of',
  'owns',
  'manages',
  'collaborates_with',
  'depends_on',
  'influences',
  'created_by',
  'used_by',
  'other'
];

interface RelationshipPanelProps {
  className?: string;
}

interface RelationFormData {
  source_entity_id: string;
  target_entity_id: string;
  relation_type: string;
  description: string;
  weight: number;
  properties: Record<string, any>;
}

interface ExtendedRelation extends Mem0GraphRelation {
  source_entity_name?: string;
  target_entity_name?: string;
}

const RelationshipPanel: React.FC<RelationshipPanelProps> = ({ className = '' }) => {
  const { toast } = useToast();
  const dispatch = useDispatch();
  
  // Redux state
  const { edges, nodes } = useSelector((state: RootState) => state.graphMemory);
  const filters = useSelector((state: RootState) => state.graphMemory.filters);
  
  // API hooks
  const { createRelation, updateRelation, deleteRelation, fetchGraphMemories } = useGraphMemoryApi();
  
  // Local state
  const [selectedRelationIds, setSelectedRelationIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingRelation, setEditingRelation] = useState<ExtendedRelation | null>(null);
  const [deletingRelationId, setDeletingRelationId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [conflicts, setConflicts] = useState<string[]>([]);
  
  // Form state
  const [formData, setFormData] = useState<RelationFormData>({
    source_entity_id: '',
    target_entity_id: '',
    relation_type: 'related_to',
    description: '',
    weight: 1.0,
    properties: {}
  });

  // 从edges中提取关系数据，并添加实体名称
  const relations: ExtendedRelation[] = edges.map(edge => {
    const sourceNode = nodes.find(node => node.id === edge.source);
    const targetNode = nodes.find(node => node.id === edge.target);
    
    return {
      id: edge.id,
      source_entity_id: edge.source,
      target_entity_id: edge.target,
      relation_type: edge.data?.relation_type || (typeof edge.label === 'string' ? edge.label : 'related_to'),
      description: edge.data?.description || '',
      weight: edge.data?.weight || 1.0,
      properties: edge.data?.properties || {},
      created_at: edge.data?.created_at,
      updated_at: edge.data?.updated_at,
      source_entity_name: sourceNode?.data?.label || sourceNode?.data?.name || 'Unknown',
      target_entity_name: targetNode?.data?.label || targetNode?.data?.name || 'Unknown'
    };
  });

  // 获取可用实体列表
  const availableEntities: Mem0GraphEntity[] = nodes.map(node => ({
    id: node.id,
    name: node.data.label || node.data.name || 'Unnamed Entity',
    type: node.data.type || 'other',
    description: node.data.description || '',
    properties: node.data.properties || {}
  }));

  // 筛选关系
  const filteredRelations = relations.filter(relation => {
    const matchesSearch = 
      relation.source_entity_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      relation.target_entity_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      relation.relation_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      relation.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || relation.relation_type === typeFilter;
    return matchesSearch && matchesType;
  });

  // 冲突检测
  const detectConflicts = (sourceId: string, targetId: string, relationType: string, excludeId?: string) => {
    const existingRelations = relations.filter(r => r.id !== excludeId);
    const conflicts: string[] = [];

    // 检查重复关系
    const duplicate = existingRelations.find(r => 
      r.source_entity_id === sourceId && 
      r.target_entity_id === targetId && 
      r.relation_type === relationType
    );
    if (duplicate) {
      conflicts.push('Duplicate relation already exists');
    }

    // 检查循环关系
    const reverse = existingRelations.find(r => 
      r.source_entity_id === targetId && 
      r.target_entity_id === sourceId && 
      r.relation_type === relationType
    );
    if (reverse) {
      conflicts.push('Reverse relation already exists');
    }

    // 检查自引用
    if (sourceId === targetId) {
      conflicts.push('Self-referencing relations are not allowed');
    }

    return conflicts;
  };

  // 选择处理
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRelationIds(filteredRelations.map(relation => relation.id));
    } else {
      setSelectedRelationIds([]);
    }
  };

  const handleSelectRelation = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedRelationIds(prev => [...prev, id]);
    } else {
      setSelectedRelationIds(prev => prev.filter(relationId => relationId !== id));
    }
  };

  const isAllSelected = filteredRelations.length > 0 && 
                       selectedRelationIds.length === filteredRelations.length;
  const isPartiallySelected = selectedRelationIds.length > 0 && 
                             selectedRelationIds.length < filteredRelations.length;

  // 表单处理
  const resetForm = () => {
    setFormData({
      source_entity_id: '',
      target_entity_id: '',
      relation_type: 'related_to',
      description: '',
      weight: 1.0,
      properties: {}
    });
    setConflicts([]);
  };

  const validateForm = () => {
    if (!formData.source_entity_id || !formData.target_entity_id) {
      toast({
        title: 'Error',
        description: 'Please select both source and target entities',
        variant: 'destructive',
      });
      return false;
    }

    const detectedConflicts = detectConflicts(
      formData.source_entity_id,
      formData.target_entity_id,
      formData.relation_type,
      editingRelation?.id
    );

    setConflicts(detectedConflicts);

    if (detectedConflicts.length > 0) {
      toast({
        title: 'Validation Error',
        description: detectedConflicts[0],
        variant: 'destructive',
      });
      return false;
    }

    return true;
  };

  const handleCreateRelation = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      await createRelation({
        source_entity_id: formData.source_entity_id,
        target_entity_id: formData.target_entity_id,
        relation_type: formData.relation_type,
        description: formData.description,
        weight: formData.weight,
        properties: formData.properties
      });

      toast({
        title: 'Success',
        description: 'Relation created successfully',
      });

      setIsCreateDialogOpen(false);
      resetForm();
      
      // 刷新数据
      await fetchGraphMemories(filters);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create relation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditRelation = (relation: ExtendedRelation) => {
    setEditingRelation(relation);
    setFormData({
      source_entity_id: relation.source_entity_id,
      target_entity_id: relation.target_entity_id,
      relation_type: relation.relation_type,
      description: relation.description || '',
      weight: relation.weight || 1.0,
      properties: relation.properties || {}
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateRelation = async () => {
    if (!editingRelation || !validateForm()) return;

    setIsLoading(true);
    try {
      await updateRelation(editingRelation.id, {
        source_entity_id: formData.source_entity_id,
        target_entity_id: formData.target_entity_id,
        relation_type: formData.relation_type,
        description: formData.description,
        weight: formData.weight,
        properties: formData.properties
      });

      toast({
        title: 'Success',
        description: 'Relation updated successfully',
      });

      setIsEditDialogOpen(false);
      setEditingRelation(null);
      resetForm();
      
      // 刷新数据
      await fetchGraphMemories(filters);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update relation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteRelation = async (relationId: string) => {
    setDeletingRelationId(relationId);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteRelation = async () => {
    if (!deletingRelationId) return;

    setIsLoading(true);
    try {
      await deleteRelation(deletingRelationId);

      toast({
        title: 'Success',
        description: 'Relation deleted successfully',
      });

      setIsDeleteDialogOpen(false);
      setDeletingRelationId(null);
      
      // 刷新数据
      await fetchGraphMemories(filters);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete relation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBatchDelete = async () => {
    if (selectedRelationIds.length === 0) return;

    setIsLoading(true);
    try {
      await Promise.all(selectedRelationIds.map(id => deleteRelation(id)));

      toast({
        title: 'Success',
        description: `${selectedRelationIds.length} relations deleted successfully`,
      });

      setSelectedRelationIds([]);
      
      // 刷新数据
      await fetchGraphMemories(filters);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete relations',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={className}>
      <Card className="bg-zinc-900 border-zinc-800">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center">
              <GitBranch className="h-5 w-5 mr-2" />
              Relationship Management
            </CardTitle>
            <div className="flex items-center space-x-2">
              {selectedRelationIds.length > 0 && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBatchDelete}
                  disabled={isLoading}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Selected ({selectedRelationIds.length})
                </Button>
              )}
              <Button
                onClick={() => setIsCreateDialogOpen(true)}
                className="bg-[#00d4aa] text-black hover:bg-[#00d4aa]/90"
                size="sm"
                disabled={availableEntities.length < 2}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Relation
              </Button>
            </div>
          </div>
          
          {/* 搜索和筛选 */}
          <div className="flex items-center space-x-4 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-zinc-400" />
              <Input
                placeholder="Search relations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-zinc-950 border-zinc-800"
              />
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px] border-zinc-700/50 bg-zinc-900">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent className="border-zinc-700/50 bg-zinc-900">
                <SelectItem value="all">All Types</SelectItem>
                {RELATION_TYPES.map(type => (
                  <SelectItem key={type} value={type}>
                    {type.replace(/_/g, ' ').charAt(0).toUpperCase() + type.replace(/_/g, ' ').slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="rounded-md border border-zinc-800">
            <Table>
              <TableHeader>
                <TableRow className="bg-zinc-800 hover:bg-zinc-800">
                  <TableHead className="w-[50px] pl-4">
                    <Checkbox
                      className="data-[state=checked]:border-primary border-zinc-500/50"
                      checked={isAllSelected}
                      data-state={
                        isPartiallySelected
                          ? "indeterminate"
                          : isAllSelected
                          ? "checked"
                          : "unchecked"
                      }
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead className="border-zinc-700">
                    <div className="flex items-center">
                      <Hash className="mr-1 h-4 w-4" />
                      Source
                    </div>
                  </TableHead>
                  <TableHead className="border-zinc-700 w-[50px] text-center">
                    <ArrowRight className="h-4 w-4 mx-auto" />
                  </TableHead>
                  <TableHead className="border-zinc-700">Target</TableHead>
                  <TableHead className="border-zinc-700">
                    <div className="flex items-center">
                      <GitBranch className="mr-1 h-4 w-4" />
                      Type
                    </div>
                  </TableHead>
                  <TableHead className="border-zinc-700">
                    <div className="flex items-center">
                      <Weight className="mr-1 h-4 w-4" />
                      Weight
                    </div>
                  </TableHead>
                  <TableHead className="border-zinc-700">Description</TableHead>
                  <TableHead className="w-[140px] border-zinc-700">
                    <div className="flex items-center justify-center">
                      <Calendar className="mr-1 h-4 w-4" />
                      Created
                    </div>
                  </TableHead>
                  <TableHead className="text-right border-zinc-700">
                    <div className="flex items-center justify-end">
                      <MoreHorizontal className="h-4 w-4 mr-2" />
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRelations.map((relation) => (
                  <TableRow
                    key={relation.id}
                    className={`hover:bg-zinc-900/50 ${isLoading ? "animate-pulse opacity-50" : ""}`}
                  >
                    <TableCell className="pl-4">
                      <Checkbox
                        className="data-[state=checked]:border-primary border-zinc-500/50"
                        checked={selectedRelationIds.includes(relation.id)}
                        onCheckedChange={(checked) =>
                          handleSelectRelation(relation.id, checked as boolean)
                        }
                      />
                    </TableCell>
                    <TableCell className="font-medium text-white">
                      {relation.source_entity_name}
                    </TableCell>
                    <TableCell className="text-center">
                      <ArrowRight className="h-4 w-4 text-zinc-400" />
                    </TableCell>
                    <TableCell className="font-medium text-white">
                      {relation.target_entity_name}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className="capitalize">
                        {relation.relation_type.replace(/_/g, ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <div className="w-12 h-2 bg-zinc-700 rounded-full mr-2">
                          <div 
                            className="h-full bg-[#00d4aa] rounded-full" 
                            style={{ width: `${(relation.weight || 1) * 100}%` }}
                          />
                        </div>
                        <span className="text-sm text-zinc-400">
                          {((relation.weight || 1) * 100).toFixed(0)}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-zinc-300 max-w-[200px] truncate">
                      {relation.description || 'No description'}
                    </TableCell>
                    <TableCell className="text-center text-zinc-400">
                      {relation.created_at ? formatDate(relation.created_at) : 'Unknown'}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="bg-zinc-900 border-zinc-800"
                        >
                          <DropdownMenuItem
                            className="cursor-pointer"
                            onClick={() => handleEditRelation(relation)}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator className="bg-zinc-800" />
                          <DropdownMenuItem
                            className="cursor-pointer text-red-400 hover:text-red-300"
                            onClick={() => handleDeleteRelation(relation.id)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredRelations.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8 text-zinc-500">
                      {relations.length === 0 ? 'No relations found' : 'No relations match your filters'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Create Relation Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="bg-zinc-900 border-zinc-800 text-white max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Relation</DialogTitle>
            <DialogDescription className="text-zinc-400">
              Add a new relation between entities in the graph memory.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {conflicts.length > 0 && (
              <div className="bg-red-900/20 border border-red-800 rounded-md p-3">
                <div className="flex items-center">
                  <AlertTriangle className="h-4 w-4 text-red-400 mr-2" />
                  <span className="text-red-400 font-medium">Validation Issues:</span>
                </div>
                <ul className="mt-2 text-sm text-red-300">
                  {conflicts.map((conflict, index) => (
                    <li key={index}>• {conflict}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="source-entity">Source Entity *</Label>
                <Select
                  value={formData.source_entity_id}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, source_entity_id: value }))}
                >
                  <SelectTrigger className="bg-zinc-950 border-zinc-800">
                    <SelectValue placeholder="Select source entity" />
                  </SelectTrigger>
                  <SelectContent className="bg-zinc-900 border-zinc-800">
                    {availableEntities.map(entity => (
                      <SelectItem key={entity.id} value={entity.id}>
                        {entity.name} ({entity.type})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="target-entity">Target Entity *</Label>
                <Select
                  value={formData.target_entity_id}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, target_entity_id: value }))}
                >
                  <SelectTrigger className="bg-zinc-950 border-zinc-800">
                    <SelectValue placeholder="Select target entity" />
                  </SelectTrigger>
                  <SelectContent className="bg-zinc-900 border-zinc-800">
                    {availableEntities.map(entity => (
                      <SelectItem key={entity.id} value={entity.id}>
                        {entity.name} ({entity.type})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="relation-type">Relation Type</Label>
              <Select
                value={formData.relation_type}
                onValueChange={(value) => setFormData(prev => ({ ...prev, relation_type: value }))}
              >
                <SelectTrigger className="bg-zinc-950 border-zinc-800">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-zinc-900 border-zinc-800">
                  {RELATION_TYPES.map(type => (
                    <SelectItem key={type} value={type}>
                      {type.replace(/_/g, ' ').charAt(0).toUpperCase() + type.replace(/_/g, ' ').slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="weight">Weight: {(formData.weight * 100).toFixed(0)}%</Label>
              <Slider
                value={[formData.weight]}
                onValueChange={(value) => setFormData(prev => ({ ...prev, weight: value[0] }))}
                max={1}
                min={0.1}
                step={0.1}
                className="mt-2"
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="bg-zinc-950 border-zinc-800"
                placeholder="Enter relation description"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsCreateDialogOpen(false)}
              className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateRelation}
              disabled={isLoading || conflicts.length > 0}
              className="bg-[#00d4aa] text-black hover:bg-[#00d4aa]/90"
            >
              {isLoading ? 'Creating...' : 'Create Relation'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Relation Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="bg-zinc-900 border-zinc-800 text-white max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Relation</DialogTitle>
            <DialogDescription className="text-zinc-400">
              Update relation information.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {conflicts.length > 0 && (
              <div className="bg-red-900/20 border border-red-800 rounded-md p-3">
                <div className="flex items-center">
                  <AlertTriangle className="h-4 w-4 text-red-400 mr-2" />
                  <span className="text-red-400 font-medium">Validation Issues:</span>
                </div>
                <ul className="mt-2 text-sm text-red-300">
                  {conflicts.map((conflict, index) => (
                    <li key={index}>• {conflict}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-source-entity">Source Entity *</Label>
                <Select
                  value={formData.source_entity_id}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, source_entity_id: value }))}
                >
                  <SelectTrigger className="bg-zinc-950 border-zinc-800">
                    <SelectValue placeholder="Select source entity" />
                  </SelectTrigger>
                  <SelectContent className="bg-zinc-900 border-zinc-800">
                    {availableEntities.map(entity => (
                      <SelectItem key={entity.id} value={entity.id}>
                        {entity.name} ({entity.type})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="edit-target-entity">Target Entity *</Label>
                <Select
                  value={formData.target_entity_id}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, target_entity_id: value }))}
                >
                  <SelectTrigger className="bg-zinc-950 border-zinc-800">
                    <SelectValue placeholder="Select target entity" />
                  </SelectTrigger>
                  <SelectContent className="bg-zinc-900 border-zinc-800">
                    {availableEntities.map(entity => (
                      <SelectItem key={entity.id} value={entity.id}>
                        {entity.name} ({entity.type})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="edit-relation-type">Relation Type</Label>
              <Select
                value={formData.relation_type}
                onValueChange={(value) => setFormData(prev => ({ ...prev, relation_type: value }))}
              >
                <SelectTrigger className="bg-zinc-950 border-zinc-800">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-zinc-900 border-zinc-800">
                  {RELATION_TYPES.map(type => (
                    <SelectItem key={type} value={type}>
                      {type.replace(/_/g, ' ').charAt(0).toUpperCase() + type.replace(/_/g, ' ').slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="edit-weight">Weight: {(formData.weight * 100).toFixed(0)}%</Label>
              <Slider
                value={[formData.weight]}
                onValueChange={(value) => setFormData(prev => ({ ...prev, weight: value[0] }))}
                max={1}
                min={0.1}
                step={0.1}
                className="mt-2"
              />
            </div>

            <div>
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="bg-zinc-950 border-zinc-800"
                placeholder="Enter relation description"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateRelation}
              disabled={isLoading || conflicts.length > 0}
              className="bg-[#00d4aa] text-black hover:bg-[#00d4aa]/90"
            >
              {isLoading ? 'Updating...' : 'Update Relation'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="bg-zinc-900 border-zinc-800 text-white">
          <DialogHeader>
            <DialogTitle>Delete Relation</DialogTitle>
            <DialogDescription className="text-zinc-400">
              Are you sure you want to delete this relation? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-zinc-700 text-zinc-300 hover:bg-zinc-800"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteRelation}
              disabled={isLoading}
            >
              {isLoading ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RelationshipPanel;
