'use client';

import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Node, Edge } from 'reactflow';

import { setGraphMemorySuccess } from '@/store/graphMemorySlice';

import GraphVisualization from './GraphVisualization';

// 测试数据
const testNodes = [
  {
    id: '1',
    type: 'graphNode',
    position: { x: 100, y: 100 },
    data: {
      id: '1',
      label: '<PERSON>',
      type: 'person',
      description: 'Software Engineer at Tech Corp',
      properties: {
        age: 30,
        location: 'San Francisco',
        skills: 'React, TypeScript'
      }
    }
  },
  {
    id: '2',
    type: 'graphNode',
    position: { x: 300, y: 100 },
    data: {
      id: '2',
      label: 'Tech Corp',
      type: 'organization',
      description: 'Technology company focused on AI',
      properties: {
        founded: 2020,
        employees: 500,
        industry: 'Technology'
      }
    }
  },
  {
    id: '3',
    type: 'graphNode',
    position: { x: 200, y: 300 },
    data: {
      id: '3',
      label: 'React Development',
      type: 'skill',
      description: 'Frontend development with React',
      properties: {
        level: 'Expert',
        years: 5
      }
    }
  },
  {
    id: '4',
    type: 'graphNode',
    position: { x: 500, y: 200 },
    data: {
      id: '4',
      label: 'San Francisco',
      type: 'location',
      description: 'City in California, USA',
      properties: {
        country: 'USA',
        state: 'California',
        population: 875000
      }
    }
  }
];

const testEdges = [
  {
    id: 'e1-2',
    source: '1',
    target: '2',
    type: 'graphEdge',
    data: {
      id: 'e1-2',
      relation_type: 'works_at',
      weight: 0.9,
      description: 'Employment relationship'
    }
  },
  {
    id: 'e1-3',
    source: '1',
    target: '3',
    type: 'graphEdge',
    data: {
      id: 'e1-3',
      relation_type: 'knows',
      weight: 0.8,
      label: 'Has skill'
    }
  },
  {
    id: 'e1-4',
    source: '1',
    target: '4',
    type: 'graphEdge',
    data: {
      id: 'e1-4',
      relation_type: 'lives_in',
      weight: 0.7,
      description: 'Residence location'
    }
  },
  {
    id: 'e2-4',
    source: '2',
    target: '4',
    type: 'graphEdge',
    data: {
      id: 'e2-4',
      relation_type: 'located_in',
      weight: 0.6,
      description: 'Company headquarters'
    }
  }
];

const GraphVisualizationTest: React.FC = () => {
  const dispatch = useDispatch();

  // 初始化测试数据
  useEffect(() => {
    // 转换测试数据为正确的API格式
    const mockResponse = {
      entities: testNodes.map(node => ({
        id: node.id,
        name: node.data.label,
        type: node.data.type,
        description: node.data.description,
        properties: node.data.properties || {},
        created_at: (node.data as any).created_at
      })),
      relations: testEdges.map(edge => ({
        id: edge.id,
        source_entity_id: edge.source,
        target_entity_id: edge.target,
        relation_type: edge.data?.relation_type || 'related_to',
        description: edge.data?.description,
        weight: edge.data?.weight || 1.0,
        properties: (edge.data as any)?.properties || {},
        created_at: (edge.data as any)?.created_at
      })),
      metadata: {
        total_entities: testNodes.length,
        total_relations: testEdges.length,
        graph_density: 0.5,
        active_entities: testNodes.length
      }
    };

    dispatch(setGraphMemorySuccess(mockResponse));
  }, [dispatch]);

  const handleNodeClick = (node: Node) => {
    console.log('Node clicked:', node);
  };

  const handleEdgeClick = (edge: Edge) => {
    console.log('Edge clicked:', edge);
  };

  const handleSelectionChange = (selectedNodes: Node[], selectedEdges: Edge[]) => {
    console.log('Selection changed:', { selectedNodes, selectedEdges });
  };

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-white mb-2">Graph Visualization Test</h1>
        <p className="text-zinc-400">
          测试Graph Memory可视化组件的基本功能，包括节点渲染、边渲染、交互功能等。
        </p>
      </div>

      <div className="bg-zinc-900 rounded-lg border border-zinc-800 p-4">
        <h2 className="text-lg font-semibold text-white mb-4">测试功能</h2>
        <ul className="text-sm text-zinc-400 space-y-1">
          <li>• 节点渲染：不同类型的实体节点（人员、组织、技能、位置）</li>
          <li>• 边渲染：不同类型的关系边（工作、技能、居住、位置）</li>
          <li>• 交互功能：点击选择、拖拽、缩放、平移</li>
          <li>• 布局切换：力导向、层次、圆形、网格布局</li>
          <li>• 控制面板：缩放控制、小地图、全屏模式</li>
          <li>• 统计信息：节点数量、边数量、选择状态</li>
        </ul>
      </div>

      <GraphVisualization
        height="600px"
        onNodeClick={handleNodeClick}
        onEdgeClick={handleEdgeClick}
        onSelectionChange={handleSelectionChange}
        className="border-2 border-zinc-700"
      />

      <div className="bg-zinc-900 rounded-lg border border-zinc-800 p-4">
        <h3 className="text-lg font-semibold text-white mb-2">使用说明</h3>
        <div className="text-sm text-zinc-400 space-y-2">
          <p><strong>基本操作：</strong></p>
          <ul className="ml-4 space-y-1">
            <li>• 点击节点或边进行选择</li>
            <li>• 拖拽节点改变位置</li>
            <li>• 鼠标滚轮缩放画布</li>
            <li>• 拖拽空白区域平移画布</li>
          </ul>
          <p><strong>工具栏功能：</strong></p>
          <ul className="ml-4 space-y-1">
            <li>• 布局切换：力导向、层次、圆形、网格</li>
            <li>• 视图控制：放大、缩小、适应视图、重置</li>
            <li>• 全屏模式：最大化可视化区域</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default GraphVisualizationTest;
