# Mem0 UI

一个现代化的 Mem0 记忆管理用户界面，基于 Next.js 15 构建。

## 🚀 特性

- **完整的记忆管理**: 创建、搜索、更新、删除记忆
- **统计面板**: 实时数据展示和分析
- **活动时间线**: 基于记忆历史的活动追踪
- **图记忆可视化**: 高级图记忆系统，支持大规模数据
- **响应式设计**: 完全支持桌面端、平板端、移动端
- **性能优化**: 自研性能管理系统，支持1000+节点流畅渲染
- **黑色主题**: 统一的 Mem0 品牌色 (#00d4aa) 和深色主题

## 🛠️ 技术栈

- **框架**: Next.js 15.2.4
- **UI库**: React 19, Tailwind CSS
- **状态管理**: Redux Toolkit
- **图可视化**: React Flow 11.10.1
- **类型安全**: TypeScript
- **包管理**: pnpm
- **容器化**: Docker & Docker Compose

## 📦 快速开始

### 使用 Docker (推荐)

1. **生产环境启动**:
```bash
./start.sh
```

2. **开发环境启动**:
```bash
./start.sh dev
```

3. **停止服务**:
```bash
./stop.sh
```

4. **健康检查**:
```bash
./health-check.sh
```

### 本地开发

1. **安装依赖**:
```bash
pnpm install
```

2. **启动开发服务器**:
```bash
pnpm dev
```

3. **构建生产版本**:
```bash
pnpm build
pnpm start
```

## 🚀 快速开始

### 一键启动

使用统一管理脚本 `start.sh`：

```bash
# 🔧 配置环境
./start.sh config dev-real    # 开发环境 + 真实API
./start.sh config dev-mock    # 开发环境 + 模拟API
./start.sh config prod        # 生产环境 + 真实API

# 🚀 启动服务
./start.sh dev               # 开发服务器 (推荐)
./start.sh build             # 构建生产版本
./start.sh start             # 启动生产服务器

# 🐳 Docker部署
./start.sh docker prod       # 生产环境Docker部署
./start.sh docker dev        # 开发环境Docker部署
./start.sh stop              # 停止Docker容器

# 🔍 其他功能
./start.sh health            # 健康检查
./start.sh config status     # 查看当前配置
./start.sh help              # 显示帮助
```

### 环境配置说明

- `NEXT_PUBLIC_MEM0_API_URL` - Mem0 Server API URL
- `NEXT_PUBLIC_USER_ID` - 默认用户 ID
- `NEXT_PUBLIC_DISABLE_MSW` - 是否禁用模拟API (true/false)
- `NODE_ENV` - 环境模式 (development/production)

## 🐳 Docker 部署

### 生产环境部署

```bash
# 构建镜像
docker compose build

# 启动服务
docker compose up -d

# 查看日志
docker compose logs -f

# 停止服务
docker compose down
```

### 开发环境部署

```bash
# 启动开发环境
docker compose -f docker-compose.dev.yml up -d

# 查看日志
docker compose -f docker-compose.dev.yml logs -f
```

## 📊 性能特性

- **应用启动时间**: ~2.2秒
- **支持大数据量**: 1000+ 记忆记录流畅处理
- **内存使用**: 稳定在 50-70MB
- **响应时间**: API 调用 <500ms
- **LOD 渲染**: 4级细节层次渲染系统

## 🧪 测试

```bash
# 运行单元测试
pnpm test

# 运行测试覆盖率
pnpm test:coverage

# 运行性能测试
pnpm test:performance
```

## 📁 项目结构

```
mem0_ui/
├── app/                    # Next.js 应用目录
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 首页
│   └── ...                # 其他页面和路由
├── components/            # React 组件
│   ├── mem0/             # Mem0 专用组件
│   ├── ui/               # 通用 UI 组件
│   └── ...               # 其他组件
├── lib/                   # 工具库
│   ├── mem0-client/      # Mem0 API 客户端
│   └── utils.ts          # 工具函数
├── hooks/                 # 自定义 React Hooks
├── store/                 # Redux 状态管理
├── styles/                # 样式文件
├── types/                 # TypeScript 类型定义
├── public/                # 静态资源
└── docs/                  # 项目文档
```

## 🔗 API 集成

本项目与 Mem0 API 服务器集成，支持以下功能：

- **记忆管理**: 完整的 CRUD 操作
- **搜索功能**: 语义搜索和内容搜索
- **统计数据**: 系统统计和性能指标
- **图记忆**: 17个专用 Graph Memory API 方法

## 🎨 界面特性

- **Mem0 品牌色**: 统一的 #00d4aa 品牌色应用
- **深色主题**: 基于 zinc 色系的深色主题
- **响应式设计**: 完全支持移动端和触摸设备
- **组件复用**: 90%+ 组件复用率
- **无障碍支持**: 符合 WCAG 标准

## 🚀 部署选项

### 1. Docker Compose (推荐)
- 生产就绪的容器化部署
- 自动健康检查和重启
- 网络隔离和安全配置

### 2. 本地部署
- 适合开发和测试环境
- 支持热重载和实时调试

### 3. 云平台部署
- 支持 Vercel、Netlify 等平台
- 自动 CI/CD 集成

## 📈 监控和日志

- **健康检查**: 内置健康检查端点
- **性能监控**: GraphPerformanceManager 性能管理
- **错误处理**: 完善的错误边界和恢复机制
- **日志记录**: 结构化日志输出

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

- **文档**: 查看 `docs/` 目录中的详细文档
- **问题报告**: 在 GitHub Issues 中报告问题
- **功能请求**: 在 GitHub Issues 中提交功能请求

## 🔄 版本历史

- **v1.0.0**: 初始发布版本，包含完整的 Mem0 UI 功能
- 查看 [CHANGELOG.md](CHANGELOG.md) 了解详细更新历史

---

**开发团队**: Mem0 AI  
**最后更新**: 2025-07-29  
**版本**: 1.0.0
