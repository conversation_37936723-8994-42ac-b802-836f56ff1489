services:
  mem0-ui-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: mem0/mem0-ui:dev
    container_name: mem0-ui-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-http://localhost:8000}
      - NEXT_PUBLIC_USER_ID=${NEXT_PUBLIC_USER_ID:-default}
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    restart: unless-stopped
    networks:
      - mem0-network

networks:
  mem0-network:
    driver: bridge
