#!/bin/bash

# Mem0 UI 统一管理脚本
# 使用方法: ./start.sh [命令] [选项]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

ENV_FILE=".env.local"

print_header() {
    clear
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}     Mem0 UI 管理工具${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

show_help() {
    echo -e "${YELLOW}用法:${NC} $0 [命令] [选项]"
    echo ""
    echo -e "${YELLOW}配置命令:${NC}"
    echo "  config              - 交互式配置"
    echo "  config dev-mock     - 开发环境 + 模拟API"
    echo "  config dev-real     - 开发环境 + 真实API"
    echo "  config prod         - 生产环境 + 真实API"
    echo "  config status       - 查看当前配置"
    echo "  config clean        - 清理配置"
    echo ""
    echo -e "${YELLOW}运行命令:${NC}"
    echo "  dev                 - 启动开发服务器 (pnpm dev)"
    echo "  build               - 构建生产版本 (pnpm build)"
    echo "  start               - 启动生产服务器 (pnpm start)"
    echo "  docker [dev|prod]   - Docker容器化部署"
    echo "  stop                - 停止Docker容器"
    echo "  health              - 健康检查"
    echo ""
    echo -e "${YELLOW}其他命令:${NC}"
    echo "  help                - 显示帮助"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo "  $0 config dev-real  # 配置开发环境"
    echo "  $0 dev              # 启动开发服务器"
    echo "  $0 docker prod      # Docker生产部署"
}

# 配置相关函数
show_current_config() {
    echo -e "${YELLOW}当前配置:${NC}"
    if [ -f "$ENV_FILE" ]; then
        if grep -q "NEXT_PUBLIC_DISABLE_MSW=true" "$ENV_FILE"; then
            echo -e "  🔗 真实 API 模式"
            echo -e "  📍 $(grep NEXT_PUBLIC_MEM0_API_URL "$ENV_FILE" | cut -d'=' -f2)"
        else
            echo -e "  🎭 模拟 API 模式"
            echo -e "  📍 开发/演示环境"
        fi
        
        if grep -q "NODE_ENV=production" "$ENV_FILE"; then
            echo -e "  🚀 生产环境"
        else
            echo -e "  🛠️  开发环境"
        fi
    else
        echo -e "  ⚠️  未配置 (将使用默认设置)"
    fi
    echo ""
}

create_config() {
    local mode=$1
    local env=$2
    
    cat > "$ENV_FILE" << EOF
# Mem0 UI 配置 - $mode ($env)
# 生成时间: $(date)

# API 配置
NEXT_PUBLIC_MEM0_API_URL=http://localhost:8000
NEXT_PUBLIC_USER_ID=default

# MSW 配置
NEXT_PUBLIC_DISABLE_MSW=$([[ $mode == "真实API" ]] && echo "true" || echo "false")

# 应用配置
NODE_ENV=$([[ $env == "生产" ]] && echo "production" || echo "development")
EOF
}

config_interactive() {
    print_header
    show_current_config
    
    echo -e "${YELLOW}请选择配置:${NC}"
    echo ""
    echo "  ${GREEN}开发环境:${NC}"
    echo "    1) 开发 + 模拟API (离线开发)"
    echo "    2) 开发 + 真实API (连接Mem0服务器)"
    echo ""
    echo "  ${GREEN}生产环境:${NC}"
    echo "    3) 生产 + 真实API (推荐)"
    echo "    4) 生产 + 模拟API (演示用)"
    echo ""
    echo "    0) 返回"
    echo ""
    
    read -p "请输入选择 (0-4): " choice
    
    case $choice in
        1) create_config "模拟API" "开发"; echo -e "${GREEN}✅ 已配置: 开发环境 + 模拟API${NC}" ;;
        2) create_config "真实API" "开发"; echo -e "${GREEN}✅ 已配置: 开发环境 + 真实API${NC}" ;;
        3) create_config "真实API" "生产"; echo -e "${GREEN}✅ 已配置: 生产环境 + 真实API${NC}" ;;
        4) create_config "模拟API" "生产"; echo -e "${GREEN}✅ 已配置: 生产环境 + 模拟API${NC}" ;;
        0) return ;;
        *) echo -e "${RED}❌ 无效选择${NC}"; return 1 ;;
    esac
}

# 运行相关函数
run_dev() {
    echo -e "${GREEN}🚀 启动开发服务器...${NC}"
    if [ ! -f "$ENV_FILE" ]; then
        echo -e "${YELLOW}⚠️  未找到配置文件，使用默认配置${NC}"
        create_config "真实API" "开发"
    fi
    
    if ! command -v pnpm &> /dev/null; then
        echo -e "${RED}❌ pnpm 未安装，请先安装 pnpm${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}📦 安装依赖...${NC}"
    pnpm install
    
    echo -e "${BLUE}🔥 启动开发服务器...${NC}"
    pnpm dev
}

run_build() {
    echo -e "${GREEN}🔨 构建生产版本...${NC}"
    if [ ! -f "$ENV_FILE" ]; then
        echo -e "${YELLOW}⚠️  未找到配置文件，使用生产配置${NC}"
        create_config "真实API" "生产"
    fi
    
    if ! command -v pnpm &> /dev/null; then
        echo -e "${RED}❌ pnpm 未安装，请先安装 pnpm${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}📦 安装依赖...${NC}"
    pnpm install
    
    echo -e "${BLUE}🔨 构建应用...${NC}"
    pnpm build
    
    echo -e "${GREEN}✅ 构建完成！${NC}"
    echo -e "${YELLOW}💡 使用 '$0 start' 启动生产服务器${NC}"
}

run_start() {
    echo -e "${GREEN}🚀 启动生产服务器...${NC}"
    if [ ! -d ".next" ]; then
        echo -e "${YELLOW}⚠️  未找到构建文件，先进行构建...${NC}"
        run_build
    fi
    
    echo -e "${BLUE}🔥 启动生产服务器...${NC}"
    pnpm start
}

# Docker相关函数
run_docker() {
    local mode=${1:-prod}
    
    echo -e "${GREEN}🐳 Docker部署 (模式: $mode)...${NC}"
    
    # 检查Docker是否运行
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker未运行，请先启动Docker服务${NC}"
        exit 1
    fi
    
    # 停止现有容器
    echo -e "${BLUE}🔄 清理现有容器...${NC}"
    if [ "$mode" = "dev" ]; then
        docker compose -f docker-compose.dev.yml down --remove-orphans 2>/dev/null || true
    else
        docker compose down --remove-orphans 2>/dev/null || true
    fi
    
    # 构建并启动
    if [ "$mode" = "dev" ]; then
        echo -e "${BLUE}🔨 构建开发环境...${NC}"
        docker compose -f docker-compose.dev.yml build
        echo -e "${BLUE}🚀 启动开发环境...${NC}"
        docker compose -f docker-compose.dev.yml up -d
    else
        echo -e "${BLUE}🔨 构建生产环境...${NC}"
        docker compose build
        echo -e "${BLUE}🚀 启动生产环境...${NC}"
        docker compose up -d
    fi

    PORT=3000
    
    echo -e "${GREEN}✅ Docker部署完成！${NC}"
    echo -e "${YELLOW}🌐 访问地址: http://localhost:$PORT${NC}"
    echo -e "${YELLOW}📝 查看日志: docker compose logs -f${NC}"
}

stop_docker() {
    echo -e "${YELLOW}🛑 停止Docker容器...${NC}"
    
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker未运行${NC}"
        exit 1
    fi
    
    docker compose down --remove-orphans 2>/dev/null || true
    docker compose -f docker-compose.dev.yml down --remove-orphans 2>/dev/null || true
    
    echo -e "${GREEN}✅ 容器已停止${NC}"
}

health_check() {
    echo -e "${BLUE}🔍 健康检查...${NC}"
    
    # 检查配置
    show_current_config
    
    # 检查Docker容器
    if docker ps | grep -q "mem0-ui"; then
        echo -e "${GREEN}✅ Docker容器正在运行${NC}"
        docker ps | grep mem0-ui
    else
        echo -e "${YELLOW}⚠️  Docker容器未运行${NC}"
    fi
    
    # 检查端口
    if curl -f -s "http://localhost:3000" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 端口 3000 响应正常${NC}"
    else
        echo -e "${YELLOW}⚠️  端口 3000 无响应${NC}"
    fi
    
    # 检查API连接
    API_URL=$(grep NEXT_PUBLIC_MEM0_API_URL "$ENV_FILE" 2>/dev/null | cut -d'=' -f2 || echo "http://localhost:8000")
    if curl -f -s "$API_URL/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API连接正常 ($API_URL)${NC}"
        # 显示API状态详情
        API_STATUS=$(curl -s "$API_URL/health" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        echo -e "    状态: $API_STATUS"
    else
        echo -e "${YELLOW}⚠️  API连接失败 ($API_URL)${NC}"
        echo -e "    请确保Mem0服务正在运行"
    fi
}

# 主函数
main() {
    # 检查是否在正确目录
    if [ ! -f "package.json" ]; then
        echo -e "${RED}❌ 请在 mem0_ui 目录下运行此脚本${NC}"
        exit 1
    fi
    
    case "${1:-help}" in
        "config")
            case "${2:-}" in
                "dev-mock") create_config "模拟API" "开发"; echo "✅ 开发环境 + 模拟API 配置完成" ;;
                "dev-real") create_config "真实API" "开发"; echo "✅ 开发环境 + 真实API 配置完成" ;;
                "prod") create_config "真实API" "生产"; echo "✅ 生产环境配置完成" ;;
                "status") show_current_config ;;
                "clean") [ -f "$ENV_FILE" ] && rm "$ENV_FILE"; echo "✅ 配置已清理" ;;
                *) config_interactive ;;
            esac
            ;;
        "dev") run_dev ;;
        "build") run_build ;;
        "start") run_start ;;
        "docker") run_docker "${2:-prod}" ;;
        "stop") stop_docker ;;
        "health") health_check ;;
        "help"|"-h"|"--help") show_help ;;
        *) echo -e "${RED}未知命令: $1${NC}"; show_help; exit 1 ;;
    esac
}

main "$@"
