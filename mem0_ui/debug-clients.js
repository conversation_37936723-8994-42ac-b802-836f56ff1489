#!/usr/bin/env node

/**
 * Debug script to test both API clients
 */

const axios = require('axios');

// Test SimplifiedMem0APIClient configuration
console.log('🔍 Testing API Client Configurations...\n');

// Environment variables
const NEXT_PUBLIC_MEM0_API_URL = process.env.NEXT_PUBLIC_MEM0_API_URL || 'http://localhost:8000';
const NEXT_PUBLIC_API_URL = process.env.NEXT_PUBLIC_API_URL;

console.log('Environment variables:');
console.log('- NEXT_PUBLIC_MEM0_API_URL:', NEXT_PUBLIC_MEM0_API_URL);
console.log('- NEXT_PUBLIC_API_URL:', NEXT_PUBLIC_API_URL);

// Test SimplifiedMem0APIClient default URL
const simplifiedClientUrl = NEXT_PUBLIC_MEM0_API_URL || NEXT_PUBLIC_API_URL || 'http://localhost:8765/api/v1';
console.log('\nSimplifiedMem0APIClient would use URL:', simplifiedClientUrl);

// Test RealMem0APIClient default URL  
const realClientUrl = NEXT_PUBLIC_MEM0_API_URL || 'http://localhost:8000';
console.log('RealMem0APIClient would use URL:', realClientUrl);

async function testClientUrls() {
  console.log('\n🧪 Testing client URL accessibility...\n');
  
  // Test SimplifiedMem0APIClient URL
  console.log('1. Testing SimplifiedMem0APIClient URL...');
  try {
    const response = await axios.get(`${simplifiedClientUrl}/v1/memories/?user_id=default&limit=1`, {
      timeout: 5000
    });
    console.log('✅ SimplifiedMem0APIClient URL accessible:', response.status);
  } catch (error) {
    console.log('❌ SimplifiedMem0APIClient URL failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('   - Connection refused - service not running on this URL');
    }
  }
  
  // Test RealMem0APIClient URL
  console.log('\n2. Testing RealMem0APIClient URL...');
  try {
    const response = await axios.get(`${realClientUrl}/v1/memories/?user_id=default&limit=1`, {
      timeout: 5000
    });
    console.log('✅ RealMem0APIClient URL accessible:', response.status);
  } catch (error) {
    console.log('❌ RealMem0APIClient URL failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('   - Connection refused - service not running on this URL');
    }
  }
  
  // Test which ports are actually listening
  console.log('\n3. Testing common ports...');
  const portsToTest = [8000, 8765, 3000];
  
  for (const port of portsToTest) {
    try {
      const response = await axios.get(`http://localhost:${port}/v1/memories/?user_id=default&limit=1`, {
        timeout: 3000
      });
      console.log(`✅ Port ${port} is accessible and has mem0 API`);
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ Port ${port} - Connection refused`);
      } else if (error.response?.status === 404) {
        console.log(`⚠️  Port ${port} - Service running but no mem0 API`);
      } else {
        console.log(`❌ Port ${port} - ${error.message}`);
      }
    }
  }
}

testClientUrls().then(() => {
  console.log('\n🏁 Client URL test completed');
}).catch(error => {
  console.error('💥 Client URL test failed:', error);
});
