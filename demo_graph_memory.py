#!/usr/bin/env python3
"""
图记忆功能实际使用示例
Graph Memory Real Usage Example

演示在当前环境中如何实际使用图记忆功能
"""

import sys
import os
sys.path.insert(0, '/opt/mem0ai')

from mem0 import Memory
import time

def demo_graph_memory():
    """演示图记忆功能的实际使用"""
    print("=" * 60)
    print("Mem0 图记忆功能实际使用演示")
    print("=" * 60)
    
    # 配置Memory实例启用图记忆
    config = {
        "llm": {
            "provider": "openai",
            "config": {
                "model": "gpt-4o-mini",
                "temperature": 0.2,
            }
        },
        "graph_store": {
            "provider": "neo4j",
            "config": {
                "url": "bolt://localhost:7687",
                "username": "neo4j",
                "password": "mem0graph"
            }
        }
    }
    
    try:
        memory = Memory.from_config(config_dict=config)
        print("✓ Memory实例创建成功，图记忆已启用")
        
        # 测试用户ID
        demo_user = "graph_demo_user"
        
        print(f"\n正在为用户 '{demo_user}' 演示图记忆功能...")
        
        # 1. 清理之前的演示数据
        try:
            memory.delete_all(user_id=demo_user)
            print("✓ 清理之前的演示数据")
        except:
            pass
        
        # 2. 添加一些记忆
        print("\n📝 添加记忆:")
        memories_to_add = [
            "My name is Alice and I work as a software engineer",
            "I love pizza and coffee", 
            "My best friend is Bob, and Bob also likes pizza",
            "Bob works at Google as a data scientist",
            "I live in San Francisco",
            "Bob lives in New York"
        ]
        
        for i, memory_text in enumerate(memories_to_add, 1):
            try:
                result = memory.add(memory_text, user_id=demo_user)
                print(f"   {i}. '{memory_text}' ✓")
                time.sleep(0.5)  # 避免过快请求
            except Exception as e:
                print(f"   {i}. '{memory_text}' ✗ ({e})")
        
        # 3. 搜索记忆 - 测试图增强检索
        print(f"\n🔍 搜索测试:")
        search_queries = [
            "What do I like to eat?",
            "Tell me about Bob", 
            "Where do my friends live?",
            "Who works in tech?"
        ]
        
        for query in search_queries:
            try:
                results = memory.search(query, user_id=demo_user)
                print(f"\n   查询: '{query}'")
                print(f"   结果: 找到 {len(results)} 条相关记忆")
                
                # 显示前3个结果
                for j, result in enumerate(results[:3], 1):
                    if isinstance(result, dict):
                        if 'memory' in result:
                            print(f"      {j}. {result['memory']}")
                        elif 'content' in result:
                            print(f"      {j}. {result['content']}")
                        else:
                            print(f"      {j}. {str(result)[:100]}...")
                    else:
                        print(f"      {j}. {str(result)[:100]}...")
                        
            except Exception as e:
                print(f"   查询: '{query}' ✗ ({e})")
        
        # 4. 获取所有记忆 - 查看图关系
        print(f"\n📊 图关系分析:")
        try:
            all_memories = memory.get_all(user_id=demo_user)
            
            if isinstance(all_memories, dict) and 'memories' in all_memories:
                memories = all_memories['memories']
                entities = all_memories.get('entities', [])
                
                print(f"   总记忆数量: {len(memories)}")
                print(f"   图关系数量: {len(entities)}")
                
                if entities:
                    print("   发现的关系:")
                    for entity in entities[:5]:  # 显示前5个关系
                        if isinstance(entity, dict):
                            source = entity.get('source', 'Unknown')
                            rel = entity.get('relationship', 'Unknown')
                            target = entity.get('target', 'Unknown')
                            print(f"      {source} --{rel}--> {target}")
                        else:
                            print(f"      {entity}")
                            
            else:
                print(f"   获取到 {len(all_memories)} 条记忆")
                
        except Exception as e:
            print(f"   获取所有记忆失败: {e}")
        
        # 5. 清理演示数据
        print(f"\n🧹 清理演示数据...")
        try:
            memory.delete_all(user_id=demo_user)
            print("✓ 演示数据清理完成")
        except Exception as e:
            print(f"✗ 清理失败: {e}")
        
        print(f"\n" + "=" * 60)
        print("🎉 图记忆功能演示完成！")
        print("✅ 图记忆功能在当前环境中工作正常")
        print("   - 可以添加和存储记忆")
        print("   - 支持智能搜索和检索")  
        print("   - 能够构建和查询图关系")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 设置环境变量（如果未设置）
    if not os.getenv('OPENAI_API_KEY'):
        os.environ['OPENAI_API_KEY'] = 'sk-kP3jyHHUmlQ0HI5D923e0fA8CfBf48C5A60aC5D71061Ac46'
        os.environ['OPENAI_BASE_URL'] = 'https://aihubmix.com/v1'
    
    success = demo_graph_memory()
    
    if success:
        print("\n✅ 结论: Mem0图记忆功能完全正常，可以投入使用！")
        sys.exit(0)
    else:
        print("\n❌ 结论: 图记忆功能存在问题，需要进一步检查。")
        sys.exit(1)