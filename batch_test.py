#!/usr/bin/env python3
"""
批量添加测试记忆 - 直接方式
"""
import requests
import json
import time

API_BASE = "http://localhost:8000"

# 测试记忆数据
memories = [
    # 个人信息类
    {"user_id": "alice", "messages": [{"role": "user", "content": "我叫Alice，是一名前端开发工程师，住在上海"}], "metadata": {"category": "个人信息"}},
    {"user_id": "alice", "messages": [{"role": "user", "content": "我擅长React、Vue.js和TypeScript，有3年开发经验"}], "metadata": {"category": "技能"}},
    {"user_id": "alice", "messages": [{"role": "user", "content": "我喜欢看电影、听音乐和旅行，最近想学摄影"}], "metadata": {"category": "兴趣爱好"}},
    
    # 工作项目类
    {"user_id": "bob", "agent_id": "work_assistant", "messages": [{"role": "user", "content": "正在开发一个电商前端项目，使用Next.js和Tailwind CSS"}], "metadata": {"category": "工作项目"}},
    {"user_id": "bob", "agent_id": "work_assistant", "messages": [{"role": "user", "content": "团队有5个人：2个前端、2个后端、1个UI设计师"}], "metadata": {"category": "团队信息"}},
    {"user_id": "bob", "agent_id": "work_assistant", "messages": [{"role": "user", "content": "项目计划下个月上线，目前进度60%"}], "metadata": {"category": "项目进度"}},
    
    # 学习记录类  
    {"user_id": "charlie", "agent_id": "study_assistant", "messages": [{"role": "user", "content": "学习了Python装饰器：@property可以将方法转为属性访问"}], "metadata": {"category": "学习笔记"}},
    {"user_id": "charlie", "agent_id": "study_assistant", "messages": [{"role": "user", "content": "Docker容器化：镜像是模板，容器是运行实例"}], "metadata": {"category": "技术学习"}},
    {"user_id": "charlie", "agent_id": "study_assistant", "messages": [{"role": "user", "content": "Git分支管理：master主分支，develop开发分支，feature功能分支"}], "metadata": {"category": "开发工具"}},
    
    # 生活记录类
    {"user_id": "diana", "messages": [{"role": "user", "content": "今天去了新开的咖啡厅，环境很棒，咖啡味道不错"}], "metadata": {"category": "生活记录"}},
    {"user_id": "diana", "messages": [{"role": "user", "content": "看了《沙丘2》，视觉效果震撼，故事节奏紧凑"}], "metadata": {"category": "娱乐"}},
    {"user_id": "diana", "messages": [{"role": "user", "content": "周末计划：购物、健身、和朋友聚餐"}], "metadata": {"category": "计划安排"}},
    
    # 关系网络类 - 启用图形记忆
    {"user_id": "eve", "enable_graph": True, "messages": [{"role": "user", "content": "我的同事Tom是后端工程师，擅长Java和Spring框架"}], "metadata": {"category": "人际关系"}},
    {"user_id": "eve", "enable_graph": True, "messages": [{"role": "user", "content": "朋友Lisa在腾讯做产品经理，负责微信小程序业务"}], "metadata": {"category": "朋友圈"}},
    {"user_id": "eve", "enable_graph": True, "messages": [{"role": "user", "content": "老板Zhang总是技术背景，创业10年，公司规模100人"}], "metadata": {"category": "工作关系"}},
    
    # 复杂对话类
    {"user_id": "frank", "agent_id": "consultant", "messages": [
        {"role": "user", "content": "我想学习人工智能，应该从哪里开始？"},
        {"role": "assistant", "content": "建议先学Python基础，然后学习机器学习的数学基础"},
        {"role": "user", "content": "数学基础包括什么？"},
        {"role": "assistant", "content": "线性代数、概率统计、微积分是核心，推荐先从线性代数开始"}
    ], "metadata": {"category": "学习咨询"}},
    
    # 技术文档类
    {"user_id": "grace", "messages": [{"role": "user", "content": "API设计最佳实践：使用RESTful风格，状态码要语义化，返回格式统一"}], "metadata": {"category": "技术文档"}},
    {"user_id": "grace", "messages": [{"role": "user", "content": "数据库优化技巧：建立合适索引，避免N+1查询，使用连接池"}], "metadata": {"category": "性能优化"}},
    
    # 项目管理类
    {"user_id": "henry", "agent_id": "pm_assistant", "messages": [{"role": "user", "content": "Sprint计划：本周完成用户登录模块，下周开始支付功能"}], "metadata": {"category": "项目管理"}},
    {"user_id": "henry", "agent_id": "pm_assistant", "messages": [{"role": "user", "content": "Bug统计：高优先级2个，中优先级5个，低优先级8个"}], "metadata": {"category": "质量管理"}},
    
    # 市场分析类
    {"user_id": "ivy", "messages": [{"role": "user", "content": "竞品分析：A产品用户体验好但功能少，B产品功能强但界面复杂"}], "metadata": {"category": "市场分析"}},
]

def add_memory(data):
    """添加单条记忆"""
    try:
        response = requests.post(f"{API_BASE}/v1/memories/", json=data, timeout=30)
        if response.status_code == 200:
            return True, "成功"
        else:
            return False, f"状态码: {response.status_code}, 响应: {response.text[:100]}"
    except Exception as e:
        return False, f"异常: {str(e)[:100]}"

print("🧠 开始添加测试记忆数据...")
print(f"📊 计划添加 {len(memories)} 条记忆")
print("-" * 50)

success_count = 0
for i, memory in enumerate(memories, 1):
    content_preview = memory["messages"][0]["content"][:30] + "..."
    print(f"[{i:2d}/{len(memories)}] {content_preview:<35}", end=" ")
    
    success, message = add_memory(memory)
    if success:
        print("✅")
        success_count += 1
    else:
        print(f"❌ {message}")
    
    time.sleep(0.5)  # 避免请求过快

print("-" * 50)
print(f"🎉 完成！成功添加 {success_count}/{len(memories)} 条记忆")
print(f"📈 成功率: {success_count/len(memories)*100:.1f}%")

# 统计信息
categories = {}
users = set()
agents = set()
for memory in memories:
    category = memory.get("metadata", {}).get("category", "未分类")
    categories[category] = categories.get(category, 0) + 1
    users.add(memory["user_id"])
    if memory.get("agent_id"):
        agents.add(memory["agent_id"])

print(f"\n📋 数据统计:")
print(f"  👥 用户数: {len(users)}")
print(f"  🤖 代理数: {len(agents)}")
print(f"  📁 分类数: {len(categories)}")
print(f"  🔗 图形记忆: {sum(1 for m in memories if m.get('enable_graph', False))} 条")

print(f"\n🏷️ 分类分布:")
for category, count in sorted(categories.items()):
    print(f"  • {category}: {count} 条")

print(f"\n🎯 测试数据已准备就绪！")
print(f"📱 可以在前端UI中查看这些记忆了")