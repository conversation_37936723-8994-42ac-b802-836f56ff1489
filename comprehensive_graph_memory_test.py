#!/usr/bin/env python3
"""
Comprehensive Graph Memory Test Suite
=====================================

This script provides comprehensive testing for Mem0's Graph Memory functionality,
including the recent KeyError fix and all core features.

Test Categories:
1. Basic Functionality Tests (add, search, get_all, delete_all)
2. Multi-user Isolation Tests
3. Agent-based Filtering Tests
4. Graph Relationship Tests
5. API Error Handling Tests
6. Performance and Load Tests
"""

import os
import sys
import requests
import json
import time
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('graph_memory_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GraphMemoryTester:
    """Comprehensive test suite for Graph Memory functionality"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
        self.start_time = None
        
    def log_test_result(self, test_name: str, status: str, details: str = "", execution_time: float = 0):
        """Log test result"""
        result = {
            "test_name": test_name,
            "status": status,
            "details": details,
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        logger.info(f"{status_emoji} {test_name}: {status} ({execution_time:.2f}s)")
        if details:
            logger.info(f"   Details: {details}")
    
    def make_request(self, method: str, endpoint: str, data: dict = None, params: dict = None) -> dict:
        """Make HTTP request to API"""
        url = f"{self.base_url}{endpoint}"
        try:
            if method.upper() == "GET":
                response = requests.get(url, params=params, timeout=30)
            elif method.upper() == "POST":
                response = requests.post(url, json=data, params=params, timeout=30)
            elif method.upper() == "DELETE":
                response = requests.delete(url, params=params, timeout=30)
            
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {e}")
            return {"error": str(e)}
    
    def test_api_connectivity(self):
        """Test basic API connectivity"""
        start_time = time.time()
        
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                self.log_test_result(
                    "API Connectivity", 
                    "PASS", 
                    "API is accessible and responding",
                    time.time() - start_time
                )
                return True
            else:
                self.log_test_result(
                    "API Connectivity", 
                    "FAIL", 
                    f"API returned status {response.status_code}",
                    time.time() - start_time
                )
                return False
        except Exception as e:
            self.log_test_result(
                "API Connectivity", 
                "FAIL", 
                f"Connection error: {str(e)}",
                time.time() - start_time
            )
            return False
    
    def test_keyerror_fix(self):
        """Test the KeyError fix - ensure graph/stats works without user_id"""
        start_time = time.time()
        
        try:
            # Test without user_id parameter
            response = self.make_request("GET", "/v1/graph/stats")
            
            if "error" not in response:
                expected_keys = ["total_entities", "total_relationships", "graph_density"]
                if all(key in response for key in expected_keys):
                    self.log_test_result(
                        "KeyError Fix (No user_id)", 
                        "PASS", 
                        "Graph stats API works without user_id parameter",
                        time.time() - start_time
                    )
                else:
                    self.log_test_result(
                        "KeyError Fix (No user_id)", 
                        "FAIL", 
                        f"Missing expected keys in response: {response}",
                        time.time() - start_time
                    )
            else:
                self.log_test_result(
                    "KeyError Fix (No user_id)", 
                    "FAIL", 
                    f"API returned error: {response['error']}",
                    time.time() - start_time
                )
                
            # Test with user_id parameter
            start_time2 = time.time()
            response = self.make_request("GET", "/v1/graph/stats", params={"user_id": "test_user"})
            
            if "error" not in response and "total_entities" in response:
                self.log_test_result(
                    "KeyError Fix (With user_id)", 
                    "PASS", 
                    "Graph stats API works with user_id parameter",
                    time.time() - start_time2
                )
            else:
                self.log_test_result(
                    "KeyError Fix (With user_id)", 
                    "FAIL", 
                    f"API failed with user_id: {response}",
                    time.time() - start_time2
                )
                
        except Exception as e:
            self.log_test_result(
                "KeyError Fix", 
                "FAIL", 
                f"Exception occurred: {str(e)}",
                time.time() - start_time
            )
    
    def test_add_memory(self, user_id: str, content: str, agent_id: str = None) -> dict:
        """Test adding a memory"""
        start_time = time.time()
        
        data = {
            "messages": [{"role": "user", "content": content}],
            "user_id": user_id
        }
        
        if agent_id:
            data["agent_id"] = agent_id
            
        response = self.make_request("POST", "/v1/memories/", data=data)
        
        test_name = f"Add Memory ({user_id}" + (f", {agent_id}" if agent_id else "") + ")"
        
        if "error" not in response and "results" in response:
            self.log_test_result(
                test_name, 
                "PASS", 
                f"Successfully added memory: {content[:50]}...",
                time.time() - start_time
            )
        else:
            self.log_test_result(
                test_name, 
                "FAIL", 
                f"Failed to add memory: {response}",
                time.time() - start_time
            )
            
        return response
    
    def test_search_memory(self, query: str, user_id: str, agent_id: str = None) -> dict:
        """Test searching memories"""
        start_time = time.time()
        
        params = {
            "query": query,
            "user_id": user_id
        }
        
        if agent_id:
            params["agent_id"] = agent_id
            
        response = self.make_request("GET", "/v1/memories/search/", params=params)
        
        test_name = f"Search Memory ({user_id}" + (f", {agent_id}" if agent_id else "") + ")"
        
        if "error" not in response and "results" in response:
            self.log_test_result(
                test_name, 
                "PASS", 
                f"Search returned {len(response['results'])} results for: {query}",
                time.time() - start_time
            )
        else:
            self.log_test_result(
                test_name, 
                "FAIL", 
                f"Search failed: {response}",
                time.time() - start_time
            )
            
        return response
    
    def test_get_all_memories(self, user_id: str, agent_id: str = None) -> dict:
        """Test getting all memories"""
        start_time = time.time()
        
        params = {"user_id": user_id}
        if agent_id:
            params["agent_id"] = agent_id
            
        response = self.make_request("GET", "/v1/memories/", params=params)
        
        test_name = f"Get All Memories ({user_id}" + (f", {agent_id}" if agent_id else "") + ")"
        
        if "error" not in response and "results" in response:
            self.log_test_result(
                test_name, 
                "PASS", 
                f"Retrieved {len(response['results'])} memories",
                time.time() - start_time
            )
        else:
            self.log_test_result(
                test_name, 
                "FAIL", 
                f"Get all failed: {response}",
                time.time() - start_time
            )
            
        return response
    
    def test_delete_all_memories(self, user_id: str, agent_id: str = None) -> dict:
        """Test deleting all memories"""
        start_time = time.time()
        
        params = {"user_id": user_id}
        if agent_id:
            params["agent_id"] = agent_id
            
        response = self.make_request("DELETE", "/v1/memories/", params=params)
        
        test_name = f"Delete All Memories ({user_id}" + (f", {agent_id}" if agent_id else "") + ")"
        
        if "error" not in response:
            self.log_test_result(
                test_name, 
                "PASS", 
                "Successfully deleted all memories",
                time.time() - start_time
            )
        else:
            self.log_test_result(
                test_name, 
                "FAIL", 
                f"Delete all failed: {response}",
                time.time() - start_time
            )
            
        return response
    
    def test_graph_entities(self, user_id: str = None):
        """Test graph entities endpoint"""
        start_time = time.time()
        
        params = {}
        if user_id:
            params["user_id"] = user_id
            
        response = self.make_request("GET", "/v1/graph/entities", params=params)
        
        test_name = f"Graph Entities" + (f" ({user_id})" if user_id else " (No user)")
        
        if "error" not in response and "entities" in response:
            self.log_test_result(
                test_name, 
                "PASS", 
                f"Retrieved {len(response['entities'])} entities",
                time.time() - start_time
            )
        else:
            self.log_test_result(
                test_name, 
                "FAIL", 
                f"Get entities failed: {response}",
                time.time() - start_time
            )
            
        return response
    
    def test_graph_relationships(self, user_id: str = None):
        """Test graph relationships endpoint"""
        start_time = time.time()
        
        params = {}
        if user_id:
            params["user_id"] = user_id
            
        response = self.make_request("GET", "/v1/graph/relationships", params=params)
        
        test_name = f"Graph Relationships" + (f" ({user_id})" if user_id else " (No user)")
        
        if "error" not in response and "relationships" in response:
            self.log_test_result(
                test_name, 
                "PASS", 
                f"Retrieved {len(response['relationships'])} relationships",
                time.time() - start_time
            )
        else:
            self.log_test_result(
                test_name, 
                "FAIL", 
                f"Get relationships failed: {response}",
                time.time() - start_time
            )
            
        return response
    
    def run_comprehensive_tests(self):
        """Run the complete test suite"""
        logger.info("🚀 Starting Comprehensive Graph Memory Test Suite")
        self.start_time = time.time()
        
        # Phase 1: Basic connectivity
        logger.info("\n📡 Phase 1: API Connectivity Tests")
        if not self.test_api_connectivity():
            logger.error("❌ API connectivity failed. Aborting tests.")
            return
        
        # Phase 2: KeyError fix verification
        logger.info("\n🔧 Phase 2: KeyError Fix Verification")
        self.test_keyerror_fix()
        
        # Phase 3: Core functionality tests
        logger.info("\n🧠 Phase 3: Core Memory Operations")
        
        # Clean up any existing test data
        self.test_delete_all_memories("test_user_alice")
        self.test_delete_all_memories("test_user_bob")
        
        # Add test memories
        self.test_add_memory("test_user_alice", "My name is Alice and I am a software engineer")
        self.test_add_memory("test_user_alice", "I live in Seattle and love hiking")
        self.test_add_memory("test_user_alice", "My favorite food is pizza")
        
        # Add memories for different user
        self.test_add_memory("test_user_bob", "My name is Bob and I work as a designer")
        self.test_add_memory("test_user_bob", "I live in Portland and enjoy cycling")
        
        # Search tests
        time.sleep(2)  # Allow time for processing
        self.test_search_memory("What is my name?", "test_user_alice")
        self.test_search_memory("Where do I live?", "test_user_alice")
        self.test_search_memory("What is my name?", "test_user_bob")
        
        # Get all tests
        self.test_get_all_memories("test_user_alice")
        self.test_get_all_memories("test_user_bob")
        
        # Phase 4: Multi-user isolation tests
        logger.info("\n👥 Phase 4: Multi-user Isolation Tests")
        
        # Verify user isolation
        alice_memories = self.test_get_all_memories("test_user_alice")
        bob_memories = self.test_get_all_memories("test_user_bob")
        
        # Check that Alice's memories don't appear in Bob's results
        if ("results" in alice_memories and "results" in bob_memories and
            len(alice_memories["results"]) > 0 and len(bob_memories["results"]) > 0):
            
            alice_contents = [m.get("memory", "") for m in alice_memories["results"]]
            bob_contents = [m.get("memory", "") for m in bob_memories["results"]]
            
            isolation_test = not any(content in bob_contents for content in alice_contents)
            
            self.log_test_result(
                "User Isolation Test",
                "PASS" if isolation_test else "FAIL",
                f"Alice has {len(alice_contents)} memories, Bob has {len(bob_contents)} memories, isolated: {isolation_test}"
            )
        
        # Phase 5: Agent-based filtering tests
        logger.info("\n🤖 Phase 5: Agent-based Filtering Tests")
        
        # Add memories with agent_id
        self.test_add_memory("test_user_charlie", "I like Italian food", "food_agent")
        self.test_add_memory("test_user_charlie", "I am allergic to peanuts", "health_agent")
        self.test_add_memory("test_user_charlie", "I live in New York")  # No agent
        
        time.sleep(2)
        
        # Test agent-specific retrieval
        self.test_get_all_memories("test_user_charlie", "food_agent")
        self.test_get_all_memories("test_user_charlie", "health_agent")
        self.test_get_all_memories("test_user_charlie")  # All agents
        
        # Phase 6: Graph-specific tests
        logger.info("\n🕸️ Phase 6: Graph-specific Tests")
        
        self.test_graph_entities()  # No user_id (test KeyError fix)
        self.test_graph_entities("test_user_alice")
        self.test_graph_relationships()  # No user_id (test KeyError fix)
        self.test_graph_relationships("test_user_alice")
        
        # Phase 7: Performance tests
        logger.info("\n⚡ Phase 7: Performance Tests")
        
        # Batch operations test
        start_time = time.time()
        for i in range(5):
            self.test_add_memory(f"perf_user_{i}", f"Performance test memory {i}")
        
        batch_time = time.time() - start_time
        self.log_test_result(
            "Batch Operations Performance",
            "PASS" if batch_time < 30 else "WARN",
            f"Added 5 memories in {batch_time:.2f}s",
            batch_time
        )
        
        # Cleanup performance test data
        for i in range(5):
            self.test_delete_all_memories(f"perf_user_{i}")
        
        # Phase 8: Cleanup
        logger.info("\n🧹 Phase 8: Cleanup")
        self.test_delete_all_memories("test_user_alice")
        self.test_delete_all_memories("test_user_bob")
        self.test_delete_all_memories("test_user_charlie")
        
        # Generate summary
        self.generate_test_summary()
    
    def generate_test_summary(self):
        """Generate and display test summary"""
        total_time = time.time() - self.start_time
        
        passed_tests = [r for r in self.test_results if r["status"] == "PASS"]
        failed_tests = [r for r in self.test_results if r["status"] == "FAIL"]
        warning_tests = [r for r in self.test_results if r["status"] == "WARN"]
        
        logger.info("\n" + "="*60)
        logger.info("📊 TEST SUMMARY")
        logger.info("="*60)
        logger.info(f"Total Tests: {len(self.test_results)}")
        logger.info(f"✅ Passed: {len(passed_tests)}")
        logger.info(f"❌ Failed: {len(failed_tests)}")
        logger.info(f"⚠️  Warnings: {len(warning_tests)}")
        logger.info(f"⏱️  Total Time: {total_time:.2f}s")
        logger.info(f"📈 Success Rate: {len(passed_tests)/len(self.test_results)*100:.1f}%")
        
        if failed_tests:
            logger.info("\n❌ FAILED TESTS:")
            for test in failed_tests:
                logger.info(f"   - {test['test_name']}: {test['details']}")
        
        if warning_tests:
            logger.info("\n⚠️  WARNING TESTS:")
            for test in warning_tests:
                logger.info(f"   - {test['test_name']}: {test['details']}")
        
        # Save detailed results
        with open('graph_memory_test_results.json', 'w') as f:
            json.dump({
                "summary": {
                    "total_tests": len(self.test_results),
                    "passed": len(passed_tests),
                    "failed": len(failed_tests),
                    "warnings": len(warning_tests),
                    "total_time": total_time,
                    "success_rate": len(passed_tests)/len(self.test_results)*100
                },
                "detailed_results": self.test_results
            }, f, indent=2)
        
        logger.info(f"\n📄 Detailed results saved to: graph_memory_test_results.json")


def main():
    """Main execution function"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:8000"
    
    logger.info(f"🔗 Testing Graph Memory API at: {base_url}")
    
    tester = GraphMemoryTester(base_url)
    tester.run_comprehensive_tests()
    
    logger.info("\n🎉 Graph Memory Test Suite Complete!")


if __name__ == "__main__":
    main()