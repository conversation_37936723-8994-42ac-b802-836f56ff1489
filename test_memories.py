#!/usr/bin/env python3
"""
Mem0 测试记忆数据生成器
添加30条不同类型的记忆用于前端UI测试
"""

import requests
import json
import time
import random
from datetime import datetime, timedelta

# API配置
API_BASE = "http://localhost:8000"
HEADERS = {"Content-Type": "application/json"}

def add_memory(messages, user_id, agent_id=None, run_id=None, category=None, enable_graph=False, metadata=None):
    """添加记忆的通用函数"""
    data = {
        "messages": messages,
        "user_id": user_id,
        "enable_graph": enable_graph
    }
    
    if agent_id:
        data["agent_id"] = agent_id
    if run_id:
        data["run_id"] = run_id
    if metadata:
        data["metadata"] = metadata
    
    try:
        response = requests.post(f"{API_BASE}/v1/memories/", json=data, headers=HEADERS)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功添加记忆: {messages[0]['content'][:50]}...")
            return result
        else:
            print(f"❌ 添加记忆失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def generate_test_memories():
    """生成30条不同类型的测试记忆"""
    
    print("🚀 开始生成测试记忆数据...")
    
    # 1. 个人信息类记忆 (5条)
    personal_memories = [
        {
            "messages": [{"role": "user", "content": "我的名字是张三，今年28岁，是一名软件工程师，住在北京市朝阳区"}],
            "user_id": "user_001",
            "metadata": {"category": "personal", "type": "basic_info", "priority": "high"}
        },
        {
            "messages": [{"role": "user", "content": "我最喜欢的编程语言是Python，有5年开发经验，擅长后端开发和机器学习"}],
            "user_id": "user_001", 
            "metadata": {"category": "personal", "type": "skills", "priority": "high"}
        },
        {
            "messages": [{"role": "user", "content": "我的生日是1995年3月15日，血型是O型，身高175cm，体重70kg"}],
            "user_id": "user_001",
            "metadata": {"category": "personal", "type": "physical_info", "priority": "medium"}
        },
        {
            "messages": [{"role": "user", "content": "我的爱好是摄影和旅行，去过日本、韩国、泰国，最想去的地方是冰岛"}],
            "user_id": "user_001",
            "metadata": {"category": "personal", "type": "interests", "priority": "medium"}
        },
        {
            "messages": [{"role": "user", "content": "我的手机号码是13800138000，邮箱是********************，微信号是zs_dev_2024"}],
            "user_id": "user_001",
            "metadata": {"category": "personal", "type": "contact", "priority": "high"}
        }
    ]
    
    # 2. 工作项目类记忆 (8条)
    work_memories = [
        {
            "messages": [{"role": "user", "content": "正在开发一个AI客服系统，使用FastAPI + Vue.js，预计3月份上线"}],
            "user_id": "user_001",
            "agent_id": "work_assistant",
            "metadata": {"category": "work", "type": "project", "status": "in_progress", "deadline": "2024-03-31"}
        },
        {
            "messages": [{"role": "user", "content": "团队有6个人：我负责后端，小李负责前端，小王负责UI设计，小赵负责测试，老板负责产品，小刘负责运维"}],
            "user_id": "user_001",
            "agent_id": "work_assistant", 
            "enable_graph": True,
            "metadata": {"category": "work", "type": "team", "priority": "high"}
        },
        {
            "messages": [{"role": "user", "content": "昨天的需求评审会议决定增加多语言支持功能，需要支持中文、英文、日文三种语言"}],
            "user_id": "user_001",
            "agent_id": "work_assistant",
            "metadata": {"category": "work", "type": "meeting", "date": "2024-01-29", "action_required": True}
        },
        {
            "messages": [{"role": "user", "content": "用户反馈系统响应速度慢，需要优化数据库查询，已创建JIRA-1234任务"}],
            "user_id": "user_001",
            "agent_id": "work_assistant",
            "metadata": {"category": "work", "type": "bug", "severity": "medium", "ticket": "JIRA-1234"}
        },
        {
            "messages": [{"role": "user", "content": "完成了Redis缓存优化，API响应时间从800ms降低到120ms，性能提升85%"}],
            "user_id": "user_001",
            "agent_id": "work_assistant",
            "metadata": {"category": "work", "type": "achievement", "performance_gain": "85%"}
        },
        {
            "messages": [{"role": "user", "content": "下周一要做技术分享：《FastAPI最佳实践》，已准备PPT和演示代码"}],
            "user_id": "user_001", 
            "agent_id": "work_assistant",
            "metadata": {"category": "work", "type": "presentation", "date": "2024-02-05", "status": "prepared"}
        },
        {
            "messages": [{"role": "user", "content": "客户ABC公司要求定制化功能：支持SSO登录和自定义主题，预算50万，工期2个月"}],
            "user_id": "user_001",
            "agent_id": "work_assistant", 
            "metadata": {"category": "work", "type": "client_request", "budget": 500000, "duration": "2_months"}
        },
        {
            "messages": [{"role": "user", "content": "服务器部署在阿里云，使用Docker+K8s，数据库是PostgreSQL，缓存用Redis"}],
            "user_id": "user_001",
            "agent_id": "work_assistant",
            "metadata": {"category": "work", "type": "infrastructure", "cloud": "aliyun", "tech_stack": ["Docker", "K8s", "PostgreSQL", "Redis"]}
        }
    ]
    
    # 3. 学习知识类记忆 (5条)
    learning_memories = [
        {
            "messages": [{"role": "user", "content": "学习了React Hooks：useState用于状态管理，useEffect用于副作用处理，useCallback用于性能优化"}],
            "user_id": "user_002",
            "agent_id": "learning_assistant",
            "metadata": {"category": "learning", "type": "programming", "topic": "React Hooks", "difficulty": "intermediate"}
        },
        {
            "messages": [{"role": "user", "content": "Docker的核心概念：镜像(Image)是只读模板，容器(Container)是镜像的运行实例，仓库(Registry)存储镜像"}],
            "user_id": "user_002",
            "agent_id": "learning_assistant",
            "metadata": {"category": "learning", "type": "devops", "topic": "Docker", "difficulty": "beginner"}
        },
        {
            "messages": [{"role": "user", "content": "机器学习算法分类：监督学习(分类、回归)、无监督学习(聚类、降维)、强化学习(Q-learning、策略梯度)"}],
            "user_id": "user_002", 
            "agent_id": "learning_assistant",
            "enable_graph": True,
            "metadata": {"category": "learning", "type": "ai", "topic": "ML算法", "difficulty": "advanced"}
        },
        {
            "messages": [{"role": "user", "content": "数据结构时间复杂度：数组查找O(n)，哈希表查找O(1)，二叉搜索树查找O(log n)，排序算法快排O(n log n)"}],
            "user_id": "user_002",
            "agent_id": "learning_assistant", 
            "metadata": {"category": "learning", "type": "algorithm", "topic": "时间复杂度", "difficulty": "intermediate"}
        },
        {
            "messages": [{"role": "user", "content": "设计模式：单例模式确保类只有一个实例，工厂模式创建对象，观察者模式定义一对多依赖关系"}],
            "user_id": "user_002",
            "agent_id": "learning_assistant",
            "metadata": {"category": "learning", "type": "design_pattern", "topic": "常用模式", "difficulty": "intermediate"}
        }
    ]
    
    # 4. 生活记录类记忆 (6条)
    life_memories = [
        {
            "messages": [{"role": "user", "content": "今天去了三里屯太古里购物，买了一件优衣库的毛衣299元，还在星巴克喝了杯咖啡42元"}],
            "user_id": "user_003",
            "metadata": {"category": "life", "type": "shopping", "location": "三里屯", "expense": 341, "date": "2024-01-30"}
        },
        {
            "messages": [{"role": "user", "content": "和朋友小美、小红一起看了电影《飞驰人生2》，票价58元，电影很搞笑，评分8.5分"}],
            "user_id": "user_003",
            "enable_graph": True,
            "metadata": {"category": "life", "type": "entertainment", "movie": "飞驰人生2", "rating": 8.5, "friends": ["小美", "小红"]}
        },
        {
            "messages": [{"role": "user", "content": "晚餐在海底捞吃火锅，点了毛肚、鸭血、土豆片，人均消费168元，服务很好"}],
            "user_id": "user_003",
            "metadata": {"category": "life", "type": "dining", "restaurant": "海底捞", "cuisine": "火锅", "cost_per_person": 168}
        },
        {
            "messages": [{"role": "user", "content": "每天早上7点起床，8点吃早餐，9点到公司，中午12点吃午饭，下午6点下班，10点睡觉"}],
            "user_id": "user_003", 
            "metadata": {"category": "life", "type": "routine", "schedule": "daily", "sleep_time": "22:00", "wake_time": "07:00"}
        },
        {
            "messages": [{"role": "user", "content": "周末计划：周六去健身房锻炼2小时，周日陪父母去公园散步，晚上准备下周工作"}],
            "user_id": "user_003",
            "metadata": {"category": "life", "type": "plan", "period": "weekend", "activities": ["健身", "陪父母", "工作准备"]}
        },
        {
            "messages": [{"role": "user", "content": "体检报告：身体健康，血压120/80，心率72，体重正常BMI 22.9，建议多运动少熬夜"}],
            "user_id": "user_003",
            "metadata": {"category": "life", "type": "health", "blood_pressure": "120/80", "heart_rate": 72, "bmi": 22.9, "status": "healthy"}
        }
    ]
    
    # 5. 关系网络类记忆 (4条) - 启用图形记忆
    relationship_memories = [
        {
            "messages": [{"role": "user", "content": "我的妻子李小红是医生，在协和医院工作，我们2020年结婚，她喜欢瑜伽和读书"}],
            "user_id": "user_004",
            "enable_graph": True,
            "metadata": {"category": "relationship", "type": "family", "relation": "spouse", "profession": "doctor"}
        },
        {
            "messages": [{"role": "user", "content": "我的同事王大明是前端开发工程师，擅长Vue.js和React，我们经常一起讨论技术问题"}],
            "user_id": "user_004",
            "enable_graph": True, 
            "metadata": {"category": "relationship", "type": "colleague", "profession": "frontend_developer", "skills": ["Vue.js", "React"]}
        },
        {
            "messages": [{"role": "user", "content": "我的大学室友张伟现在在腾讯做产品经理，负责微信小程序，我们每月都会聚会"}],
            "user_id": "user_004",
            "enable_graph": True,
            "metadata": {"category": "relationship", "type": "friend", "company": "腾讯", "position": "产品经理", "contact_frequency": "monthly"}
        },
        {
            "messages": [{"role": "user", "content": "我的老板刘总是技术出身，创业15年，公司有200人，对技术要求很高但人很好"}],
            "user_id": "user_004", 
            "enable_graph": True,
            "metadata": {"category": "relationship", "type": "boss", "background": "技术", "company_size": 200, "leadership_style": "high_tech_requirement"}
        }
    ]
    
    # 6. 复杂对话类记忆 (2条)
    complex_memories = [
        {
            "messages": [
                {"role": "user", "content": "我想做一个电商网站，需要什么技术栈？"},
                {"role": "assistant", "content": "建议使用：前端React/Vue.js，后端Node.js/Python FastAPI，数据库MySQL/PostgreSQL，缓存Redis，搜索ElasticSearch"},
                {"role": "user", "content": "用户量预计10万，需要考虑什么？"},
                {"role": "assistant", "content": "需要考虑：负载均衡、数据库分库分表、CDN加速、监控报警、自动扩容等"},
                {"role": "user", "content": "预算大概多少？"},
                {"role": "assistant", "content": "初期开发成本50-100万，服务器月成本5-10万，根据业务增长调整"}
            ],
            "user_id": "user_005",
            "agent_id": "tech_consultant", 
            "metadata": {"category": "consultation", "type": "complex_dialog", "topic": "电商网站", "estimated_users": 100000}
        }
    ]
    
    # 收集所有记忆
    all_memories = (
        personal_memories + 
        work_memories + 
        learning_memories + 
        life_memories + 
        relationship_memories + 
        complex_memories
    )
    
    print(f"📊 总共准备了 {len(all_memories)} 条记忆")
    
    # 批量添加记忆
    success_count = 0
    for i, memory in enumerate(all_memories, 1):
        print(f"\n[{i}/{len(all_memories)}] 添加记忆...")
        result = add_memory(**memory)
        if result:
            success_count += 1
        # 添加小延迟避免请求过快
        time.sleep(0.5)
    
    print(f"\n🎉 完成！成功添加 {success_count}/{len(all_memories)} 条记忆")
    
    # 统计信息
    categories = {}
    for memory in all_memories:
        category = memory.get("metadata", {}).get("category", "unknown")
        categories[category] = categories.get(category, 0) + 1
    
    print("\n📈 记忆分类统计:")
    for category, count in categories.items():
        print(f"  • {category}: {count} 条")
    
    print(f"\n🔗 启用图形记忆的记忆: {sum(1 for m in all_memories if m.get('enable_graph', False))} 条")
    print(f"👥 涉及用户数: {len(set(m['user_id'] for m in all_memories))} 个")
    print(f"🤖 涉及代理数: {len(set(m.get('agent_id') for m in all_memories if m.get('agent_id')))} 个")

def main():
    """主函数"""
    print("=" * 60)
    print("🧠 Mem0 测试记忆数据生成器")
    print("=" * 60)
    
    # 检查API连接
    try:
        response = requests.get(f"{API_BASE}/health")
        if response.status_code == 200:
            print("✅ API连接正常")
        else:
            print("❌ API连接失败")
            return
    except Exception as e:
        print(f"❌ 无法连接到API: {e}")
        return
    
    # 生成测试数据
    generate_test_memories()
    
    print("\n🎯 测试数据生成完成！现在可以在前端UI中查看这些记忆了。")
    print("📱 前端UI地址: http://localhost:3000 (如果已启动)")
    print("📚 API文档: http://localhost:8000/docs")

if __name__ == "__main__":
    main()