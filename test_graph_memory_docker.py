#!/usr/bin/env python3
"""
图记忆功能实际测试
Real Graph Memory Functionality Test

测试Mem0的图记忆功能在当前Docker环境中的实际工作情况
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, '/opt/mem0ai')

def test_graph_memory_integration():
    """测试图记忆集成功能"""
    print("=" * 60)
    print("Mem0 图记忆功能集成测试")
    print(f"测试时间: {datetime.now()}")
    print("=" * 60)
    
    try:
        from mem0 import Memory
        
        # 配置图记忆
        config = {
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "temperature": 0.2,
                }
            },
            "graph_store": {
                "provider": "neo4j",
                "config": {
                    "url": "bolt://localhost:7687",  # Docker内部连接
                    "username": "neo4j",
                    "password": "mem0graph"
                }
            }
        }
        
        print("✓ 1. 创建配置成功")
        
        # 检查是否有OpenAI API密钥
        if not os.getenv('OPENAI_API_KEY'):
            print("⚠️  警告: 未设置 OPENAI_API_KEY，将使用模拟模式")
            # 使用本地LLM或跳过需要LLM的测试
            config["llm"]["provider"] = "ollama"
            config["llm"]["config"] = {"model": "llama2", "base_url": "http://localhost:11434"}
        
        try:
            memory = Memory.from_config(config_dict=config)
            print("✓ 2. Memory实例创建成功")
        except Exception as e:
            print(f"✗ 2. Memory实例创建失败: {e}")
            return False
        
        # 测试基本的图操作（不需要LLM）
        try:
            # 检查图记忆是否启用
            if hasattr(memory, 'graph') and memory.graph:
                print("✓ 3. 图记忆模块已启用")
                
                # 测试Neo4j连接
                try:
                    # 简单的连接测试
                    test_query = "RETURN 'connection_test' as result"
                    result = memory.graph.graph.query(test_query)
                    if result and result[0]['result'] == 'connection_test':
                        print("✓ 4. Neo4j数据库连接正常")
                    else:
                        print("✗ 4. Neo4j连接测试失败")
                except Exception as e:
                    print(f"✗ 4. Neo4j连接测试异常: {e}")
                    
            else:
                print("✗ 3. 图记忆模块未启用")
                return False
                
        except Exception as e:
            print(f"✗ 3. 图记忆模块检查失败: {e}")
            return False
        
        # 测试简单的图操作（模拟数据）
        try:
            # 创建一些测试节点（手动创建，避免LLM依赖）
            test_user_id = "test_user_graph_memory"
            
            # 清理可能存在的测试数据
            cleanup_query = f"""
            MATCH (n:__Entity__ {{user_id: '{test_user_id}'}})
            DETACH DELETE n
            """
            memory.graph.graph.query(cleanup_query)
            print("✓ 5. 测试环境清理完成")
            
            # 手动创建测试图结构
            create_nodes_query = f"""
            CREATE (alice:__Entity__ {{name: 'alice', user_id: '{test_user_id}', entity_type: 'person'}})
            CREATE (bob:__Entity__ {{name: 'bob', user_id: '{test_user_id}', entity_type: 'person'}})
            CREATE (pizza:__Entity__ {{name: 'pizza', user_id: '{test_user_id}', entity_type: 'food'}})
            CREATE (alice)-[:KNOWS]->(bob)
            CREATE (alice)-[:LIKES]->(pizza)
            CREATE (bob)-[:LIKES]->(pizza)
            RETURN count(*) as nodes_created
            """
            result = memory.graph.graph.query(create_nodes_query)
            print("✓ 6. 测试图结构创建成功")
            
            # 测试图查询
            search_query = f"""
            MATCH (n:__Entity__ {{user_id: '{test_user_id}'}})-[r]->(m:__Entity__ {{user_id: '{test_user_id}'}})
            RETURN n.name as source, type(r) as relationship, m.name as target
            """
            relationships = memory.graph.graph.query(search_query)
            
            if relationships:
                print(f"✓ 7. 图查询成功，找到 {len(relationships)} 个关系:")
                for rel in relationships:
                    print(f"     {rel['source']} --{rel['relationship']}--> {rel['target']}")
            else:
                print("✗ 7. 图查询未返回结果")
            
            # 测试图记忆的get_all方法
            try:
                filters = {"user_id": test_user_id}
                all_relations = memory.graph.get_all(filters, limit=10)
                
                if all_relations:
                    print(f"✓ 8. get_all方法成功，返回 {len(all_relations)} 个关系")
                    for rel in all_relations:
                        print(f"     {rel['source']} --{rel['relationship']}--> {rel['target']}")
                else:
                    print("✗ 8. get_all方法未返回结果")
            except Exception as e:
                print(f"✗ 8. get_all方法测试失败: {e}")
            
            # 清理测试数据
            memory.graph.graph.query(cleanup_query)
            print("✓ 9. 测试数据清理完成")
            
        except Exception as e:
            print(f"✗ 图操作测试失败: {e}")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 图记忆功能集成测试通过！")
        print("   - Neo4j连接正常")
        print("   - 图记忆模块已启用") 
        print("   - 基本图操作功能正常")
        print("   - 数据存储和查询功能正常")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_graph_memory_config_validation():
    """测试图记忆配置验证"""
    print("\n测试配置验证...")
    
    from mem0.graphs.configs import GraphStoreConfig, Neo4jConfig
    
    # 测试正确配置
    try:
        config = GraphStoreConfig(
            provider="neo4j",
            config=Neo4jConfig(
                url="bolt://localhost:7687",
                username="neo4j",
                password="mem0graph"
            )
        )
        print("✓ 正确配置验证通过")
    except Exception as e:
        print(f"✗ 正确配置验证失败: {e}")
    
    # 测试错误配置
    try:
        bad_config = GraphStoreConfig(
            provider="neo4j",
            config=Neo4jConfig(
                url="",  # 空URL应该失败
                username="neo4j",
                password="mem0graph"
            )
        )
        print("✗ 错误配置验证应该失败但通过了")
    except Exception as e:
        print("✓ 错误配置正确被拒绝")

def test_docker_environment():
    """测试Docker环境"""
    print("\n测试Docker环境...")
    
    # 检查是否在Docker中
    if os.path.exists('/.dockerenv'):
        print("✓ 运行在Docker容器中")
    else:
        print("⚠️  不在Docker容器中运行")
    
    # 检查Neo4j连接
    try:
        from neo4j import GraphDatabase
        driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', 'mem0graph'))
        with driver.session() as session:
            result = session.run('RETURN "Docker Neo4j Test" as message')
            record = result.single()
            if record:
                print("✓ Docker Neo4j连接正常")
            else:
                print("✗ Docker Neo4j连接异常")
        driver.close()
    except Exception as e:
        print(f"✗ Docker Neo4j连接失败: {e}")

if __name__ == "__main__":
    success = True
    
    # 运行各项测试
    test_docker_environment()
    test_graph_memory_config_validation()
    success = test_graph_memory_integration() and success
    
    if success:
        print("\n🎉 所有测试通过！图记忆功能在当前环境中工作正常。")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败。图记忆功能可能存在问题。")
        sys.exit(1)