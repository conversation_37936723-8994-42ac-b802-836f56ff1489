#!/usr/bin/env node

/**
 * 验证后端API数据与前端数据转换的一致性
 * 模拟前端数据转换过程，检查字段映射是否正确
 */

const https = require('http');

// 模拟前端数据转换逻辑
function simulateClientTransformation(rawApiResponse) {
  // 模拟 realClient.ts 中的转换逻辑
  const rawData = rawApiResponse;
  return {
    entities: rawData.entities || [],
    relations: rawData.relationships || [], // 后端返回relationships，转换为relations
    metadata: {
      total_entities: rawData.total_entities || 0,
      total_relations: rawData.total_relationships || 0, // 后端返回total_relationships，转换为total_relations
      ...rawData.metadata
    }
  };
}

function simulateDataTransformer(graphData) {
  console.log('🔍 [模拟数据转换器] 输入数据结构验证:');
  
  // 验证实体数据结构
  const entities = graphData?.entities || [];
  console.log(`  实体数量: ${entities.length}`);
  
  if (entities.length > 0) {
    const sampleEntity = entities[0];
    console.log('  示例实体结构:', {
      id: sampleEntity.id,
      label: sampleEntity.label, // 后端使用label
      name: sampleEntity.name,   // 前端期望name
      type: sampleEntity.type,
      hasProperties: !!sampleEntity.properties
    });
  }
  
  // 验证关系数据结构
  const relations = graphData?.relations || [];
  console.log(`  关系数量: ${relations.length}`);
  
  if (relations.length > 0) {
    const sampleRelation = relations[0];
    console.log('  示例关系结构:', {
      id: sampleRelation.id,
      source_entity: sampleRelation.source_entity,     // 后端使用
      source_entity_id: sampleRelation.source_entity_id, // 前端期望
      target_entity: sampleRelation.target_entity,     // 后端使用
      target_entity_id: sampleRelation.target_entity_id, // 前端期望
      relationship_type: sampleRelation.relationship_type, // 后端使用
      relation_type: sampleRelation.relation_type         // 前端期望
    });
  }
  
  return {
    nodes: entities.map((entity, index) => ({
      id: entity.id,
      type: 'default',
      position: { x: Math.random() * 500, y: Math.random() * 500 },
      data: {
        id: entity.id,
        label: entity.label || entity.name || entity.id, // 字段映射
        type: entity.type?.toLowerCase() || 'default',
        description: entity.description || '',
        properties: entity.properties || {},
        metadata: entity.metadata || {}
      }
    })),
    edges: relations.map(relation => ({
      id: relation.id,
      source: relation.source_entity || relation.source_entity_id, // 字段映射
      target: relation.target_entity || relation.target_entity_id,  // 字段映射
      type: 'default',
      data: {
        id: relation.id,
        label: relation.relationship_type || relation.relation_type || 'related_to', // 字段映射
        relation_type: relation.relationship_type || relation.relation_type || 'related_to',
        properties: relation.properties || {}
      }
    }))
  };
}

async function fetchGraphData() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8000,
      path: '/v1/graph/memories?user_id=test_bob&limit=10',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function main() {
  try {
    console.log('🚀 开始验证后端数据与前端数据转换的一致性...\n');
    
    // 1. 获取后端原始数据
    console.log('📡 步骤1: 获取后端API原始响应');
    const rawApiResponse = await fetchGraphData();
    
    console.log('✅ 后端API响应结构:');
    console.log(`  entities: ${rawApiResponse.entities?.length || 0} 个`);
    console.log(`  relationships: ${rawApiResponse.relationships?.length || 0} 个`);
    console.log(`  total_entities: ${rawApiResponse.total_entities}`);
    console.log(`  total_relationships: ${rawApiResponse.total_relationships}`);
    
    // 2. 模拟前端客户端转换
    console.log('\n🔄 步骤2: 模拟前端客户端转换 (realClient.ts)');
    const clientTransformed = simulateClientTransformation(rawApiResponse);
    
    console.log('✅ 客户端转换后结构:');
    console.log(`  entities: ${clientTransformed.entities?.length || 0} 个`);
    console.log(`  relations: ${clientTransformed.relations?.length || 0} 个 (字段名已转换)`);
    console.log(`  total_entities: ${clientTransformed.metadata.total_entities}`);
    console.log(`  total_relations: ${clientTransformed.metadata.total_relations} (字段名已转换)`);
    
    // 3. 模拟数据转换器
    console.log('\n🎯 步骤3: 模拟数据转换器 (graph-data-transformer.ts)');
    const transformedData = simulateDataTransformer(clientTransformed);
    
    console.log('✅ 转换器输出结构:');
    console.log(`  nodes: ${transformedData.nodes?.length || 0} 个`);
    console.log(`  edges: ${transformedData.edges?.length || 0} 个`);
    
    // 4. 验证数据一致性
    console.log('\n🔍 步骤4: 数据一致性验证');
    
    const isConsistent = (
      rawApiResponse.entities.length === transformedData.nodes.length &&
      rawApiResponse.relationships.length === transformedData.edges.length &&
      rawApiResponse.total_entities === clientTransformed.metadata.total_entities &&
      rawApiResponse.total_relationships === clientTransformed.metadata.total_relations
    );
    
    if (isConsistent) {
      console.log('✅ 数据一致性验证通过！');
      console.log('   - 实体数量匹配');
      console.log('   - 关系数量匹配');
      console.log('   - 统计数据匹配');
    } else {
      console.log('❌ 数据一致性验证失败');
      console.log('   后端实体数:', rawApiResponse.entities.length);
      console.log('   前端节点数:', transformedData.nodes.length);
      console.log('   后端关系数:', rawApiResponse.relationships.length);
      console.log('   前端边数:', transformedData.edges.length);
    }
    
    // 5. 详细字段映射验证
    console.log('\n🔬 步骤5: 字段映射验证');
    
    if (rawApiResponse.entities.length > 0 && transformedData.nodes.length > 0) {
      const backendEntity = rawApiResponse.entities[0];
      const frontendNode = transformedData.nodes[0];
      
      console.log('实体字段映射验证:');
      console.log(`  后端 label: "${backendEntity.label}" -> 前端 label: "${frontendNode.data.label}"`);
      console.log(`  字段映射: ${backendEntity.label === frontendNode.data.label ? '✅' : '❌'}`);
    }
    
    if (rawApiResponse.relationships.length > 0 && transformedData.edges.length > 0) {
      const backendRelation = rawApiResponse.relationships[0];
      const frontendEdge = transformedData.edges[0];
      
      console.log('关系字段映射验证:');
      console.log(`  后端 source_entity: "${backendRelation.source_entity}" -> 前端 source: "${frontendEdge.source}"`);
      console.log(`  后端 relationship_type: "${backendRelation.relationship_type}" -> 前端 label: "${frontendEdge.data.label}"`);
      console.log(`  源节点映射: ${backendRelation.source_entity === frontendEdge.source ? '✅' : '❌'}`);
      console.log(`  关系类型映射: ${backendRelation.relationship_type === frontendEdge.data.label ? '✅' : '❌'}`);
    }
    
    console.log('\n🎉 验证完成！');
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行验证
main();