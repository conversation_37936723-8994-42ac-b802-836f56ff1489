#!/usr/bin/env python3
"""
Test script for Categories automatic classification functionality
"""

import json
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mem0.configs.prompts import FACT_RETRIEVAL_PROMPT

def test_prompt_structure():
    """Test if the FACT_RETRIEVAL_PROMPT contains Categories logic"""
    print("=== 测试FACT_RETRIEVAL_PROMPT结构 ===")
    
    # Check if prompt contains categories instructions
    required_keywords = [
        "Available Categories",
        "categories",
        "personal_details",
        "family",
        "professional_details"
    ]
    
    found_keywords = []
    for keyword in required_keywords:
        if keyword in FACT_RETRIEVAL_PROMPT:
            found_keywords.append(keyword)
    
    print(f"发现关键词: {found_keywords}")
    print(f"缺失关键词: {set(required_keywords) - set(found_keywords)}")
    
    # Check output format
    if '{"facts"' in FACT_RETRIEVAL_PROMPT and '"categories"' in FACT_RETRIEVAL_PROMPT:
        print("✓ 输出格式包含facts和categories")
    else:
        print("✗ 输出格式不正确")
    
    return len(found_keywords) == len(required_keywords)

def test_example_parsing():
    """Test if we can parse the example from the prompt"""
    print("\n=== 测试示例解析 ===")
    
    # Extract example from prompt
    example_output = '{"facts" : ["Name is John", "Is a Software engineer"], "categories": ["personal_details", "professional_details"]}'
    
    try:
        parsed = json.loads(example_output)
        print(f"✓ 示例JSON解析成功: {parsed}")
        
        if "facts" in parsed and "categories" in parsed:
            print("✓ 包含所需字段")
            return True
        else:
            print("✗ 缺少必需字段")
            return False
    except json.JSONDecodeError as e:
        print(f"✗ JSON解析失败: {e}")
        return False

def main():
    """Run all tests"""
    print("Categories自动分类功能测试")
    print("="*50)
    
    test1_passed = test_prompt_structure()
    test2_passed = test_example_parsing()
    
    print("\n=== 测试结果 ===")
    print(f"Prompt结构测试: {'通过' if test1_passed else '失败'}")
    print(f"示例解析测试: {'通过' if test2_passed else '失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！Categories自动分类功能已就绪")
        return 0
    else:
        print("\n❌ 部分测试失败，需要检查实现")
        return 1

if __name__ == "__main__":
    exit(main())