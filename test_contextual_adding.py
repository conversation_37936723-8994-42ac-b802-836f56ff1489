#!/usr/bin/env python3
"""
测试Mem0上下文添加功能（version=v2）
测试自动历史检索和合并功能
"""

import requests
import json
import time
import uuid

BASE_URL = "http://localhost:8000"

def setup_initial_memories(user_id):
    """设置初始记忆数据以便测试上下文添加"""
    print(f"为用户 {user_id} 设置初始记忆...")
    
    initial_memories = [
        "I work as a software engineer at Microsoft",
        "I love playing tennis on weekends",
        "My favorite programming language is Python",
        "I have a cat named <PERSON><PERSON><PERSON>",
        "I live in Seattle, Washington"
    ]
    
    created_count = 0
    for memory_text in initial_memories:
        try:
            response = requests.post(
                f"{BASE_URL}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": memory_text}],
                    "user_id": user_id
                },
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                created_count += len(result)
            
            time.sleep(0.5)
            
        except Exception as e:
            print(f"创建初始记忆失败: {e}")
    
    print(f"成功创建 {created_count} 条初始记忆")
    return created_count

def test_contextual_adding_v1(user_id):
    """测试版本1（非上下文）添加 - 作为对比基准"""
    print("\n=== 测试V1添加（非上下文）===")
    
    try:
        # 添加相关信息但不使用上下文
        new_info = "I recently got promoted to Senior Software Engineer"
        
        response = requests.post(
            f"{BASE_URL}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": new_info}],
                "user_id": user_id
            },
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ V1添加成功: {len(result)} 条新记忆")
            
            for memory in result:
                print(f"  - {memory.get('memory', '')}")
                
        else:
            print(f"❌ V1添加失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ V1添加异常: {e}")

def test_contextual_adding_v2(user_id):
    """测试版本2（上下文）添加"""
    print("\n=== 测试V2添加（上下文感知）===")
    
    try:
        # 添加相关信息，使用上下文版本
        contextual_info = "I'm now working on machine learning projects at my current company"
        
        response = requests.post(
            f"{BASE_URL}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": contextual_info}],
                "user_id": user_id,
                "version": "v2"  # 启用上下文添加
            },
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ V2添加成功: {len(result)} 条记忆")
            
            for memory in result:
                print(f"  - {memory.get('memory', '')}")
                
        else:
            print(f"❌ V2添加失败: {response.status_code}")
            print(f"错误详情: {response.text}")
            
    except Exception as e:
        print(f"❌ V2添加异常: {e}")

def test_contextual_update_scenario(user_id):
    """测试上下文更新场景"""
    print("\n=== 测试上下文更新场景 ===")
    
    try:
        # 添加可能需要更新的信息
        update_info = "I moved to Portland, Oregon last month"
        
        response = requests.post(
            f"{BASE_URL}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": update_info}],
                "user_id": user_id,
                "version": "v2"
            },
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 上下文更新成功: {len(result)} 条记忆")
            
            # 检查是否检测到了位置变化
            for memory in result:
                action = memory.get('event', '')
                print(f"  - [{action}] {memory.get('memory', '')}")
                
        else:
            print(f"❌ 上下文更新失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 上下文更新异常: {e}")

def test_contextual_conflict_resolution(user_id):
    """测试上下文冲突解决"""
    print("\n=== 测试上下文冲突解决 ===")
    
    try:
        # 添加冲突信息
        conflict_info = "Actually, I prefer Java over Python for most projects now"
        
        response = requests.post(
            f"{BASE_URL}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": conflict_info}],
                "user_id": user_id,
                "version": "v2"
            },
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 冲突解决成功: {len(result)} 条记忆")
            
            # 分析操作类型
            operations = {}
            for memory in result:
                action = memory.get('event', 'UNKNOWN')
                operations[action] = operations.get(action, 0) + 1
                print(f"  - [{action}] {memory.get('memory', '')}")
            
            print(f"\n操作统计: {operations}")
            
            if 'UPDATE' in operations:
                print("✅ 检测到UPDATE操作，冲突解决正常")
            else:
                print("❓ 未检测到UPDATE操作，可能只是添加了新信息")
                
        else:
            print(f"❌ 冲突解决失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 冲突解决异常: {e}")

def test_complex_contextual_scenario(user_id):
    """测试复杂上下文场景"""
    print("\n=== 测试复杂上下文场景 ===")
    
    try:
        # 添加复杂的、可能涉及多个记忆的信息
        complex_info = "I got a new job at Google as a Machine Learning Engineer, so I'll be moving back to California and will have less time for tennis"
        
        response = requests.post(
            f"{BASE_URL}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": complex_info}],
                "user_id": user_id,
                "version": "v2"
            },
            timeout=20  # 更长的超时时间，因为需要处理更多上下文
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 复杂场景处理成功: {len(result)} 条记忆")
            
            # 详细分析操作
            operations = {}
            for memory in result:
                action = memory.get('event', 'UNKNOWN')
                operations[action] = operations.get(action, 0) + 1
                print(f"  - [{action}] {memory.get('memory', '')}")
            
            print(f"\n操作统计: {operations}")
            
            # 检查是否同时处理了工作、地理位置和爱好信息
            total_changes = sum(operations.values())
            if total_changes >= 2:
                print("✅ 复杂上下文处理良好，检测到多个相关变化")
            else:
                print("❓ 复杂上下文处理可能不够全面")
                
        else:
            print(f"❌ 复杂场景处理失败: {response.status_code}")
            print(f"错误详情: {response.text}")
            
    except Exception as e:
        print(f"❌ 复杂场景处理异常: {e}")

def verify_final_memories(user_id):
    """验证最终的记忆状态"""
    print("\n=== 验证最终记忆状态 ===")
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/memories/search/",
            json={
                "query": "tell me about yourself",
                "user_id": user_id,
                "limit": 20
            },
            timeout=15
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"最终共有 {len(results)} 条记忆:")
            
            for i, memory in enumerate(results[:10], 1):  # 显示前10条
                print(f"  {i}. {memory.get('memory', '')}")
                
            if len(results) > 10:
                print(f"  ... 还有 {len(results) - 10} 条记忆")
                
        else:
            print(f"❌ 验证失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 验证异常: {e}")

def test_contextual_adding():
    """主测试函数"""
    print("🔄 测试Mem0上下文添加功能（V2版本）")
    print("=" * 60)
    
    test_user = f"contextual_test_{str(uuid.uuid4())[:8]}"
    
    # 1. 设置初始记忆
    print("\n=== 1. 设置初始记忆 ===")
    initial_count = setup_initial_memories(test_user)
    
    if initial_count == 0:
        print("❌ 无法创建初始记忆，跳过上下文测试")
        return
    
    # 等待初始记忆处理完成
    print("等待初始记忆处理完成...")
    time.sleep(3)
    
    # 2. 测试V1添加（作为对比）
    test_contextual_adding_v1(test_user)
    time.sleep(2)
    
    # 3. 测试V2上下文添加
    test_contextual_adding_v2(test_user)
    time.sleep(2)
    
    # 4. 测试上下文更新
    test_contextual_update_scenario(test_user)
    time.sleep(2)
    
    # 5. 测试冲突解决
    test_contextual_conflict_resolution(test_user)
    time.sleep(2)
    
    # 6. 测试复杂场景
    test_complex_contextual_scenario(test_user)
    time.sleep(2)
    
    # 7. 验证最终状态
    verify_final_memories(test_user)
    
    print("\n" + "=" * 60)
    print("🎯 上下文添加功能测试完成!")

if __name__ == "__main__":
    test_contextual_adding()