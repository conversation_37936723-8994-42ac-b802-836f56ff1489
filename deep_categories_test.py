#!/usr/bin/env python3
"""
深入测试Categories功能
"""

import requests
import json

API_BASE = "http://localhost:8000"

def comprehensive_categories_test():
    print("🔬 深入测试Categories功能")
    print("=" * 60)
    
    # 1. 测试自动Categories生成
    print("\n=== 1. 测试自动Categories生成 ===")
    auto_test_cases = [
        {"content": "My name is <PERSON> and I work at Google as a data scientist.", "expected": ["personal_details", "professional_details"]},
        {"content": "I love playing soccer and tennis every weekend.", "expected": ["sports"]},
        {"content": "Last month I traveled to Tokyo and Kyoto. Amazing experience!", "expected": ["travel"]},
        {"content": "I eat vegan food and practice yoga for health.", "expected": ["health", "food"]},
        {"content": "My favorite band is Coldplay and I enjoy reading fiction books.", "expected": ["entertainment"]},
    ]
    
    for i, case in enumerate(auto_test_cases):
        try:
            response = requests.post(
                f"{API_BASE}/v1/memories/",
                json={
                    "messages": [{"role": "user", "content": case["content"]}],
                    "user_id": f"auto_cat_test_{i}"
                },
                timeout=20
            )
            
            if response.status_code == 200:
                # 获取用户记忆查看categories
                user_response = requests.get(
                    f"{API_BASE}/v1/memories/",
                    params={"user_id": f"auto_cat_test_{i}", "limit": 10}
                )
                
                if user_response.status_code == 200:
                    memories = user_response.json()
                    if memories:
                        all_categories = set()
                        for mem in memories:
                            categories = mem.get('metadata', {}).get('categories', [])
                            all_categories.update(categories)
                        
                        print(f"✓ 测试 {i+1}: 生成categories {list(all_categories)}")
                        
                        # 检查是否包含预期分类
                        expected_found = [cat for cat in case["expected"] if cat in all_categories]
                        if expected_found:
                            print(f"  匹配预期: {expected_found}")
                        else:
                            print(f"  ⚠️ 未匹配预期 {case['expected']}")
                    else:
                        print(f"✗ 测试 {i+1}: 无记忆返回")
                else:
                    print(f"✗ 测试 {i+1}: 无法获取记忆列表")
            else:
                print(f"✗ 测试 {i+1}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"✗ 测试 {i+1}: 异常 {e}")
            
        time.sleep(1)
    
    # 2. 测试自定义Categories（正确格式）
    print(f"\n=== 2. 测试自定义Categories（字典格式） ===")
    try:
        response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": "I received my Python certification from Coursera."}],
                "user_id": "custom_dict_test",
                "custom_categories": [{"name": "education"}, {"name": "certification"}, {"name": "programming"}]
            },
            timeout=20
        )
        
        if response.status_code == 200:
            user_response = requests.get(
                f"{API_BASE}/v1/memories/",
                params={"user_id": "custom_dict_test", "limit": 10}
            )
            
            if user_response.status_code == 200:
                memories = user_response.json()
                if memories:
                    categories = memories[0].get('metadata', {}).get('categories', [])
                    print(f"✓ 自定义Categories（字典格式）: {categories}")
                    
                    # 检查是否使用了自定义分类
                    custom_cats = ["education", "certification", "programming"]
                    used_custom = [cat for cat in custom_cats if cat in categories]
                    if used_custom:
                        print(f"  ✓ 使用了自定义分类: {used_custom}")
                    else:
                        print(f"  ⚠️ 未使用自定义分类，使用了: {categories}")
                else:
                    print("✗ 无记忆返回")
            else:
                print("✗ 无法获取记忆列表")
        else:
            print(f"✗ HTTP {response.status_code}: {response.text[:200]}")
            
    except Exception as e:
        print(f"✗ 异常: {e}")
    
    # 3. 对比测试：同样内容，一个自动一个自定义
    print(f"\n=== 3. 对比测试：自动 vs 自定义Categories ===")
    test_content = "I learned machine learning and got a certificate from MIT."
    
    # 自动分类
    try:
        auto_response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": test_content}],
                "user_id": "compare_auto"
            },
            timeout=15
        )
        
        if auto_response.status_code == 200:
            auto_memories = requests.get(f"{API_BASE}/v1/memories/?user_id=compare_auto&limit=10").json()
            if auto_memories:
                auto_categories = auto_memories[0].get('metadata', {}).get('categories', [])
                print(f"自动分类结果: {auto_categories}")
            else:
                print("自动分类: 无结果")
        else:
            print(f"自动分类失败: HTTP {auto_response.status_code}")
            
    except Exception as e:
        print(f"自动分类异常: {e}")
    
    # 自定义分类
    try:
        custom_response = requests.post(
            f"{API_BASE}/v1/memories/",
            json={
                "messages": [{"role": "user", "content": test_content}],
                "user_id": "compare_custom",
                "custom_categories": [{"name": "learning"}, {"name": "certificate"}, {"name": "AI"}]
            },
            timeout=15
        )
        
        if custom_response.status_code == 200:
            custom_memories = requests.get(f"{API_BASE}/v1/memories/?user_id=compare_custom&limit=10").json()
            if custom_memories:
                custom_categories = custom_memories[0].get('metadata', {}).get('categories', [])
                print(f"自定义分类结果: {custom_categories}")
            else:
                print("自定义分类: 无结果")
        else:
            print(f"自定义分类失败: HTTP {custom_response.status_code}")
            
    except Exception as e:
        print(f"自定义分类异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Categories深入测试完成!")

if __name__ == "__main__":
    import time
    comprehensive_categories_test()