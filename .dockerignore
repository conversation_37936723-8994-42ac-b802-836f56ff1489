# Docker 构建优化 - 忽略不必要的文件
# 这将显著减少构建上下文大小，提高构建速度

# 数据目录
data/
*/data/
.mem0/
*/‍.mem0/

# 缓存和临时文件
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.cache/
.pytest_cache/
*.egg-info/
build/
dist/
.tox/
.coverage
htmlcov/

# 版本控制
.git/
.gitignore
.github/

# 开发工具配置
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# OS生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js相关（UI部分）
mem0_ui/node_modules/
mem0_ui/.next/
mem0_ui/out/
mem0_ui/build/
mem0_ui/.env.local
mem0_ui/.env.development.local
mem0_ui/.env.test.local
mem0_ui/.env.production.local
mem0_ui/npm-debug.log*
mem0_ui/yarn-debug.log*
mem0_ui/yarn-error.log*
mem0_ui/package-lock.json
mem0_ui/yarn.lock
mem0_ui/tsconfig.tsbuildinfo

# openmemory UI相关
openmemory/ui/node_modules/
openmemory/ui/.next/
openmemory/ui/out/
openmemory/ui/build/
openmemory/ui/.env.local
openmemory/ui/.env.development.local
openmemory/ui/.env.test.local
openmemory/ui/.env.production.local
openmemory/ui/npm-debug.log*
openmemory/ui/yarn-debug.log*
openmemory/ui/yarn-error.log*
openmemory/ui/pnpm-lock.yaml
openmemory/ui/tsconfig.tsbuildinfo

# 文档
docs/
README.md
*.md
CHANGELOG.md
LICENSE

# 测试相关
tests/
test/
*.test.js
*.spec.js
coverage/

# 环境配置文件（保留.env.example）
.env
.env.local
.env.production

# 备份文件
*.bak
*.backup
*.orig

# 工具配置文件
Makefile
.editorconfig
.pylintrc
pyproject.toml
setup.py
setup.cfg

# Docker相关
Dockerfile*
docker-compose*.yml
docker-compose*.yaml
.dockerignore

# 部署脚本
deploy*.sh
setup*.sh

# 其他大文件目录
examples/
tutorials/
benchmarks/